<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* emails/password_reset.html.twig */
class __TwigTemplate_d37b54ef1a37dfac4b77a08e89a175bf extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "emails/password_reset.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "emails/password_reset.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Password Reset Code</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Email-safe styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            padding: 40px 30px;
            text-align: center;
            color: #ffffff;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 50%;
        }

        .logo-fallback {
            font-size: 2rem;
            font-weight: bold;
            color: #011a2d;
            display: none;
        }

        .email-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .email-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        .email-body {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #011a2d;
            margin-bottom: 20px;
        }

        .message {
            font-size: 16px;
            color: #343a40;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .code-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #011a2d;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }

        .code-label {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px;
        }

        .verification-code {
            font-size: 36px;
            font-weight: 800;
            color: #011a2d;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            background-color: #ffffff;
            padding: 15px 25px;
            border-radius: 8px;
            border: 2px solid #a90418;
            display: inline-block;
            margin-bottom: 15px;
        }

        .code-note {
            font-size: 14px;
            color: #6c757d;
            font-style: italic;
        }

        .expiry-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }

        .expiry-warning .icon {
            font-size: 24px;
            color: #856404;
            margin-bottom: 10px;
        }

        .expiry-text {
            font-size: 15px;
            color: #856404;
            font-weight: 600;
        }

        .instructions {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .instructions-title {
            font-size: 16px;
            font-weight: 600;
            color: #0056b3;
            margin-bottom: 10px;
        }

        .instructions-list {
            list-style: none;
            padding: 0;
        }

        .instructions-list li {
            font-size: 14px;
            color: #0056b3;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .instructions-list li:before {
            content: \"→\";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .security-notice {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .security-notice .icon {
            font-size: 20px;
            color: #721c24;
            margin-bottom: 10px;
        }

        .security-text {
            font-size: 14px;
            color: #721c24;
            line-height: 1.6;
        }

        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .footer-text {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .company-info {
            font-size: 12px;
            color: #adb5bd;
            line-height: 1.5;
        }

        .divider {
            height: 2px;
            background: linear-gradient(90deg, #011a2d 0%, #a90418 50%, #011a2d 100%);
            margin: 30px 0;
            border-radius: 1px;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }

            .email-header {
                padding: 30px 20px;
            }

            .email-body {
                padding: 30px 20px;
            }

            .email-title {
                font-size: 24px;
            }

            .verification-code {
                font-size: 28px;
                letter-spacing: 4px;
                padding: 12px 20px;
            }

            .code-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class=\"email-container\">
        <!-- Header -->
        <div class=\"email-header\">
            <div class=\"logo\">
                <img src=\"";
        // line 275
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 275, $this->source); })()), "request", [], "any", false, false, false, 275), "schemeAndHttpHost", [], "any", false, false, false, 275), "html", null, true);
        yield "/images/logos/logo-round.png\" alt=\"Capitol Academy\"
                     onerror=\"this.style.display='none'; this.nextElementSibling.style.display='block';\">
                <div class=\"logo-fallback\">CA</div>
            </div>
            <h1 class=\"email-title\">Capitol Academy</h1>
            <p class=\"email-subtitle\">Password Reset Request</p>
        </div>

        <!-- Body -->
        <div class=\"email-body\">
            <div class=\"greeting\">Hello ";
        // line 285
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 285, $this->source); })()), "firstName", [], "any", false, false, false, 285), "html", null, true);
        yield ",</div>

            <div class=\"message\">
                We received a request to reset your password for your Capitol Academy account. Please use the verification code below to proceed.
            </div>

            <!-- Verification Code -->
            <div class=\"code-container\">
                <div class=\"code-label\">Your Verification Code</div>
                <div class=\"verification-code\">";
        // line 294
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["token"]) || array_key_exists("token", $context) ? $context["token"] : (function () { throw new RuntimeError('Variable "token" does not exist.', 294, $this->source); })()), "html", null, true);
        yield "</div>
                <div class=\"code-note\">Enter this code on the password reset page</div>
            </div>

            <!-- Expiry Warning -->
            <div class=\"expiry-warning\">
                <div class=\"icon\">⏰</div>
                <div class=\"expiry-text\">This code will expire in ";
        // line 301
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["expires_in_minutes"]) || array_key_exists("expires_in_minutes", $context) ? $context["expires_in_minutes"] : (function () { throw new RuntimeError('Variable "expires_in_minutes" does not exist.', 301, $this->source); })()), "html", null, true);
        yield " minutes</div>
            </div>

            <div class=\"divider\"></div>

            <!-- Security Notice -->
            <div class=\"security-notice\">
                <div class=\"icon\">🔒</div>
                <div class=\"security-text\">
                    <strong>Security Notice:</strong> If you did not request this password reset, please ignore this email. Your account remains secure.
                </div>
            </div>

            <div class=\"message\">
                If you're having trouble with the password reset process, please don't hesitate to contact our support team. We're here to help you regain access to your Capitol Academy account.
            </div>
        </div>

        <!-- Footer -->
        <div class=\"email-footer\">
            <div class=\"footer-text\">
                Best regards,<br>
                <strong>The Capitol Academy Team</strong>
            </div>
            
            <div class=\"company-info\">
                Capitol Academy - Professional Trading Education<br>
                This is an automated message. Please do not reply to this email.<br>
                © ";
        // line 329
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "Y"), "html", null, true);
        yield " Capitol Academy. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "emails/password_reset.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  390 => 329,  359 => 301,  349 => 294,  337 => 285,  324 => 275,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Password Reset Code</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Email-safe styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            padding: 40px 30px;
            text-align: center;
            color: #ffffff;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 50%;
        }

        .logo-fallback {
            font-size: 2rem;
            font-weight: bold;
            color: #011a2d;
            display: none;
        }

        .email-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .email-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        .email-body {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #011a2d;
            margin-bottom: 20px;
        }

        .message {
            font-size: 16px;
            color: #343a40;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .code-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #011a2d;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }

        .code-label {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px;
        }

        .verification-code {
            font-size: 36px;
            font-weight: 800;
            color: #011a2d;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            background-color: #ffffff;
            padding: 15px 25px;
            border-radius: 8px;
            border: 2px solid #a90418;
            display: inline-block;
            margin-bottom: 15px;
        }

        .code-note {
            font-size: 14px;
            color: #6c757d;
            font-style: italic;
        }

        .expiry-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }

        .expiry-warning .icon {
            font-size: 24px;
            color: #856404;
            margin-bottom: 10px;
        }

        .expiry-text {
            font-size: 15px;
            color: #856404;
            font-weight: 600;
        }

        .instructions {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .instructions-title {
            font-size: 16px;
            font-weight: 600;
            color: #0056b3;
            margin-bottom: 10px;
        }

        .instructions-list {
            list-style: none;
            padding: 0;
        }

        .instructions-list li {
            font-size: 14px;
            color: #0056b3;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .instructions-list li:before {
            content: \"→\";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .security-notice {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .security-notice .icon {
            font-size: 20px;
            color: #721c24;
            margin-bottom: 10px;
        }

        .security-text {
            font-size: 14px;
            color: #721c24;
            line-height: 1.6;
        }

        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .footer-text {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .company-info {
            font-size: 12px;
            color: #adb5bd;
            line-height: 1.5;
        }

        .divider {
            height: 2px;
            background: linear-gradient(90deg, #011a2d 0%, #a90418 50%, #011a2d 100%);
            margin: 30px 0;
            border-radius: 1px;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }

            .email-header {
                padding: 30px 20px;
            }

            .email-body {
                padding: 30px 20px;
            }

            .email-title {
                font-size: 24px;
            }

            .verification-code {
                font-size: 28px;
                letter-spacing: 4px;
                padding: 12px 20px;
            }

            .code-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class=\"email-container\">
        <!-- Header -->
        <div class=\"email-header\">
            <div class=\"logo\">
                <img src=\"{{ app.request.schemeAndHttpHost }}/images/logos/logo-round.png\" alt=\"Capitol Academy\"
                     onerror=\"this.style.display='none'; this.nextElementSibling.style.display='block';\">
                <div class=\"logo-fallback\">CA</div>
            </div>
            <h1 class=\"email-title\">Capitol Academy</h1>
            <p class=\"email-subtitle\">Password Reset Request</p>
        </div>

        <!-- Body -->
        <div class=\"email-body\">
            <div class=\"greeting\">Hello {{ user.firstName }},</div>

            <div class=\"message\">
                We received a request to reset your password for your Capitol Academy account. Please use the verification code below to proceed.
            </div>

            <!-- Verification Code -->
            <div class=\"code-container\">
                <div class=\"code-label\">Your Verification Code</div>
                <div class=\"verification-code\">{{ token }}</div>
                <div class=\"code-note\">Enter this code on the password reset page</div>
            </div>

            <!-- Expiry Warning -->
            <div class=\"expiry-warning\">
                <div class=\"icon\">⏰</div>
                <div class=\"expiry-text\">This code will expire in {{ expires_in_minutes }} minutes</div>
            </div>

            <div class=\"divider\"></div>

            <!-- Security Notice -->
            <div class=\"security-notice\">
                <div class=\"icon\">🔒</div>
                <div class=\"security-text\">
                    <strong>Security Notice:</strong> If you did not request this password reset, please ignore this email. Your account remains secure.
                </div>
            </div>

            <div class=\"message\">
                If you're having trouble with the password reset process, please don't hesitate to contact our support team. We're here to help you regain access to your Capitol Academy account.
            </div>
        </div>

        <!-- Footer -->
        <div class=\"email-footer\">
            <div class=\"footer-text\">
                Best regards,<br>
                <strong>The Capitol Academy Team</strong>
            </div>
            
            <div class=\"company-info\">
                Capitol Academy - Professional Trading Education<br>
                This is an automated message. Please do not reply to this email.<br>
                © {{ \"now\"|date(\"Y\") }} Capitol Academy. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>
", "emails/password_reset.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\emails\\password_reset.html.twig");
    }
}
