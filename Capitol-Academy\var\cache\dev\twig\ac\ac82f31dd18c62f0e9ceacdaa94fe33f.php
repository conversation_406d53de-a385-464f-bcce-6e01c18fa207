<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* contact/instructor.html.twig */
class __TwigTemplate_d30b4df66f3f830e8cd31e5f4eb27e55 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "contact/instructor.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "contact/instructor.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Instructor Registration - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Join Capitol Academy as a professional instructor and share your expertise in financial markets education with students worldwide.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 8
        yield "<!-- Breadcrumb -->
<nav aria-label=\"breadcrumb\" class=\"bg-light py-3\">
    <div class=\"container\">
        <ol class=\"breadcrumb mb-0\">
            <li class=\"breadcrumb-item\"><a href=\"";
        // line 12
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\">Home</a></li>
            <li class=\"breadcrumb-item active\" aria-current=\"page\">Instructor Registration</li>
        </ol>
    </div>
</nav>

<!-- Hero Section -->
<section class=\"bg-success text-white py-4\">
    <div class=\"container\">
        <div class=\"row align-items-center\">
            <div class=\"col-lg-8\">
                <h1 class=\"display-5 fw-bold mb-2\">Instructor Registration</h1>
                <p class=\"lead mb-0\">
                    Join our team of professional instructors and share your expertise with aspiring traders worldwide.
                </p>
            </div>
            <div class=\"col-lg-4 text-center\">
                <i class=\"fas fa-chalkboard-teacher fa-4x opacity-75\"></i>
            </div>
        </div>
    </div>
</section>

<div class=\"container py-5\">
    <div class=\"row\">
        <div class=\"col-lg-8\">
            <div class=\"card border-0 shadow-sm\">
                <div class=\"card-body p-5\">
                    <h2 class=\"mb-4\">Instructor Application Form</h2>
                    
                    <p class=\"lead mb-4\">
                        Be one of our Capitol Academy Professional Instructors and help shape the next generation of successful traders.
                    </p>
                    
                    <p class=\"mb-4\">
                        Please fill out this application form and our academic team will review your qualifications and contact you about joining our instructor network.
                    </p>
                    
                    ";
        // line 50
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 50, $this->source); })()), 'form_start', ["attr" => ["class" => "needs-validation", "novalidate" => true]]);
        yield "
                    
                    <div class=\"row g-3\">
                        <div class=\"col-md-6\">
                            ";
        // line 54
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 54, $this->source); })()), "fname", [], "any", false, false, false, 54), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 57
        yield "
                        </div>
                        <div class=\"col-md-6\">
                            ";
        // line 60
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 60, $this->source); })()), "lname", [], "any", false, false, false, 60), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 63
        yield "
                        </div>
                    </div>
                    
                    <div class=\"row g-3 mt-2\">
                        <div class=\"col-md-6\">
                            ";
        // line 69
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 69, $this->source); })()), "country", [], "any", false, false, false, 69), 'row', ["attr" => ["class" => "form-select"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 72
        yield "
                        </div>
                        <div class=\"col-md-6\">
                            ";
        // line 75
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 75, $this->source); })()), "email", [], "any", false, false, false, 75), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 78
        yield "
                        </div>
                    </div>
                    
                    <div class=\"row g-3 mt-2\">
                        <div class=\"col-md-6\">
                            ";
        // line 84
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 84, $this->source); })()), "phone", [], "any", false, false, false, 84), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 87
        yield "
                        </div>
                        <div class=\"col-md-6\">
                            ";
        // line 90
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 90, $this->source); })()), "subject", [], "any", false, false, false, 90), 'row', ["attr" => ["class" => "form-control", "readonly" => true], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 93
        yield "
                        </div>
                    </div>
                    
                    <div class=\"mt-3\">
                        ";
        // line 98
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 98, $this->source); })()), "message", [], "any", false, false, false, 98), 'row', ["attr" => ["class" => "form-control", "rows" => 6, "placeholder" => "Please provide details about your trading experience, educational background, areas of expertise, teaching experience, and why you want to join Capitol Academy as an instructor..."], "label" => "Professional Background & Teaching Interest", "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 102
        yield "
                    </div>
                    
                    <div class=\"d-flex justify-content-center gap-3 mt-4\">
                        <button type=\"submit\" class=\"btn btn-success btn-lg px-5 btn-enhanced\">
                            <i class=\"fas fa-chalkboard-teacher me-2\"></i>Submit Application
                        </button>
                        <button type=\"reset\" class=\"btn btn-outline-secondary btn-lg px-5 btn-enhanced\" onclick=\"return confirm('Are you sure you want to reset all fields?')\">
                            <i class=\"fas fa-undo me-2\"></i>Reset Form
                        </button>
                    </div>
                    
                    ";
        // line 114
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 114, $this->source); })()), 'form_end');
        yield "
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class=\"col-lg-4\">
            <!-- Requirements -->
            <div class=\"card mb-4\">
                <div class=\"card-header bg-warning text-dark\">
                    <h5 class=\"mb-0\">Instructor Requirements</h5>
                </div>
                <div class=\"card-body\">
                    <ul class=\"list-unstyled\">
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Minimum 3 years trading experience</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Proven track record of profitability</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Strong communication skills</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Teaching or mentoring experience preferred</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Professional certifications (preferred)</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Fluency in English</li>
                    </ul>
                </div>
            </div>
            
            <!-- Benefits -->
            <div class=\"card mb-4\">
                <div class=\"card-header bg-info text-white\">
                    <h5 class=\"mb-0\">Instructor Benefits</h5>
                </div>
                <div class=\"card-body\">
                    <ul class=\"list-unstyled\">
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Competitive compensation</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Flexible teaching schedule</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Professional development opportunities</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Access to teaching resources</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Global student network</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Capitol Academy certification</li>
                    </ul>
                </div>
            </div>
            
            <!-- Teaching Areas -->
            <div class=\"card mb-4\">
                <div class=\"card-header bg-primary text-white\">
                    <h5 class=\"mb-0\">Teaching Opportunities</h5>
                </div>
                <div class=\"card-body\">
                    <div class=\"row g-2\">
                        <div class=\"col-12\">
                            <span class=\"badge bg-primary w-100 p-2\">Financial Markets Analysis</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-success w-100 p-2\">Technical Analysis</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-info w-100 p-2\">Trading Strategies</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-warning w-100 p-2\">Fundamental Analysis</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-danger w-100 p-2\">Trading Psychology</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-dark w-100 p-2\">Risk & Capital Management</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class=\"card\">
                <div class=\"card-header bg-secondary text-white\">
                    <h5 class=\"mb-0\">Questions?</h5>
                </div>
                <div class=\"card-body\">
                    <p class=\"card-text mb-3\">
                        Have questions about becoming an instructor? Contact our academic team.
                    </p>
                    <div class=\"d-flex align-items-center mb-2\">
                        <i class=\"fas fa-envelope text-primary me-2\"></i>
                        <a href=\"mailto:<EMAIL>\" class=\"text-decoration-none\"><EMAIL></a>
                    </div>
                    <div class=\"d-flex align-items-center\">
                        <i class=\"fas fa-phone text-primary me-2\"></i>
                        <span>+44-************</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "contact/instructor.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  244 => 114,  230 => 102,  228 => 98,  221 => 93,  219 => 90,  214 => 87,  212 => 84,  204 => 78,  202 => 75,  197 => 72,  195 => 69,  187 => 63,  185 => 60,  180 => 57,  178 => 54,  171 => 50,  130 => 12,  124 => 8,  111 => 7,  88 => 5,  65 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Instructor Registration - Capitol Academy{% endblock %}

{% block meta_description %}Join Capitol Academy as a professional instructor and share your expertise in financial markets education with students worldwide.{% endblock %}

{% block body %}
<!-- Breadcrumb -->
<nav aria-label=\"breadcrumb\" class=\"bg-light py-3\">
    <div class=\"container\">
        <ol class=\"breadcrumb mb-0\">
            <li class=\"breadcrumb-item\"><a href=\"{{ path('app_home') }}\">Home</a></li>
            <li class=\"breadcrumb-item active\" aria-current=\"page\">Instructor Registration</li>
        </ol>
    </div>
</nav>

<!-- Hero Section -->
<section class=\"bg-success text-white py-4\">
    <div class=\"container\">
        <div class=\"row align-items-center\">
            <div class=\"col-lg-8\">
                <h1 class=\"display-5 fw-bold mb-2\">Instructor Registration</h1>
                <p class=\"lead mb-0\">
                    Join our team of professional instructors and share your expertise with aspiring traders worldwide.
                </p>
            </div>
            <div class=\"col-lg-4 text-center\">
                <i class=\"fas fa-chalkboard-teacher fa-4x opacity-75\"></i>
            </div>
        </div>
    </div>
</section>

<div class=\"container py-5\">
    <div class=\"row\">
        <div class=\"col-lg-8\">
            <div class=\"card border-0 shadow-sm\">
                <div class=\"card-body p-5\">
                    <h2 class=\"mb-4\">Instructor Application Form</h2>
                    
                    <p class=\"lead mb-4\">
                        Be one of our Capitol Academy Professional Instructors and help shape the next generation of successful traders.
                    </p>
                    
                    <p class=\"mb-4\">
                        Please fill out this application form and our academic team will review your qualifications and contact you about joining our instructor network.
                    </p>
                    
                    {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                    
                    <div class=\"row g-3\">
                        <div class=\"col-md-6\">
                            {{ form_row(form.fname, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                        <div class=\"col-md-6\">
                            {{ form_row(form.lname, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class=\"row g-3 mt-2\">
                        <div class=\"col-md-6\">
                            {{ form_row(form.country, {
                                'attr': {'class': 'form-select'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                        <div class=\"col-md-6\">
                            {{ form_row(form.email, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class=\"row g-3 mt-2\">
                        <div class=\"col-md-6\">
                            {{ form_row(form.phone, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                        <div class=\"col-md-6\">
                            {{ form_row(form.subject, {
                                'attr': {'class': 'form-control', 'readonly': true},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class=\"mt-3\">
                        {{ form_row(form.message, {
                            'attr': {'class': 'form-control', 'rows': 6, 'placeholder': 'Please provide details about your trading experience, educational background, areas of expertise, teaching experience, and why you want to join Capitol Academy as an instructor...'},
                            'label': 'Professional Background & Teaching Interest',
                            'label_attr': {'class': 'form-label fw-bold'}
                        }) }}
                    </div>
                    
                    <div class=\"d-flex justify-content-center gap-3 mt-4\">
                        <button type=\"submit\" class=\"btn btn-success btn-lg px-5 btn-enhanced\">
                            <i class=\"fas fa-chalkboard-teacher me-2\"></i>Submit Application
                        </button>
                        <button type=\"reset\" class=\"btn btn-outline-secondary btn-lg px-5 btn-enhanced\" onclick=\"return confirm('Are you sure you want to reset all fields?')\">
                            <i class=\"fas fa-undo me-2\"></i>Reset Form
                        </button>
                    </div>
                    
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class=\"col-lg-4\">
            <!-- Requirements -->
            <div class=\"card mb-4\">
                <div class=\"card-header bg-warning text-dark\">
                    <h5 class=\"mb-0\">Instructor Requirements</h5>
                </div>
                <div class=\"card-body\">
                    <ul class=\"list-unstyled\">
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Minimum 3 years trading experience</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Proven track record of profitability</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Strong communication skills</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Teaching or mentoring experience preferred</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Professional certifications (preferred)</li>
                        <li class=\"mb-2\"><i class=\"fas fa-check text-success me-2\"></i>Fluency in English</li>
                    </ul>
                </div>
            </div>
            
            <!-- Benefits -->
            <div class=\"card mb-4\">
                <div class=\"card-header bg-info text-white\">
                    <h5 class=\"mb-0\">Instructor Benefits</h5>
                </div>
                <div class=\"card-body\">
                    <ul class=\"list-unstyled\">
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Competitive compensation</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Flexible teaching schedule</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Professional development opportunities</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Access to teaching resources</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Global student network</li>
                        <li class=\"mb-2\"><i class=\"fas fa-star text-warning me-2\"></i>Capitol Academy certification</li>
                    </ul>
                </div>
            </div>
            
            <!-- Teaching Areas -->
            <div class=\"card mb-4\">
                <div class=\"card-header bg-primary text-white\">
                    <h5 class=\"mb-0\">Teaching Opportunities</h5>
                </div>
                <div class=\"card-body\">
                    <div class=\"row g-2\">
                        <div class=\"col-12\">
                            <span class=\"badge bg-primary w-100 p-2\">Financial Markets Analysis</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-success w-100 p-2\">Technical Analysis</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-info w-100 p-2\">Trading Strategies</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-warning w-100 p-2\">Fundamental Analysis</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-danger w-100 p-2\">Trading Psychology</span>
                        </div>
                        <div class=\"col-12\">
                            <span class=\"badge bg-dark w-100 p-2\">Risk & Capital Management</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class=\"card\">
                <div class=\"card-header bg-secondary text-white\">
                    <h5 class=\"mb-0\">Questions?</h5>
                </div>
                <div class=\"card-body\">
                    <p class=\"card-text mb-3\">
                        Have questions about becoming an instructor? Contact our academic team.
                    </p>
                    <div class=\"d-flex align-items-center mb-2\">
                        <i class=\"fas fa-envelope text-primary me-2\"></i>
                        <a href=\"mailto:<EMAIL>\" class=\"text-decoration-none\"><EMAIL></a>
                    </div>
                    <div class=\"d-flex align-items-center\">
                        <i class=\"fas fa-phone text-primary me-2\"></i>
                        <span>+44-************</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
", "contact/instructor.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\contact\\instructor.html.twig");
    }
}
