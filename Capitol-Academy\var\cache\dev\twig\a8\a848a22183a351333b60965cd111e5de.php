<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* course/show.html.twig */
class __TwigTemplate_a4d4a5c2d541c00a605ea4d47219e05a extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "course/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "course/show.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "html", null, true);
        yield " - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 5, $this->source); })()), "description", [], "any", false, false, false, 5), 0, 160), "html", null, true);
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-dark-gray: #343a40;
    --ca-medium-gray: #6c757d;
    --ca-white: #ffffff;
    --ca-green: #99b75a;
    --ca-blue: #00233e;
}

/* Hero Section */
.course-hero {
    background: linear-gradient(135deg, var(--ca-primary) 0%, var(--ca-blue) 100%);
    padding: 120px 0 80px;
    color: white;
    position: relative;
    overflow: hidden;
}

.course-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.course-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--ca-accent) 0%, #c41230 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(169, 4, 24, 0.3);
}

.course-hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    font-family: 'Montserrat', sans-serif;
    line-height: 1.2;
}

.course-hero-description {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 800px;
    line-height: 1.6;
    font-family: 'Calibri', sans-serif;
}

.course-meta-badges {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.meta-badge {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.meta-badge i {
    font-size: 1.1rem;
    opacity: 0.8;
}

/* CTA Section */
.cta-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-top: 40px;
    text-align: center;
}

.cta-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 15px;
    font-family: 'Montserrat', sans-serif;
}

.cta-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.btn-primary-cta {
    background: linear-gradient(135deg, var(--ca-accent) 0%, #c41230 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(169, 4, 24, 0.4);
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    margin-right: 20px;
}

.btn-primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(169, 4, 24, 0.5);
    color: white;
    text-decoration: none;
}

.btn-secondary-cta {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 13px 35px;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary-cta:hover {
    background: white;
    color: var(--ca-primary);
    text-decoration: none;
    transform: translateY(-2px);
}

/* Content Section */
.course-content-section {
    padding: 100px 0;
    background: var(--ca-light-gray);
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    margin-top: 60px;
}

.main-content {
    background: white;
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 10px 40px rgba(1, 26, 45, 0.08);
}

.content-section {
    margin-bottom: 50px;
}

.content-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ca-primary);
    margin-bottom: 25px;
    font-family: 'Montserrat', sans-serif;
    display: flex;
    align-items: center;
    gap: 15px;
}

.section-title i {
    color: var(--ca-accent);
    font-size: 1.5rem;
}

.section-content {
    color: var(--ca-dark-gray);
    line-height: 1.8;
    font-size: 1.1rem;
    font-family: 'Calibri', sans-serif;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.features-list li:last-child {
    border-bottom: none;
}

.features-list i {
    color: var(--ca-green);
    font-size: 1.2rem;
    width: 20px;
}

/* Sidebar */
.course-sidebar {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 40px rgba(1, 26, 45, 0.08);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-section {
    margin-bottom: 40px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--ca-primary);
    margin-bottom: 20px;
    font-family: 'Montserrat', sans-serif;
}

.course-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.course-info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--ca-medium-gray);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-value {
    color: var(--ca-dark-gray);
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .course-sidebar {
        position: static;
    }
}

@media (max-width: 768px) {
    .course-hero-title {
        font-size: 2.5rem;
    }

    .course-meta-badges {
        justify-content: center;
    }

    .main-content {
        padding: 30px;
    }

    .course-sidebar {
        padding: 30px;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 331
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 332
        yield "<!-- Course Hero Section -->
<section class=\"course-hero\">
    <div class=\"container\">
        <div class=\"hero-content\">
            <div class=\"course-badge\">";
        // line 336
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 336, $this->source); })()), "code", [], "any", false, false, false, 336), "html", null, true);
        yield "</div>
            <h1 class=\"course-hero-title\">";
        // line 337
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 337, $this->source); })()), "title", [], "any", false, false, false, 337), "html", null, true);
        yield "</h1>
            <p class=\"course-hero-description\">";
        // line 338
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 338, $this->source); })()), "description", [], "any", false, false, false, 338), "html", null, true);
        yield "</p>

            <!-- Course Meta Information -->
            <div class=\"course-meta-badges\">
                <div class=\"meta-badge\">
                    <i class=\"fas fa-tag\"></i>
                    <span>";
        // line 344
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "category", [], "any", true, true, false, 344)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 344, $this->source); })()), "category", [], "any", false, false, false, 344), "Trading")) : ("Trading")), "html", null, true);
        yield "</span>
                </div>
                ";
        // line 346
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 346, $this->source); })()), "level", [], "any", false, false, false, 346)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 347
            yield "                <div class=\"meta-badge\">
                    <i class=\"fas fa-layer-group\"></i>
                    <span>";
            // line 349
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 349, $this->source); })()), "level", [], "any", false, false, false, 349), "html", null, true);
            yield "</span>
                </div>
                ";
        }
        // line 352
        yield "                <div class=\"meta-badge\">
                    <i class=\"fas fa-eye\"></i>
                    <span>";
        // line 354
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "viewCount", [], "any", true, true, false, 354)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 354, $this->source); })()), "viewCount", [], "any", false, false, false, 354), 0)) : (0)), "html", null, true);
        yield " Views</span>
                </div>
                ";
        // line 356
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 356, $this->source); })()), "isActive", [], "any", false, false, false, 356)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 357
            yield "                <div class=\"meta-badge\">
                    <i class=\"fas fa-check-circle\"></i>
                    <span>Active Course</span>
                </div>
                ";
        }
        // line 362
        yield "            </div>

            <!-- Call to Action -->
            <div class=\"cta-section\">
                <h3 class=\"cta-title\">Ready to Start Your Trading Journey?</h3>
                <p class=\"cta-description\">Contact our team to learn more about enrollment and get personalized guidance for your trading education.</p>
                <a href=\"";
        // line 368
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_registration");
        yield "?course=";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 368, $this->source); })()), "code", [], "any", false, false, false, 368), "html", null, true);
        yield "\" class=\"btn-primary-cta\">
                    <i class=\"fas fa-envelope me-2\"></i>Contact for Enrollment
                </a>
                <a href=\"";
        // line 371
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn-secondary-cta\">
                    <i class=\"fas fa-arrow-left me-2\"></i>View All Courses
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Course Content Section -->
<section class=\"course-content-section\">
    <div class=\"container\">
        <div class=\"content-grid\">
            <!-- Main Content -->
            <div class=\"main-content\">
                <!-- Course Overview -->
                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-info-circle\"></i>
                        Course Overview
                    </h2>
                    <div class=\"section-content\">
                        <p>";
        // line 392
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 392, $this->source); })()), "description", [], "any", false, false, false, 392), "html", null, true);
        yield "</p>
                        <p>This comprehensive course is designed to provide you with the essential knowledge and practical skills needed to succeed in financial markets. Our expert instructors will guide you through every aspect of the subject matter with real-world examples and hands-on exercises.</p>
                    </div>
                </div>

                <!-- Learning Outcomes -->
                ";
        // line 398
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 398, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 398)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 399
            yield "                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-graduation-cap\"></i>
                        What You'll Learn
                    </h2>
                    <div class=\"section-content\">
                        <ul class=\"features-list\">
                            ";
            // line 406
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 406, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 406));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 407
                yield "                            <li>
                                <i class=\"fas fa-check-circle\"></i>
                                <span>";
                // line 409
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "</span>
                            </li>
                            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 412
            yield "                        </ul>
                    </div>
                </div>
                ";
        }
        // line 416
        yield "
                <!-- Course Features -->
                ";
        // line 418
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 418, $this->source); })()), "features", [], "any", false, false, false, 418)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 419
            yield "                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-star\"></i>
                        Course Features
                    </h2>
                    <div class=\"section-content\">
                        <ul class=\"features-list\">
                            ";
            // line 426
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 426, $this->source); })()), "features", [], "any", false, false, false, 426));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 427
                yield "                            <li>
                                <i class=\"fas fa-arrow-right\"></i>
                                <span>";
                // line 429
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "</span>
                            </li>
                            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 432
            yield "                        </ul>
                    </div>
                </div>
                ";
        }
        // line 436
        yield "
                <!-- Course Modules -->
                ";
        // line 438
        if (((isset($context["modules"]) || array_key_exists("modules", $context) ? $context["modules"] : (function () { throw new RuntimeError('Variable "modules" does not exist.', 438, $this->source); })()) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["modules"]) || array_key_exists("modules", $context) ? $context["modules"] : (function () { throw new RuntimeError('Variable "modules" does not exist.', 438, $this->source); })())) > 0))) {
            // line 439
            yield "                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-list\"></i>
                        Course Modules
                    </h2>
                    <div class=\"section-content\">
                        <ul class=\"features-list\">
                            ";
            // line 446
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["modules"]) || array_key_exists("modules", $context) ? $context["modules"] : (function () { throw new RuntimeError('Variable "modules" does not exist.', 446, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                // line 447
                yield "                            <li>
                                <i class=\"fas fa-play-circle\"></i>
                                <span><strong>";
                // line 449
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 449), "html", null, true);
                yield "</strong> - ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 449), 0, 100), "html", null, true);
                if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 449)) > 100)) {
                    yield "...";
                }
                yield "</span>
                            </li>
                            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['module'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 452
            yield "                        </ul>
                    </div>
                </div>
                ";
        }
        // line 456
        yield "            </div>

            <!-- Sidebar -->
            <div class=\"course-sidebar\">
                <!-- Course Information -->
                <div class=\"sidebar-section\">
                    <h3 class=\"sidebar-title\">Course Information</h3>
                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-code\"></i>
                            Course Code
                        </span>
                        <span class=\"info-value\">";
        // line 468
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 468, $this->source); })()), "code", [], "any", false, false, false, 468), "html", null, true);
        yield "</span>
                    </div>
                    ";
        // line 470
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 470, $this->source); })()), "category", [], "any", false, false, false, 470)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 471
            yield "                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-tag\"></i>
                            Category
                        </span>
                        <span class=\"info-value\">";
            // line 476
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 476, $this->source); })()), "category", [], "any", false, false, false, 476), "html", null, true);
            yield "</span>
                    </div>
                    ";
        }
        // line 479
        yield "                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 479, $this->source); })()), "level", [], "any", false, false, false, 479)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 480
            yield "                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-layer-group\"></i>
                            Level
                        </span>
                        <span class=\"info-value\">";
            // line 485
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 485, $this->source); })()), "level", [], "any", false, false, false, 485), "html", null, true);
            yield "</span>
                    </div>
                    ";
        }
        // line 488
        yield "                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-eye\"></i>
                            Views
                        </span>
                        <span class=\"info-value\">";
        // line 493
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "viewCount", [], "any", true, true, false, 493)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 493, $this->source); })()), "viewCount", [], "any", false, false, false, 493), 0)) : (0)), "html", null, true);
        yield "</span>
                    </div>
                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-calendar\"></i>
                            Created
                        </span>
                        <span class=\"info-value\">";
        // line 500
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 500, $this->source); })()), "createdAt", [], "any", false, false, false, 500), "M Y"), "html", null, true);
        yield "</span>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class=\"sidebar-section\">
                    <h3 class=\"sidebar-title\">Quick Actions</h3>
                    <div class=\"d-grid gap-3\">
                        <a href=\"";
        // line 508
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_registration");
        yield "?course=";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 508, $this->source); })()), "code", [], "any", false, false, false, 508), "html", null, true);
        yield "\" class=\"btn-primary-cta\" style=\"margin-right: 0; text-align: center;\">
                            <i class=\"fas fa-envelope me-2\"></i>Contact for Enrollment
                        </a>
                        <a href=\"";
        // line 511
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn-secondary-cta\" style=\"color: var(--ca-primary); border-color: var(--ca-primary); text-align: center;\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Courses
                        </a>
                    </div>
                </div>

                <!-- Need Help -->
                <div class=\"sidebar-section\">
                    <h3 class=\"sidebar-title\">Need Help?</h3>
                    <div class=\"section-content\">
                        <p style=\"color: var(--ca-medium-gray); font-size: 0.95rem; line-height: 1.6;\">
                            Have questions about this course? Our team is here to help you choose the right program for your trading goals.
                        </p>
                        <a href=\"";
        // line 524
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" style=\"color: var(--ca-accent); text-decoration: none; font-weight: 600;\">
                            <i class=\"fas fa-phone me-2\"></i>Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "course/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  796 => 524,  780 => 511,  772 => 508,  761 => 500,  751 => 493,  744 => 488,  738 => 485,  731 => 480,  728 => 479,  722 => 476,  715 => 471,  713 => 470,  708 => 468,  694 => 456,  688 => 452,  674 => 449,  670 => 447,  666 => 446,  657 => 439,  655 => 438,  651 => 436,  645 => 432,  636 => 429,  632 => 427,  628 => 426,  619 => 419,  617 => 418,  613 => 416,  607 => 412,  598 => 409,  594 => 407,  590 => 406,  581 => 399,  579 => 398,  570 => 392,  546 => 371,  538 => 368,  530 => 362,  523 => 357,  521 => 356,  516 => 354,  512 => 352,  506 => 349,  502 => 347,  500 => 346,  495 => 344,  486 => 338,  482 => 337,  478 => 336,  472 => 332,  459 => 331,  126 => 8,  113 => 7,  90 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}{{ course.title }} - Capitol Academy{% endblock %}

{% block meta_description %}{{ course.description|slice(0, 160) }}{% endblock %}

{% block stylesheets %}
{{ parent() }}
<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-dark-gray: #343a40;
    --ca-medium-gray: #6c757d;
    --ca-white: #ffffff;
    --ca-green: #99b75a;
    --ca-blue: #00233e;
}

/* Hero Section */
.course-hero {
    background: linear-gradient(135deg, var(--ca-primary) 0%, var(--ca-blue) 100%);
    padding: 120px 0 80px;
    color: white;
    position: relative;
    overflow: hidden;
}

.course-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.03)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.course-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--ca-accent) 0%, #c41230 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(169, 4, 24, 0.3);
}

.course-hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    font-family: 'Montserrat', sans-serif;
    line-height: 1.2;
}

.course-hero-description {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 800px;
    line-height: 1.6;
    font-family: 'Calibri', sans-serif;
}

.course-meta-badges {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.meta-badge {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.meta-badge i {
    font-size: 1.1rem;
    opacity: 0.8;
}

/* CTA Section */
.cta-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-top: 40px;
    text-align: center;
}

.cta-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 15px;
    font-family: 'Montserrat', sans-serif;
}

.cta-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.btn-primary-cta {
    background: linear-gradient(135deg, var(--ca-accent) 0%, #c41230 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(169, 4, 24, 0.4);
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    margin-right: 20px;
}

.btn-primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(169, 4, 24, 0.5);
    color: white;
    text-decoration: none;
}

.btn-secondary-cta {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 13px 35px;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary-cta:hover {
    background: white;
    color: var(--ca-primary);
    text-decoration: none;
    transform: translateY(-2px);
}

/* Content Section */
.course-content-section {
    padding: 100px 0;
    background: var(--ca-light-gray);
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    margin-top: 60px;
}

.main-content {
    background: white;
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 10px 40px rgba(1, 26, 45, 0.08);
}

.content-section {
    margin-bottom: 50px;
}

.content-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ca-primary);
    margin-bottom: 25px;
    font-family: 'Montserrat', sans-serif;
    display: flex;
    align-items: center;
    gap: 15px;
}

.section-title i {
    color: var(--ca-accent);
    font-size: 1.5rem;
}

.section-content {
    color: var(--ca-dark-gray);
    line-height: 1.8;
    font-size: 1.1rem;
    font-family: 'Calibri', sans-serif;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.features-list li:last-child {
    border-bottom: none;
}

.features-list i {
    color: var(--ca-green);
    font-size: 1.2rem;
    width: 20px;
}

/* Sidebar */
.course-sidebar {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 40px rgba(1, 26, 45, 0.08);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-section {
    margin-bottom: 40px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--ca-primary);
    margin-bottom: 20px;
    font-family: 'Montserrat', sans-serif;
}

.course-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.course-info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--ca-medium-gray);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-value {
    color: var(--ca-dark-gray);
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .course-sidebar {
        position: static;
    }
}

@media (max-width: 768px) {
    .course-hero-title {
        font-size: 2.5rem;
    }

    .course-meta-badges {
        justify-content: center;
    }

    .main-content {
        padding: 30px;
    }

    .course-sidebar {
        padding: 30px;
    }
}
</style>
{% endblock %}

{% block body %}
<!-- Course Hero Section -->
<section class=\"course-hero\">
    <div class=\"container\">
        <div class=\"hero-content\">
            <div class=\"course-badge\">{{ course.code }}</div>
            <h1 class=\"course-hero-title\">{{ course.title }}</h1>
            <p class=\"course-hero-description\">{{ course.description }}</p>

            <!-- Course Meta Information -->
            <div class=\"course-meta-badges\">
                <div class=\"meta-badge\">
                    <i class=\"fas fa-tag\"></i>
                    <span>{{ course.category|default('Trading') }}</span>
                </div>
                {% if course.level %}
                <div class=\"meta-badge\">
                    <i class=\"fas fa-layer-group\"></i>
                    <span>{{ course.level }}</span>
                </div>
                {% endif %}
                <div class=\"meta-badge\">
                    <i class=\"fas fa-eye\"></i>
                    <span>{{ course.viewCount|default(0) }} Views</span>
                </div>
                {% if course.isActive %}
                <div class=\"meta-badge\">
                    <i class=\"fas fa-check-circle\"></i>
                    <span>Active Course</span>
                </div>
                {% endif %}
            </div>

            <!-- Call to Action -->
            <div class=\"cta-section\">
                <h3 class=\"cta-title\">Ready to Start Your Trading Journey?</h3>
                <p class=\"cta-description\">Contact our team to learn more about enrollment and get personalized guidance for your trading education.</p>
                <a href=\"{{ path('app_contact_registration') }}?course={{ course.code }}\" class=\"btn-primary-cta\">
                    <i class=\"fas fa-envelope me-2\"></i>Contact for Enrollment
                </a>
                <a href=\"{{ path('app_courses') }}\" class=\"btn-secondary-cta\">
                    <i class=\"fas fa-arrow-left me-2\"></i>View All Courses
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Course Content Section -->
<section class=\"course-content-section\">
    <div class=\"container\">
        <div class=\"content-grid\">
            <!-- Main Content -->
            <div class=\"main-content\">
                <!-- Course Overview -->
                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-info-circle\"></i>
                        Course Overview
                    </h2>
                    <div class=\"section-content\">
                        <p>{{ course.description }}</p>
                        <p>This comprehensive course is designed to provide you with the essential knowledge and practical skills needed to succeed in financial markets. Our expert instructors will guide you through every aspect of the subject matter with real-world examples and hands-on exercises.</p>
                    </div>
                </div>

                <!-- Learning Outcomes -->
                {% if course.learningOutcomes %}
                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-graduation-cap\"></i>
                        What You'll Learn
                    </h2>
                    <div class=\"section-content\">
                        <ul class=\"features-list\">
                            {% for outcome in course.learningOutcomes %}
                            <li>
                                <i class=\"fas fa-check-circle\"></i>
                                <span>{{ outcome }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}

                <!-- Course Features -->
                {% if course.features %}
                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-star\"></i>
                        Course Features
                    </h2>
                    <div class=\"section-content\">
                        <ul class=\"features-list\">
                            {% for feature in course.features %}
                            <li>
                                <i class=\"fas fa-arrow-right\"></i>
                                <span>{{ feature }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}

                <!-- Course Modules -->
                {% if modules and modules|length > 0 %}
                <div class=\"content-section\">
                    <h2 class=\"section-title\">
                        <i class=\"fas fa-list\"></i>
                        Course Modules
                    </h2>
                    <div class=\"section-content\">
                        <ul class=\"features-list\">
                            {% for module in modules %}
                            <li>
                                <i class=\"fas fa-play-circle\"></i>
                                <span><strong>{{ module.title }}</strong> - {{ module.description|slice(0, 100) }}{% if module.description|length > 100 %}...{% endif %}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class=\"course-sidebar\">
                <!-- Course Information -->
                <div class=\"sidebar-section\">
                    <h3 class=\"sidebar-title\">Course Information</h3>
                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-code\"></i>
                            Course Code
                        </span>
                        <span class=\"info-value\">{{ course.code }}</span>
                    </div>
                    {% if course.category %}
                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-tag\"></i>
                            Category
                        </span>
                        <span class=\"info-value\">{{ course.category }}</span>
                    </div>
                    {% endif %}
                    {% if course.level %}
                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-layer-group\"></i>
                            Level
                        </span>
                        <span class=\"info-value\">{{ course.level }}</span>
                    </div>
                    {% endif %}
                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-eye\"></i>
                            Views
                        </span>
                        <span class=\"info-value\">{{ course.viewCount|default(0) }}</span>
                    </div>
                    <div class=\"course-info-item\">
                        <span class=\"info-label\">
                            <i class=\"fas fa-calendar\"></i>
                            Created
                        </span>
                        <span class=\"info-value\">{{ course.createdAt|date('M Y') }}</span>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class=\"sidebar-section\">
                    <h3 class=\"sidebar-title\">Quick Actions</h3>
                    <div class=\"d-grid gap-3\">
                        <a href=\"{{ path('app_contact_registration') }}?course={{ course.code }}\" class=\"btn-primary-cta\" style=\"margin-right: 0; text-align: center;\">
                            <i class=\"fas fa-envelope me-2\"></i>Contact for Enrollment
                        </a>
                        <a href=\"{{ path('app_courses') }}\" class=\"btn-secondary-cta\" style=\"color: var(--ca-primary); border-color: var(--ca-primary); text-align: center;\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Courses
                        </a>
                    </div>
                </div>

                <!-- Need Help -->
                <div class=\"sidebar-section\">
                    <h3 class=\"sidebar-title\">Need Help?</h3>
                    <div class=\"section-content\">
                        <p style=\"color: var(--ca-medium-gray); font-size: 0.95rem; line-height: 1.6;\">
                            Have questions about this course? Our team is here to help you choose the right program for your trading goals.
                        </p>
                        <a href=\"{{ path('app_contact') }}\" style=\"color: var(--ca-accent); text-decoration: none; font-weight: 600;\">
                            <i class=\"fas fa-phone me-2\"></i>Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock %}", "course/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\course\\show.html.twig");
    }
}
