<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* market_analysis/show.html.twig */
class __TwigTemplate_9f1c9325bbeb81f01b968df1cd8400b8 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "market_analysis/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "market_analysis/show.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "html", null, true);
        yield " - Market Analysis | Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 5, $this->source); })()), "excerpt", [], "any", false, false, false, 5), 0, 160), "html", null, true);
        yield " - Expert market analysis from Capitol Academy's professional trading team.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
<style>
/* Article Detail Page Styles */
.article-detail-page {
    background: #f8f9fa;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(1, 26, 45, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(169, 4, 24, 0.02) 0%, transparent 50%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Enhanced Article Card - Same as main page */
.enhanced-article-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.enhanced-article-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #e9ecef;
    transition: all 0.3s ease;
}

.enhanced-article-item:hover::before {
    background: #a90418;
    width: 6px;
}

/* Breadcrumb Navigation */
.breadcrumb-navigation {
    margin-bottom: 2rem;
}

.breadcrumb-row {
    margin-bottom: 0.75rem;
}

.breadcrumb-row:last-child {
    margin-bottom: 0;
}

/* Back Navigation */
.back-to-analysis {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-to-analysis:hover {
    color: #011a2d;
    text-decoration: none;
    transform: translateX(-3px);
}

/* Asset Category Badge */
.asset-category {
    background: #011a2d;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-bottom: 1rem;
}

/* Article Title */
.enhanced-article-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #011a2d;
    line-height: 1.3;
    margin-bottom: 1rem;
    font-family: 'Georgia', serif;
}

.enhanced-article-title a {
    color: inherit;
    text-decoration: none;
}

/* Article Excerpt */
.enhanced-article-excerpt {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Author and Date Row */
.author-date-row {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.author-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #011a2d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 600;
}

.author-name {
    font-weight: 600;
    color: #343a40;
    font-size: 0.95rem;
}

.publish-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Social Share */
.social-share {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.social-share-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #343a40;
}

.social-share-buttons {
    display: flex;
    gap: 0.5rem;
}

.social-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-btn.twitter {
    background: #1da1f2;
    color: white;
}

.social-btn.linkedin {
    background: #0077b5;
    color: white;
}

.social-btn.facebook {
    background: #1877f2;
    color: white;
}

.social-btn.email {
    background: #6c757d;
    color: white;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    color: white;
    text-decoration: none;
}

/* Featured Image */
.featured-image {
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Content Body */
.content-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #343a40;
    margin-bottom: 2rem;
}

.content-body p {
    margin-bottom: 1.5rem;
}

.content-body h2,
.content-body h3,
.content-body h4 {
    color: #011a2d;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.content-body h2 {
    font-size: 1.5rem;
    border-bottom: 2px solid #a90418;
    padding-bottom: 0.5rem;
}

.content-body h3 {
    font-size: 1.3rem;
}

.content-body h4 {
    font-size: 1.1rem;
}

/* External Link Alert */
.external-link-alert {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-left: 4px solid #a90418;
    border-radius: 8px;
    padding: 2rem;
    margin: 2rem 0;
}

.external-link-btn {
    background: #a90418;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.external-link-btn:hover {
    background: #8b0314;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Related Articles Section - Using same card design */
.related-articles-section {
    margin-top: 3rem;
}

.related-articles-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #011a2d;
    margin-bottom: 2rem;
    text-align: left;
}

.articles-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Meta separator */
.meta-separator {
    color: #6c757d;
    font-weight: 300;
}

/* Article actions */
.article-actions {
    margin-left: auto;
}

.read-more-btn {
    background: #a90418;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    background: #8b0314;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Article Layout for related articles */
.article-layout {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.article-image-container {
    position: relative;
    flex-shrink: 0;
    width: 200px;
    height: 130px;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;
}

.article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.enhanced-article-item:hover .article-image {
    transform: scale(1.05);
}

.article-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.article-meta-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.publish-time {
    color: #6c757d;
}

.enhanced-article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.title-link {
    color: #011a2d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.title-link:hover {
    color: #a90418;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-article-title {
        font-size: 1.8rem;
    }

    .enhanced-article-item {
        padding: 1.5rem;
    }

    .author-date-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .social-share {
        flex-wrap: wrap;
    }

    .article-layout {
        flex-direction: column;
    }

    .article-image-container {
        width: 100%;
        height: 200px;
    }
}

@media (max-width: 576px) {
    .article-detail-page {
        padding: 1rem 0;
    }

    .enhanced-article-title {
        font-size: 1.5rem;
    }

    .enhanced-article-item {
        padding: 1rem;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 460
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 461
        yield "<div class=\"article-detail-page\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-lg-10 mx-auto\">
                <!-- Main Article Card -->
                <article class=\"enhanced-article-item\">
                    <!-- Breadcrumb Navigation -->
                    <div class=\"breadcrumb-navigation\">
                        <!-- First Row: Back to Market Analysis -->
                        <div class=\"breadcrumb-row\">
                            <a href=\"";
        // line 471
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "\" class=\"back-to-analysis\">
                                <i class=\"fas fa-arrow-left\"></i>
                                Back to Market Analysis
                            </a>
                        </div>

                        <!-- Second Row: Article Category -->
                        <div class=\"breadcrumb-row\">
                            <span class=\"asset-category ";
        // line 479
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 479, $this->source); })()), "assetType", [], "any", false, false, false, 479), "html", null, true);
        yield "\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 479, $this->source); })()), "assetTypeLabel", [], "any", false, false, false, 479), "html", null, true);
        yield "</span>
                        </div>
                    </div>

                    <!-- 3. Article Title -->
                    <h1 class=\"enhanced-article-title\">";
        // line 484
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 484, $this->source); })()), "title", [], "any", false, false, false, 484), "html", null, true);
        yield "</h1>

                    <!-- 4. Article Excerpt/Description -->
                    ";
        // line 487
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 487, $this->source); })()), "excerpt", [], "any", false, false, false, 487)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 488
            yield "                    <p class=\"enhanced-article-excerpt\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 488, $this->source); })()), "excerpt", [], "any", false, false, false, 488), "html", null, true);
            yield "</p>
                    ";
        }
        // line 490
        yield "
                    <!-- 5. Author and Publication Date Row -->
                    <div class=\"author-date-row\">
                        <div class=\"author-info\">
                            <div class=\"author-avatar-small\">
                                ";
        // line 495
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 495, $this->source); })()), "author", [], "any", false, false, false, 495)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 496
            yield "                                    ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 496, $this->source); })()), "author", [], "any", false, false, false, 496), 0, 1)), "html", null, true);
            yield "
                                ";
        } else {
            // line 498
            yield "                                    CA
                                ";
        }
        // line 500
        yield "                            </div>
                            <span class=\"author-name\">
                                ";
        // line 502
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 502, $this->source); })()), "author", [], "any", false, false, false, 502)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 503
            yield "                                    ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 503, $this->source); })()), "author", [], "any", false, false, false, 503), "html", null, true);
            yield "
                                ";
        } else {
            // line 505
            yield "                                    Capitol Academy
                                ";
        }
        // line 507
        yield "                            </span>
                        </div>
                        <div class=\"publish-date\">
                            <i class=\"fas fa-calendar-alt\"></i>
                            ";
        // line 511
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 511, $this->source); })()), "publishDate", [], "any", false, false, false, 511), "F j, Y"), "html", null, true);
        yield "
                        </div>
                    </div>

                    <!-- 6. Social Share Options -->
                    <div class=\"social-share\">
                        <span class=\"social-share-label\">Share:</span>
                        <div class=\"social-share-buttons\">
                            <a href=\"https://twitter.com/intent/tweet?text=";
        // line 519
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 519, $this->source); })()), "title", [], "any", false, false, false, 519)), "html", null, true);
        yield "&url=";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 519, $this->source); })()), "slug", [], "any", false, false, false, 519)])), "html", null, true);
        yield "\"
                               target=\"_blank\" class=\"social-btn twitter\" title=\"Share on Twitter\">
                                <i class=\"fab fa-twitter\"></i>
                            </a>
                            <a href=\"https://www.linkedin.com/sharing/share-offsite/?url=";
        // line 523
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 523, $this->source); })()), "slug", [], "any", false, false, false, 523)])), "html", null, true);
        yield "\"
                               target=\"_blank\" class=\"social-btn linkedin\" title=\"Share on LinkedIn\">
                                <i class=\"fab fa-linkedin-in\"></i>
                            </a>
                            <a href=\"https://www.facebook.com/sharer/sharer.php?u=";
        // line 527
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 527, $this->source); })()), "slug", [], "any", false, false, false, 527)])), "html", null, true);
        yield "\"
                               target=\"_blank\" class=\"social-btn facebook\" title=\"Share on Facebook\">
                                <i class=\"fab fa-facebook-f\"></i>
                            </a>
                            <a href=\"mailto:?subject=";
        // line 531
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 531, $this->source); })()), "title", [], "any", false, false, false, 531)), "html", null, true);
        yield "&body=";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 531, $this->source); })()), "slug", [], "any", false, false, false, 531)])), "html", null, true);
        yield "\"
                               class=\"social-btn email\" title=\"Share via Email\">
                                <i class=\"fas fa-envelope\"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 7. Featured Image -->
                    ";
        // line 539
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 539, $this->source); })()), "featuredImageUrl", [], "any", false, false, false, 539)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 540
            yield "                    <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 540, $this->source); })()), "featuredImageUrl", [], "any", false, false, false, 540), "html", null, true);
            yield "\" alt=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 540, $this->source); })()), "title", [], "any", false, false, false, 540), "html", null, true);
            yield "\" class=\"featured-image\">
                    ";
        }
        // line 542
        yield "
                    <!-- 8. Main Article Content -->
                    ";
        // line 544
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 544, $this->source); })()), "content", [], "any", false, false, false, 544)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 545
            yield "                    <div class=\"content-body\">
                        ";
            // line 546
            yield CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 546, $this->source); })()), "content", [], "any", false, false, false, 546);
            yield "
                    </div>
                    ";
        }
        // line 549
        yield "
                    <!-- 9. Social Share Options (Repeated at Bottom) -->
                    <div class=\"social-share\" style=\"border-top: 1px solid #e9ecef; padding-top: 1.5rem; margin-top: 2rem;\">
                        <span class=\"social-share-label\">Share this analysis:</span>
                        <div class=\"social-share-buttons\">
                            <a href=\"https://twitter.com/intent/tweet?text=";
        // line 554
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 554, $this->source); })()), "title", [], "any", false, false, false, 554)), "html", null, true);
        yield "&url=";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 554, $this->source); })()), "slug", [], "any", false, false, false, 554)])), "html", null, true);
        yield "\"
                               target=\"_blank\" class=\"social-btn twitter\" title=\"Share on Twitter\">
                                <i class=\"fab fa-twitter\"></i>
                            </a>
                            <a href=\"https://www.linkedin.com/sharing/share-offsite/?url=";
        // line 558
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 558, $this->source); })()), "slug", [], "any", false, false, false, 558)])), "html", null, true);
        yield "\"
                               target=\"_blank\" class=\"social-btn linkedin\" title=\"Share on LinkedIn\">
                                <i class=\"fab fa-linkedin-in\"></i>
                            </a>
                            <a href=\"https://www.facebook.com/sharer/sharer.php?u=";
        // line 562
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 562, $this->source); })()), "slug", [], "any", false, false, false, 562)])), "html", null, true);
        yield "\"
                               target=\"_blank\" class=\"social-btn facebook\" title=\"Share on Facebook\">
                                <i class=\"fab fa-facebook-f\"></i>
                            </a>
                            <a href=\"mailto:?subject=";
        // line 566
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 566, $this->source); })()), "title", [], "any", false, false, false, 566)), "html", null, true);
        yield "&body=";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::urlencode($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getUrl("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 566, $this->source); })()), "slug", [], "any", false, false, false, 566)])), "html", null, true);
        yield "\"
                               class=\"social-btn email\" title=\"Share via Email\">
                                <i class=\"fas fa-envelope\"></i>
                            </a>
                        </div>
                    </div>
                </article>
            </div>
        </div>

        <!-- Related Articles Section - Using Same Card Design as Main Page -->
        ";
        // line 577
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["relatedArticles"]) || array_key_exists("relatedArticles", $context) ? $context["relatedArticles"] : (function () { throw new RuntimeError('Variable "relatedArticles" does not exist.', 577, $this->source); })())) > 0)) {
            // line 578
            yield "        <div class=\"row\">
            <div class=\"col-lg-10 mx-auto\">
                <div class=\"related-articles-section\">
                    <h2 class=\"related-articles-title\">Related Articles</h2>
                    <div class=\"articles-list\">
                        ";
            // line 583
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["relatedArticles"]) || array_key_exists("relatedArticles", $context) ? $context["relatedArticles"] : (function () { throw new RuntimeError('Variable "relatedArticles" does not exist.', 583, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["relatedArticle"]) {
                // line 584
                yield "                        <article class=\"enhanced-article-item\">
                            <div class=\"article-layout\">
                                <!-- Article Thumbnail -->
                                <div class=\"article-image-container\">
                                    <img src=\"";
                // line 588
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "thumbnailUrl", [], "any", false, false, false, 588), "html", null, true);
                yield "\"
                                         alt=\"";
                // line 589
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "title", [], "any", false, false, false, 589), "html", null, true);
                yield "\"
                                         class=\"article-image\"
                                         loading=\"lazy\">
                                </div>

                                <!-- Article Content -->
                                <div class=\"article-content-area\">
                                    <!-- Meta Information -->
                                    <div class=\"article-meta-row\">
                                        <span class=\"asset-category ";
                // line 598
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "assetType", [], "any", false, false, false, 598), "html", null, true);
                yield "\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "assetTypeLabel", [], "any", false, false, false, 598), "html", null, true);
                yield "</span>
                                        <span class=\"meta-separator\">•</span>
                                        <span class=\"publish-time\">";
                // line 600
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "publishDate", [], "any", false, false, false, 600), "M j, Y"), "html", null, true);
                yield "</span>
                                    </div>

                                    <!-- Article Title -->
                                    <h3 class=\"enhanced-article-title\" style=\"font-size: 1.3rem;\">
                                        <a href=\"";
                // line 605
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "slug", [], "any", false, false, false, 605)]), "html", null, true);
                yield "\" class=\"title-link\">
                                            ";
                // line 606
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "title", [], "any", false, false, false, 606), "html", null, true);
                yield "
                                        </a>
                                    </h3>

                                    <!-- Article Excerpt -->
                                    <p class=\"enhanced-article-excerpt\">";
                // line 611
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "shortExcerpt", [120], "method", false, false, false, 611), "html", null, true);
                yield "</p>

                                    <!-- Article Footer -->
                                    <div class=\"enhanced-article-footer\">
                                        <div class=\"author-info\">
                                            ";
                // line 616
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "author", [], "any", false, false, false, 616)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 617
                    yield "                                                <div class=\"author-avatar-small\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "author", [], "any", false, false, false, 617), 0, 1)), "html", null, true);
                    yield "</div>
                                                <span class=\"author-name\">";
                    // line 618
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "author", [], "any", false, false, false, 618), "html", null, true);
                    yield "</span>
                                            ";
                } else {
                    // line 620
                    yield "                                                <div class=\"author-avatar-small\">CA</div>
                                                <span class=\"author-name\">Capitol Academy</span>
                                            ";
                }
                // line 623
                yield "                                        </div>

                                        <div class=\"article-actions\">
                                            <a href=\"";
                // line 626
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, $context["relatedArticle"], "slug", [], "any", false, false, false, 626)]), "html", null, true);
                yield "\" class=\"read-more-btn\">
                                                <span>Read Analysis</span>
                                                <i class=\"fas fa-arrow-right\"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['relatedArticle'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 636
            yield "                    </div>
                </div>
            </div>
        </div>
        ";
        }
        // line 641
        yield "    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "market_analysis/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  923 => 641,  916 => 636,  900 => 626,  895 => 623,  890 => 620,  885 => 618,  880 => 617,  878 => 616,  870 => 611,  862 => 606,  858 => 605,  850 => 600,  843 => 598,  831 => 589,  827 => 588,  821 => 584,  817 => 583,  810 => 578,  808 => 577,  792 => 566,  785 => 562,  778 => 558,  769 => 554,  762 => 549,  756 => 546,  753 => 545,  751 => 544,  747 => 542,  739 => 540,  737 => 539,  724 => 531,  717 => 527,  710 => 523,  701 => 519,  690 => 511,  684 => 507,  680 => 505,  674 => 503,  672 => 502,  668 => 500,  664 => 498,  658 => 496,  656 => 495,  649 => 490,  643 => 488,  641 => 487,  635 => 484,  625 => 479,  614 => 471,  602 => 461,  589 => 460,  127 => 8,  114 => 7,  90 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}{{ article.title }} - Market Analysis | Capitol Academy{% endblock %}

{% block meta_description %}{{ article.excerpt|slice(0, 160) }} - Expert market analysis from Capitol Academy's professional trading team.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
<style>
/* Article Detail Page Styles */
.article-detail-page {
    background: #f8f9fa;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(1, 26, 45, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(169, 4, 24, 0.02) 0%, transparent 50%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Enhanced Article Card - Same as main page */
.enhanced-article-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.enhanced-article-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #e9ecef;
    transition: all 0.3s ease;
}

.enhanced-article-item:hover::before {
    background: #a90418;
    width: 6px;
}

/* Breadcrumb Navigation */
.breadcrumb-navigation {
    margin-bottom: 2rem;
}

.breadcrumb-row {
    margin-bottom: 0.75rem;
}

.breadcrumb-row:last-child {
    margin-bottom: 0;
}

/* Back Navigation */
.back-to-analysis {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-to-analysis:hover {
    color: #011a2d;
    text-decoration: none;
    transform: translateX(-3px);
}

/* Asset Category Badge */
.asset-category {
    background: #011a2d;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-bottom: 1rem;
}

/* Article Title */
.enhanced-article-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #011a2d;
    line-height: 1.3;
    margin-bottom: 1rem;
    font-family: 'Georgia', serif;
}

.enhanced-article-title a {
    color: inherit;
    text-decoration: none;
}

/* Article Excerpt */
.enhanced-article-excerpt {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Author and Date Row */
.author-date-row {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.author-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #011a2d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 600;
}

.author-name {
    font-weight: 600;
    color: #343a40;
    font-size: 0.95rem;
}

.publish-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Social Share */
.social-share {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.social-share-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #343a40;
}

.social-share-buttons {
    display: flex;
    gap: 0.5rem;
}

.social-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-btn.twitter {
    background: #1da1f2;
    color: white;
}

.social-btn.linkedin {
    background: #0077b5;
    color: white;
}

.social-btn.facebook {
    background: #1877f2;
    color: white;
}

.social-btn.email {
    background: #6c757d;
    color: white;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    color: white;
    text-decoration: none;
}

/* Featured Image */
.featured-image {
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Content Body */
.content-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #343a40;
    margin-bottom: 2rem;
}

.content-body p {
    margin-bottom: 1.5rem;
}

.content-body h2,
.content-body h3,
.content-body h4 {
    color: #011a2d;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.content-body h2 {
    font-size: 1.5rem;
    border-bottom: 2px solid #a90418;
    padding-bottom: 0.5rem;
}

.content-body h3 {
    font-size: 1.3rem;
}

.content-body h4 {
    font-size: 1.1rem;
}

/* External Link Alert */
.external-link-alert {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-left: 4px solid #a90418;
    border-radius: 8px;
    padding: 2rem;
    margin: 2rem 0;
}

.external-link-btn {
    background: #a90418;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.external-link-btn:hover {
    background: #8b0314;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Related Articles Section - Using same card design */
.related-articles-section {
    margin-top: 3rem;
}

.related-articles-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #011a2d;
    margin-bottom: 2rem;
    text-align: left;
}

.articles-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Meta separator */
.meta-separator {
    color: #6c757d;
    font-weight: 300;
}

/* Article actions */
.article-actions {
    margin-left: auto;
}

.read-more-btn {
    background: #a90418;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    background: #8b0314;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Article Layout for related articles */
.article-layout {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.article-image-container {
    position: relative;
    flex-shrink: 0;
    width: 200px;
    height: 130px;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;
}

.article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.enhanced-article-item:hover .article-image {
    transform: scale(1.05);
}

.article-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.article-meta-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.publish-time {
    color: #6c757d;
}

.enhanced-article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.title-link {
    color: #011a2d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.title-link:hover {
    color: #a90418;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-article-title {
        font-size: 1.8rem;
    }

    .enhanced-article-item {
        padding: 1.5rem;
    }

    .author-date-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .social-share {
        flex-wrap: wrap;
    }

    .article-layout {
        flex-direction: column;
    }

    .article-image-container {
        width: 100%;
        height: 200px;
    }
}

@media (max-width: 576px) {
    .article-detail-page {
        padding: 1rem 0;
    }

    .enhanced-article-title {
        font-size: 1.5rem;
    }

    .enhanced-article-item {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block body %}
<div class=\"article-detail-page\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-lg-10 mx-auto\">
                <!-- Main Article Card -->
                <article class=\"enhanced-article-item\">
                    <!-- Breadcrumb Navigation -->
                    <div class=\"breadcrumb-navigation\">
                        <!-- First Row: Back to Market Analysis -->
                        <div class=\"breadcrumb-row\">
                            <a href=\"{{ path('app_market_analysis') }}\" class=\"back-to-analysis\">
                                <i class=\"fas fa-arrow-left\"></i>
                                Back to Market Analysis
                            </a>
                        </div>

                        <!-- Second Row: Article Category -->
                        <div class=\"breadcrumb-row\">
                            <span class=\"asset-category {{ article.assetType }}\">{{ article.assetTypeLabel }}</span>
                        </div>
                    </div>

                    <!-- 3. Article Title -->
                    <h1 class=\"enhanced-article-title\">{{ article.title }}</h1>

                    <!-- 4. Article Excerpt/Description -->
                    {% if article.excerpt %}
                    <p class=\"enhanced-article-excerpt\">{{ article.excerpt }}</p>
                    {% endif %}

                    <!-- 5. Author and Publication Date Row -->
                    <div class=\"author-date-row\">
                        <div class=\"author-info\">
                            <div class=\"author-avatar-small\">
                                {% if article.author %}
                                    {{ article.author|slice(0, 1)|upper }}
                                {% else %}
                                    CA
                                {% endif %}
                            </div>
                            <span class=\"author-name\">
                                {% if article.author %}
                                    {{ article.author }}
                                {% else %}
                                    Capitol Academy
                                {% endif %}
                            </span>
                        </div>
                        <div class=\"publish-date\">
                            <i class=\"fas fa-calendar-alt\"></i>
                            {{ article.publishDate|date('F j, Y') }}
                        </div>
                    </div>

                    <!-- 6. Social Share Options -->
                    <div class=\"social-share\">
                        <span class=\"social-share-label\">Share:</span>
                        <div class=\"social-share-buttons\">
                            <a href=\"https://twitter.com/intent/tweet?text={{ article.title|url_encode }}&url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               target=\"_blank\" class=\"social-btn twitter\" title=\"Share on Twitter\">
                                <i class=\"fab fa-twitter\"></i>
                            </a>
                            <a href=\"https://www.linkedin.com/sharing/share-offsite/?url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               target=\"_blank\" class=\"social-btn linkedin\" title=\"Share on LinkedIn\">
                                <i class=\"fab fa-linkedin-in\"></i>
                            </a>
                            <a href=\"https://www.facebook.com/sharer/sharer.php?u={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               target=\"_blank\" class=\"social-btn facebook\" title=\"Share on Facebook\">
                                <i class=\"fab fa-facebook-f\"></i>
                            </a>
                            <a href=\"mailto:?subject={{ article.title|url_encode }}&body={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               class=\"social-btn email\" title=\"Share via Email\">
                                <i class=\"fas fa-envelope\"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 7. Featured Image -->
                    {% if article.featuredImageUrl %}
                    <img src=\"{{ article.featuredImageUrl }}\" alt=\"{{ article.title }}\" class=\"featured-image\">
                    {% endif %}

                    <!-- 8. Main Article Content -->
                    {% if article.content %}
                    <div class=\"content-body\">
                        {{ article.content|raw }}
                    </div>
                    {% endif %}

                    <!-- 9. Social Share Options (Repeated at Bottom) -->
                    <div class=\"social-share\" style=\"border-top: 1px solid #e9ecef; padding-top: 1.5rem; margin-top: 2rem;\">
                        <span class=\"social-share-label\">Share this analysis:</span>
                        <div class=\"social-share-buttons\">
                            <a href=\"https://twitter.com/intent/tweet?text={{ article.title|url_encode }}&url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               target=\"_blank\" class=\"social-btn twitter\" title=\"Share on Twitter\">
                                <i class=\"fab fa-twitter\"></i>
                            </a>
                            <a href=\"https://www.linkedin.com/sharing/share-offsite/?url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               target=\"_blank\" class=\"social-btn linkedin\" title=\"Share on LinkedIn\">
                                <i class=\"fab fa-linkedin-in\"></i>
                            </a>
                            <a href=\"https://www.facebook.com/sharer/sharer.php?u={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               target=\"_blank\" class=\"social-btn facebook\" title=\"Share on Facebook\">
                                <i class=\"fab fa-facebook-f\"></i>
                            </a>
                            <a href=\"mailto:?subject={{ article.title|url_encode }}&body={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}\"
                               class=\"social-btn email\" title=\"Share via Email\">
                                <i class=\"fas fa-envelope\"></i>
                            </a>
                        </div>
                    </div>
                </article>
            </div>
        </div>

        <!-- Related Articles Section - Using Same Card Design as Main Page -->
        {% if relatedArticles|length > 0 %}
        <div class=\"row\">
            <div class=\"col-lg-10 mx-auto\">
                <div class=\"related-articles-section\">
                    <h2 class=\"related-articles-title\">Related Articles</h2>
                    <div class=\"articles-list\">
                        {% for relatedArticle in relatedArticles %}
                        <article class=\"enhanced-article-item\">
                            <div class=\"article-layout\">
                                <!-- Article Thumbnail -->
                                <div class=\"article-image-container\">
                                    <img src=\"{{ relatedArticle.thumbnailUrl }}\"
                                         alt=\"{{ relatedArticle.title }}\"
                                         class=\"article-image\"
                                         loading=\"lazy\">
                                </div>

                                <!-- Article Content -->
                                <div class=\"article-content-area\">
                                    <!-- Meta Information -->
                                    <div class=\"article-meta-row\">
                                        <span class=\"asset-category {{ relatedArticle.assetType }}\">{{ relatedArticle.assetTypeLabel }}</span>
                                        <span class=\"meta-separator\">•</span>
                                        <span class=\"publish-time\">{{ relatedArticle.publishDate|date('M j, Y') }}</span>
                                    </div>

                                    <!-- Article Title -->
                                    <h3 class=\"enhanced-article-title\" style=\"font-size: 1.3rem;\">
                                        <a href=\"{{ path('app_market_analysis_show_seo', {'slug': relatedArticle.slug}) }}\" class=\"title-link\">
                                            {{ relatedArticle.title }}
                                        </a>
                                    </h3>

                                    <!-- Article Excerpt -->
                                    <p class=\"enhanced-article-excerpt\">{{ relatedArticle.shortExcerpt(120) }}</p>

                                    <!-- Article Footer -->
                                    <div class=\"enhanced-article-footer\">
                                        <div class=\"author-info\">
                                            {% if relatedArticle.author %}
                                                <div class=\"author-avatar-small\">{{ relatedArticle.author|slice(0, 1)|upper }}</div>
                                                <span class=\"author-name\">{{ relatedArticle.author }}</span>
                                            {% else %}
                                                <div class=\"author-avatar-small\">CA</div>
                                                <span class=\"author-name\">Capitol Academy</span>
                                            {% endif %}
                                        </div>

                                        <div class=\"article-actions\">
                                            <a href=\"{{ path('app_market_analysis_show_seo', {'slug': relatedArticle.slug}) }}\" class=\"read-more-btn\">
                                                <span>Read Analysis</span>
                                                <i class=\"fas fa-arrow-right\"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}", "market_analysis/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\market_analysis\\show.html.twig");
    }
}
