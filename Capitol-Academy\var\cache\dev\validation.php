<?php

// This file has been auto-generated by the Symfony Cache Component.

return [[

'Symfony.Component.Form.Form' => 0,
'IteratorAggregate' => 1,
'Symfony.Component.Form.FormInterface' => 2,
'Symfony.Component.Form.ClearableErrorsInterface' => 3,
'Traversable' => 4,
'Countable' => 5,
'ArrayAccess' => 6,

], [

0 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            (\Symfony\Component\VarExporter\Internal\Registry::$factories['Symfony\\Component\\Validator\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::f('Symfony\\Component\\Validator\\Mapping\\ClassMetadata'))(),
            clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Symfony\\Component\\Form\\Extension\\Validator\\Constraints\\Form'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Symfony\\Component\\Form\\Extension\\Validator\\Constraints\\Form')),
        ],
        null,
        [
            'stdClass' => [
                'constraints' => [
                    [
                        $o[1],
                    ],
                ],
                'constraintsByGroup' => [
                    [
                        'Default' => [
                            $o[1],
                        ],
                        'Form' => [
                            $o[1],
                        ],
                    ],
                ],
                'traversalStrategy' => [
                    2,
                ],
                'name' => [
                    'Symfony\\Component\\Form\\Form',
                ],
                'defaultGroup' => [
                    'Form',
                ],
                'groups' => [
                    1 => [
                        'Default',
                        'Form',
                    ],
                ],
            ],
        ],
        $o[0],
        []
    );
},
1 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            (\Symfony\Component\VarExporter\Internal\Registry::$factories['Symfony\\Component\\Validator\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::f('Symfony\\Component\\Validator\\Mapping\\ClassMetadata'))(),
        ],
        null,
        [
            'stdClass' => [
                'name' => [
                    'IteratorAggregate',
                ],
                'defaultGroup' => [
                    'IteratorAggregate',
                ],
            ],
        ],
        $o[0],
        []
    );
},
2 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            (\Symfony\Component\VarExporter\Internal\Registry::$factories['Symfony\\Component\\Validator\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::f('Symfony\\Component\\Validator\\Mapping\\ClassMetadata'))(),
        ],
        null,
        [
            'stdClass' => [
                'name' => [
                    'Symfony\\Component\\Form\\FormInterface',
                ],
                'defaultGroup' => [
                    'FormInterface',
                ],
            ],
        ],
        $o[0],
        []
    );
},
3 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            (\Symfony\Component\VarExporter\Internal\Registry::$factories['Symfony\\Component\\Validator\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::f('Symfony\\Component\\Validator\\Mapping\\ClassMetadata'))(),
        ],
        null,
        [
            'stdClass' => [
                'name' => [
                    'Symfony\\Component\\Form\\ClearableErrorsInterface',
                ],
                'defaultGroup' => [
                    'ClearableErrorsInterface',
                ],
            ],
        ],
        $o[0],
        []
    );
},
4 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            (\Symfony\Component\VarExporter\Internal\Registry::$factories['Symfony\\Component\\Validator\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::f('Symfony\\Component\\Validator\\Mapping\\ClassMetadata'))(),
        ],
        null,
        [
            'stdClass' => [
                'name' => [
                    'Traversable',
                ],
                'defaultGroup' => [
                    'Traversable',
                ],
            ],
        ],
        $o[0],
        []
    );
},
5 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            (\Symfony\Component\VarExporter\Internal\Registry::$factories['Symfony\\Component\\Validator\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::f('Symfony\\Component\\Validator\\Mapping\\ClassMetadata'))(),
        ],
        null,
        [
            'stdClass' => [
                'name' => [
                    'Countable',
                ],
                'defaultGroup' => [
                    'Countable',
                ],
            ],
        ],
        $o[0],
        []
    );
},
6 => static function () {
    return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
        $o = [
            (\Symfony\Component\VarExporter\Internal\Registry::$factories['Symfony\\Component\\Validator\\Mapping\\ClassMetadata'] ?? \Symfony\Component\VarExporter\Internal\Registry::f('Symfony\\Component\\Validator\\Mapping\\ClassMetadata'))(),
        ],
        null,
        [
            'stdClass' => [
                'name' => [
                    'ArrayAccess',
                ],
                'defaultGroup' => [
                    'ArrayAccess',
                ],
            ],
        ],
        $o[0],
        []
    );
},

]];
