<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/users/index.html.twig */
class __TwigTemplate_a9c80cfbd3d586c48e85c572a12e8d7b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/users/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/users/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Users Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Users Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Users</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Users Management", "page_icon" => "fas fa-users", "search_placeholder" => "Search users by name, email, or country...", "stats" => [["title" => "Total Users", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 20
(isset($context["users"]) || array_key_exists("users", $context) ? $context["users"] : (function () { throw new RuntimeError('Variable "users" does not exist.', 20, $this->source); })())), "icon" => "fas fa-users", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active Users", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 27
(isset($context["users"]) || array_key_exists("users", $context) ? $context["users"] : (function () { throw new RuntimeError('Variable "users" does not exist.', 27, $this->source); })()), function ($__user__) use ($context, $macros) { $context["user"] = $__user__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 27, $this->source); })()), "isBlocked", [], "any", false, false, false, 27); })), "icon" => "fas fa-user-check", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Blocked Users", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 34
(isset($context["users"]) || array_key_exists("users", $context) ? $context["users"] : (function () { throw new RuntimeError('Variable "users" does not exist.', 34, $this->source); })()), function ($__user__) use ($context, $macros) { $context["user"] = $__user__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 34, $this->source); })()), "isBlocked", [], "any", false, false, false, 34); })), "icon" => "fas fa-user-slash", "color" => "#dc3545", "gradient" => "linear-gradient(135deg, #dc3545 0%, #c82333 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 41
(isset($context["users"]) || array_key_exists("users", $context) ? $context["users"] : (function () { throw new RuntimeError('Variable "users" does not exist.', 41, $this->source); })()), function ($__user__) use ($context, $macros) { $context["user"] = $__user__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 41, $this->source); })()), "createdAt", [], "any", false, false, false, 41) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 41, $this->source); })()), "createdAt", [], "any", false, false, false, 41) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-user-plus", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 48
        yield "
";
        // line 49
        yield from $this->load("admin/users/index.html.twig", 49, "1717497792")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 49, $this->source); })())));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 127
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 128
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.user-row',
        ['.user-name', '.user-email', '.user-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'block' : 'unblock';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// User management functions
function toggleUserStatus(userId, userName, isBlocked) {
    const action = isBlocked ? 'unblock' : 'block';
    showStatusModal(userName, isBlocked ? 'true' : 'false', function() {
        executeUserStatusToggle(userId);
    });
}

function deleteUser(userId, userName) {
    showDeleteModal(userName, function() {
        executeUserDelete(userId);
    });
}

// Actual execution functions
function executeUserStatusToggle(userId) {
    fetch(`/admin/users/\${userId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the user status'); // REMOVED
    });
}

function executeUserDelete(userId) {
    fetch(`/admin/users/\${userId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the user');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/users/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  187 => 128,  174 => 127,  163 => 49,  160 => 48,  158 => 41,  157 => 34,  156 => 27,  155 => 20,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Users Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Users Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Users</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Users Management',
    'page_icon': 'fas fa-users',
    'search_placeholder': 'Search users by name, email, or country...',
    'stats': [
        {
            'title': 'Total Users',
            'value': users|length,
            'icon': 'fas fa-users',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active Users',
            'value': users|filter(user => not user.isBlocked)|length,
            'icon': 'fas fa-user-check',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Blocked Users',
            'value': users|filter(user => user.isBlocked)|length,
            'icon': 'fas fa-user-slash',
            'color': '#dc3545',
            'gradient': 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': users|filter(user => user.createdAt and user.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-user-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Profile'},
            {'text': 'Name'},
            {'text': 'Email'},
            {'text': 'Country'},
            {'text': 'Phone'},
            {'text': 'Status'},
            {'text': 'Joined'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for user in users %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center justify-content-center\">
                        ' ~ (user.profilePicture ?
                            '<img src=\"' ~ asset('uploads/profiles/' ~ user.profilePicture) ~ '\" alt=\"' ~ user.fullName ~ '\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\">' :
                            '<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">' ~ user.firstName|first ~ user.lastName|first ~ '</div>'
                        ) ~ '
                    </div>'
                },
                {
                    'content': '<h6 class=\"user-name mb-0 font-weight-bold text-dark\">' ~ user.fullName ~ '</h6>'
                },
                {
                    'content': '<span class=\"user-email text-dark\">' ~ user.email ~ '</span>'
                },
                {
                    'content': '<span class=\"user-country text-dark\">' ~ (user.country|default('N/A')) ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark\">' ~ (user.phone|default('N/A')) ~ '</span>'
                },
                {
                    'content': user.isBlocked ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Blocked</span>' :
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>'
                },
                {
                    'content': '<small class=\"text-muted\">' ~ user.createdAt|date('M d, Y') ~ '</small>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_user_details', {'emailPrefix': user.emailPrefix}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ' ~ (canEdit ?
                            (user.isBlocked ?
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Unblock User\" onclick=\"toggleUserStatus(' ~ user.id ~ ', \\'' ~ user.fullName ~ '\\', ' ~ user.isBlocked ~ ')\"><i class=\"fas fa-unlock\"></i></button>' :
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Block User\" onclick=\"toggleUserStatus(' ~ user.id ~ ', \\'' ~ user.fullName ~ '\\', ' ~ user.isBlocked ~ ')\"><i class=\"fas fa-lock\"></i></button>'
                            ) : ''
                        ) ~
                        (canDelete ?
                            '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete User\" onclick=\"deleteUser(' ~ user.id ~ ', \\'' ~ user.fullName ~ '\\')\"><i class=\"fas fa-trash\"></i></button>' : ''
                        ) ~
                    '</div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'user-row',
            'empty_message': 'No users found',
            'empty_icon': 'fas fa-users',
            'empty_description': 'Users will appear here as they register.',
            'search_config': {
                'fields': ['.user-name', '.user-email', '.user-country']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.user-row',
        ['.user-name', '.user-email', '.user-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'block' : 'unblock';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// User management functions
function toggleUserStatus(userId, userName, isBlocked) {
    const action = isBlocked ? 'unblock' : 'block';
    showStatusModal(userName, isBlocked ? 'true' : 'false', function() {
        executeUserStatusToggle(userId);
    });
}

function deleteUser(userId, userName) {
    showDeleteModal(userName, function() {
        executeUserDelete(userId);
    });
}

// Actual execution functions
function executeUserStatusToggle(userId) {
    fetch(`/admin/users/\${userId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the user status'); // REMOVED
    });
}

function executeUserDelete(userId) {
    fetch(`/admin/users/\${userId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the user');
    });
}
</script>
{% endblock %}


", "admin/users/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\users\\index.html.twig");
    }
}


/* admin/users/index.html.twig */
class __TwigTemplate_a9c80cfbd3d586c48e85c572a12e8d7b___1717497792 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 49
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/users/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/users/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 49);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 50
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 51
        yield "        <!-- Standardized Table -->
        ";
        // line 52
        $context["table_headers"] = [["text" => "Profile"], ["text" => "Name"], ["text" => "Email"], ["text" => "Country"], ["text" => "Phone"], ["text" => "Status"], ["text" => "Joined"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 62
        yield "
        ";
        // line 63
        $context["table_rows"] = [];
        // line 64
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["users"]) || array_key_exists("users", $context) ? $context["users"] : (function () { throw new RuntimeError('Variable "users" does not exist.', 64, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["user"]) {
            // line 65
            yield "            ";
            $context["row_cells"] = [["content" => (("<div class=\"d-flex align-items-center justify-content-center\">
                        " . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 68
$context["user"], "profilePicture", [], "any", false, false, false, 68)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((("<img src=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source,             // line 69
$context["user"], "profilePicture", [], "any", false, false, false, 69)))) . "\" alt=\"") . CoreExtension::getAttribute($this->env, $this->source, $context["user"], "fullName", [], "any", false, false, false, 69)) . "\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\">")) : (((("<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">" . Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 70
$context["user"], "firstName", [], "any", false, false, false, 70))) . Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["user"], "lastName", [], "any", false, false, false, 70))) . "</div>")))) . "
                    </div>")], ["content" => (("<h6 class=\"user-name mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 75
$context["user"], "fullName", [], "any", false, false, false, 75)) . "</h6>")], ["content" => (("<span class=\"user-email text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["user"], "email", [], "any", false, false, false, 78)) . "</span>")], ["content" => (("<span class=\"user-country text-dark\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 81
$context["user"], "country", [], "any", true, true, false, 81)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["user"], "country", [], "any", false, false, false, 81), "N/A")) : ("N/A"))) . "</span>")], ["content" => (("<span class=\"text-dark\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 84
$context["user"], "phone", [], "any", true, true, false, 84)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["user"], "phone", [], "any", false, false, false, 84), "N/A")) : ("N/A"))) . "</span>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 87
$context["user"], "isBlocked", [], "any", false, false, false, 87)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Blocked</span>") : ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>"))], ["content" => (("<small class=\"text-muted\">" . $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source,             // line 92
$context["user"], "createdAt", [], "any", false, false, false, 92), "M d, Y")) . "</small>")], ["content" => ((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_user_details", ["emailPrefix" => CoreExtension::getAttribute($this->env, $this->source,             // line 96
$context["user"], "emailPrefix", [], "any", false, false, false, 96)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ") . (((($tmp =             // line 97
(isset($context["canEdit"]) || array_key_exists("canEdit", $context) ? $context["canEdit"] : (function () { throw new RuntimeError('Variable "canEdit" does not exist.', 97, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 98
$context["user"], "isBlocked", [], "any", false, false, false, 98)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((((("<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Unblock User\" onclick=\"toggleUserStatus(" . CoreExtension::getAttribute($this->env, $this->source,             // line 99
$context["user"], "id", [], "any", false, false, false, 99)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["user"], "fullName", [], "any", false, false, false, 99)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["user"], "isBlocked", [], "any", false, false, false, 99)) . ")\"><i class=\"fas fa-unlock\"></i></button>")) : ((((((("<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Block User\" onclick=\"toggleUserStatus(" . CoreExtension::getAttribute($this->env, $this->source,             // line 100
$context["user"], "id", [], "any", false, false, false, 100)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["user"], "fullName", [], "any", false, false, false, 100)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["user"], "isBlocked", [], "any", false, false, false, 100)) . ")\"><i class=\"fas fa-lock\"></i></button>")))) : (""))) . (((($tmp =             // line 103
(isset($context["canDelete"]) || array_key_exists("canDelete", $context) ? $context["canDelete"] : (function () { throw new RuntimeError('Variable "canDelete" does not exist.', 103, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((("<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete User\" onclick=\"deleteUser(" . CoreExtension::getAttribute($this->env, $this->source,             // line 104
$context["user"], "id", [], "any", false, false, false, 104)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["user"], "fullName", [], "any", false, false, false, 104)) . "')\"><i class=\"fas fa-trash\"></i></button>")) : (""))) . "</div>")]];
            // line 109
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 109, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 109, $this->source); })())]]);
            // line 110
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['user'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 111
        yield "
        ";
        // line 112
        yield from $this->load("components/admin_table.html.twig", 112)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 113
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 113, $this->source); })()), "rows" =>         // line 114
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 114, $this->source); })()), "row_class" => "user-row", "empty_message" => "No users found", "empty_icon" => "fas fa-users", "empty_description" => "Users will appear here as they register.", "search_config" => ["fields" => [".user-name", ".user-email", ".user-country"]]]));
        // line 123
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/users/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  679 => 123,  677 => 114,  676 => 113,  675 => 112,  672 => 111,  666 => 110,  663 => 109,  661 => 104,  660 => 103,  659 => 100,  658 => 99,  657 => 98,  656 => 97,  654 => 96,  652 => 92,  651 => 87,  650 => 84,  649 => 81,  648 => 78,  647 => 75,  645 => 70,  644 => 69,  643 => 68,  640 => 65,  635 => 64,  633 => 63,  630 => 62,  628 => 52,  625 => 51,  612 => 50,  589 => 49,  187 => 128,  174 => 127,  163 => 49,  160 => 48,  158 => 41,  157 => 34,  156 => 27,  155 => 20,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Users Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Users Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Users</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Users Management',
    'page_icon': 'fas fa-users',
    'search_placeholder': 'Search users by name, email, or country...',
    'stats': [
        {
            'title': 'Total Users',
            'value': users|length,
            'icon': 'fas fa-users',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active Users',
            'value': users|filter(user => not user.isBlocked)|length,
            'icon': 'fas fa-user-check',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Blocked Users',
            'value': users|filter(user => user.isBlocked)|length,
            'icon': 'fas fa-user-slash',
            'color': '#dc3545',
            'gradient': 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': users|filter(user => user.createdAt and user.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-user-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Profile'},
            {'text': 'Name'},
            {'text': 'Email'},
            {'text': 'Country'},
            {'text': 'Phone'},
            {'text': 'Status'},
            {'text': 'Joined'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for user in users %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center justify-content-center\">
                        ' ~ (user.profilePicture ?
                            '<img src=\"' ~ asset('uploads/profiles/' ~ user.profilePicture) ~ '\" alt=\"' ~ user.fullName ~ '\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\">' :
                            '<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">' ~ user.firstName|first ~ user.lastName|first ~ '</div>'
                        ) ~ '
                    </div>'
                },
                {
                    'content': '<h6 class=\"user-name mb-0 font-weight-bold text-dark\">' ~ user.fullName ~ '</h6>'
                },
                {
                    'content': '<span class=\"user-email text-dark\">' ~ user.email ~ '</span>'
                },
                {
                    'content': '<span class=\"user-country text-dark\">' ~ (user.country|default('N/A')) ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark\">' ~ (user.phone|default('N/A')) ~ '</span>'
                },
                {
                    'content': user.isBlocked ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Blocked</span>' :
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>'
                },
                {
                    'content': '<small class=\"text-muted\">' ~ user.createdAt|date('M d, Y') ~ '</small>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_user_details', {'emailPrefix': user.emailPrefix}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ' ~ (canEdit ?
                            (user.isBlocked ?
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Unblock User\" onclick=\"toggleUserStatus(' ~ user.id ~ ', \\'' ~ user.fullName ~ '\\', ' ~ user.isBlocked ~ ')\"><i class=\"fas fa-unlock\"></i></button>' :
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Block User\" onclick=\"toggleUserStatus(' ~ user.id ~ ', \\'' ~ user.fullName ~ '\\', ' ~ user.isBlocked ~ ')\"><i class=\"fas fa-lock\"></i></button>'
                            ) : ''
                        ) ~
                        (canDelete ?
                            '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete User\" onclick=\"deleteUser(' ~ user.id ~ ', \\'' ~ user.fullName ~ '\\')\"><i class=\"fas fa-trash\"></i></button>' : ''
                        ) ~
                    '</div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'user-row',
            'empty_message': 'No users found',
            'empty_icon': 'fas fa-users',
            'empty_description': 'Users will appear here as they register.',
            'search_config': {
                'fields': ['.user-name', '.user-email', '.user-country']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.user-row',
        ['.user-name', '.user-email', '.user-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'block' : 'unblock';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// User management functions
function toggleUserStatus(userId, userName, isBlocked) {
    const action = isBlocked ? 'unblock' : 'block';
    showStatusModal(userName, isBlocked ? 'true' : 'false', function() {
        executeUserStatusToggle(userId);
    });
}

function deleteUser(userId, userName) {
    showDeleteModal(userName, function() {
        executeUserDelete(userId);
    });
}

// Actual execution functions
function executeUserStatusToggle(userId) {
    fetch(`/admin/users/\${userId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the user status'); // REMOVED
    });
}

function executeUserDelete(userId) {
    fetch(`/admin/users/\${userId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the user');
    });
}
</script>
{% endblock %}


", "admin/users/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\users\\index.html.twig");
    }
}
