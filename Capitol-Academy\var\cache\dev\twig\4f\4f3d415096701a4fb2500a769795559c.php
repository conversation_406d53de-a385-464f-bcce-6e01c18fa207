<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/partners/edit.html.twig */
class __TwigTemplate_6ea5e0c64d43cd362f209f1a27cdf0f4 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Partner - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 8
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 8, $this->source); })()), "flashes", [], "any", false, false, false, 8));
        foreach ($context['_seq'] as $context["type"] => $context["messages"]) {
            // line 9
            yield "        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable($context["messages"]);
            foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
                // line 10
                yield "            <div class=\"alert alert-";
                yield ((($context["type"] == "error")) ? ("danger") : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["type"], "html", null, true)));
                yield " alert-dismissible fade show\" role=\"alert\">
                <i class=\"fas fa-";
                // line 11
                yield ((($context["type"] == "success")) ? ("check-circle") : (((($context["type"] == "error")) ? ("exclamation-triangle") : ("info-circle"))));
                yield " me-2\"></i>
                ";
                // line 12
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
                yield "
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 16
            yield "    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['type'], $context['messages'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 17
        yield "
    <!-- Header Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"d-flex justify-content-between align-items-center\">
                <div>
                    <h1 class=\"h3 mb-0 text-gray-800\">
                        <i class=\"fas fa-edit me-2 text-primary\"></i>Edit Partner
                    </h1>
                    <p class=\"text-muted mb-0\">Update partner information for ";
        // line 26
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 26, $this->source); })()), "name", [], "any", false, false, false, 26), "html", null, true);
        yield "</p>
                </div>
                <a href=\"";
        // line 28
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_partners");
        yield "\" class=\"btn btn-secondary\">
                    <i class=\"fas fa-arrow-left me-2\"></i>Back to Partners
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class=\"row\">
        <div class=\"col-lg-8\">
            <div class=\"card shadow\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Partner Information</h6>
                </div>
                <div class=\"card-body\">
                    ";
        // line 43
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 43, $this->source); })()), 'form_start', ["attr" => ["enctype" => "multipart/form-data", "novalidate" => "novalidate"]]);
        yield "
                    
                    <div class=\"row\">
                        <div class=\"col-md-6 mb-3\">
                            ";
        // line 47
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 47, $this->source); })()), "name", [], "any", false, false, false, 47), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
        yield "
                            ";
        // line 48
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 48, $this->source); })()), "name", [], "any", false, false, false, 48), 'widget', ["attr" => ["class" => "form-control"]]);
        yield "
                            ";
        // line 49
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 49, $this->source); })()), "name", [], "any", false, false, false, 49), 'errors');
        yield "
                            ";
        // line 50
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 50, $this->source); })()), "name", [], "any", false, false, false, 50), "vars", [], "any", false, false, false, 50), "help", [], "any", false, false, false, 50)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 51
            yield "                                <div class=\"form-text\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 51, $this->source); })()), "name", [], "any", false, false, false, 51), "vars", [], "any", false, false, false, 51), "help", [], "any", false, false, false, 51), "html", null, true);
            yield "</div>
                            ";
        }
        // line 53
        yield "                        </div>
                        
                        <div class=\"col-md-6 mb-3\">
                            ";
        // line 56
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 56, $this->source); })()), "displayOrder", [], "any", false, false, false, 56), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
        yield "
                            ";
        // line 57
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 57, $this->source); })()), "displayOrder", [], "any", false, false, false, 57), 'widget', ["attr" => ["class" => "form-control"]]);
        yield "
                            ";
        // line 58
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 58, $this->source); })()), "displayOrder", [], "any", false, false, false, 58), 'errors');
        yield "
                            ";
        // line 59
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 59, $this->source); })()), "displayOrder", [], "any", false, false, false, 59), "vars", [], "any", false, false, false, 59), "help", [], "any", false, false, false, 59)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 60
            yield "                                <div class=\"form-text\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 60, $this->source); })()), "displayOrder", [], "any", false, false, false, 60), "vars", [], "any", false, false, false, 60), "help", [], "any", false, false, false, 60), "html", null, true);
            yield "</div>
                            ";
        }
        // line 62
        yield "                        </div>
                    </div>

                    <div class=\"mb-3\">
                        ";
        // line 66
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 66, $this->source); })()), "logoFile", [], "any", false, false, false, 66), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
        yield "
                        ";
        // line 67
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 67, $this->source); })()), "logoFile", [], "any", false, false, false, 67), 'widget', ["attr" => ["class" => "form-control"]]);
        yield "
                        ";
        // line 68
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 68, $this->source); })()), "logoFile", [], "any", false, false, false, 68), 'errors');
        yield "
                        ";
        // line 69
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 69, $this->source); })()), "logoFile", [], "any", false, false, false, 69), "vars", [], "any", false, false, false, 69), "help", [], "any", false, false, false, 69)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 70
            yield "                            <div class=\"form-text\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 70, $this->source); })()), "logoFile", [], "any", false, false, false, 70), "vars", [], "any", false, false, false, 70), "help", [], "any", false, false, false, 70), "html", null, true);
            yield "</div>
                        ";
        }
        // line 72
        yield "                        ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 72, $this->source); })()), "logoPath", [], "any", false, false, false, 72)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 73
            yield "                            <div class=\"form-text\">
                                <strong>Current logo:</strong> ";
            // line 74
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 74, $this->source); })()), "logoPath", [], "any", false, false, false, 74), "html", null, true);
            yield "
                                <br><small class=\"text-muted\">Leave empty to keep current logo</small>
                            </div>
                        ";
        }
        // line 78
        yield "                    </div>

                    <div class=\"mb-3\">
                        <div class=\"form-check\">
                            ";
        // line 82
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 82, $this->source); })()), "isActive", [], "any", false, false, false, 82), 'widget', ["attr" => ["class" => "form-check-input"]]);
        yield "
                            ";
        // line 83
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 83, $this->source); })()), "isActive", [], "any", false, false, false, 83), 'label', ["label_attr" => ["class" => "form-check-label"]]);
        yield "
                        </div>
                        ";
        // line 85
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 85, $this->source); })()), "isActive", [], "any", false, false, false, 85), 'errors');
        yield "
                        ";
        // line 86
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 86, $this->source); })()), "isActive", [], "any", false, false, false, 86), "vars", [], "any", false, false, false, 86), "help", [], "any", false, false, false, 86)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 87
            yield "                            <div class=\"form-text\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 87, $this->source); })()), "isActive", [], "any", false, false, false, 87), "vars", [], "any", false, false, false, 87), "help", [], "any", false, false, false, 87), "html", null, true);
            yield "</div>
                        ";
        }
        // line 89
        yield "                    </div>

                    <div class=\"d-flex justify-content-between\">
                        <a href=\"";
        // line 92
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_partners");
        yield "\" class=\"btn btn-secondary\">
                            <i class=\"fas fa-times me-2\"></i>Cancel
                        </a>
                        <button type=\"submit\" class=\"btn btn-primary\">
                            <i class=\"fas fa-save me-2\"></i>Update Partner
                        </button>
                    </div>

                    ";
        // line 100
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 100, $this->source); })()), 'form_end');
        yield "
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class=\"col-lg-4\">
            <div class=\"card shadow\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Current Logo</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"text-center\">
                        <div class=\"partner-preview mb-3\" style=\"min-height: 100px; border: 2px solid #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;\">
                            <img src=\"";
        // line 114
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 114, $this->source); })()), "logoUrl", [], "any", false, false, false, 114), "html", null, true);
        yield "\" alt=\"";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 114, $this->source); })()), "name", [], "any", false, false, false, 114), "html", null, true);
        yield "\" 
                                 class=\"img-fluid\" style=\"max-height: 80px;\"
                                 onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                        </div>
                        <div class=\"h6 text-dark\">";
        // line 118
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 118, $this->source); })()), "name", [], "any", false, false, false, 118), "html", null, true);
        yield "</div>
                        <small class=\"text-muted\">Display Order: ";
        // line 119
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 119, $this->source); })()), "displayOrder", [], "any", false, false, false, 119), "html", null, true);
        yield "</small>
                    </div>
                </div>
            </div>

            <div class=\"card shadow mt-3\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">New Logo Preview</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"text-center\">
                        <div class=\"partner-preview mb-3\" style=\"min-height: 100px; border: 2px dashed #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;\">
                            <div id=\"logoPreview\" class=\"text-muted\">
                                <i class=\"fas fa-image fa-2x mb-2\"></i>
                                <p class=\"mb-0\">New logo preview will appear here</p>
                            </div>
                        </div>
                        <div id=\"namePreview\" class=\"h6 text-dark\">";
        // line 136
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 136, $this->source); })()), "name", [], "any", false, false, false, 136), "html", null, true);
        yield "</div>
                    </div>
                    
                    <div class=\"mt-4\">
                        <h6 class=\"text-primary\">Tips:</h6>
                        <ul class=\"small text-muted\">
                            <li>Use PNG or SVG format for best quality</li>
                            <li>Recommended size: 200x100px</li>
                            <li>Keep file size under 2MB</li>
                            <li>Use transparent background if possible</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 155
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 156
        yield "<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('partner_name');
    const logoInput = document.getElementById('partner_logoFile');
    const namePreview = document.getElementById('namePreview');
    const logoPreview = document.getElementById('logoPreview');

    // Name preview
    nameInput.addEventListener('input', function() {
        const name = this.value.trim();
        namePreview.textContent = name || '";
        // line 166
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 166, $this->source); })()), "name", [], "any", false, false, false, 166), "html", null, true);
        yield "';
    });

    // Logo preview
    logoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                logoPreview.innerHTML = `<img src=\"\${e.target.result}\" alt=\"Logo Preview\" class=\"img-fluid\" style=\"max-height: 80px;\">`;
            };
            reader.readAsDataURL(file);
        } else {
            logoPreview.innerHTML = `
                <i class=\"fas fa-image fa-2x mb-2\"></i>
                <p class=\"mb-0\">New logo preview will appear here</p>
            `;
        }
    });

    // Enhanced form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        let errorMessages = [];

        // Validate required fields
        const requiredFields = [
            { id: 'partner_name', name: 'Partner Name' },
            { id: 'partner_displayOrder', name: 'Display Order' }
        ];

        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (element && !element.value.trim()) {
                isValid = false;
                element.classList.add('is-invalid');
                errorMessages.push(`\${field.name} is required`);
            } else if (element) {
                element.classList.remove('is-invalid');
            }
        });

        // Validate display order is a positive number
        const displayOrderField = document.getElementById('partner_displayOrder');
        if (displayOrderField && displayOrderField.value.trim()) {
            const displayOrder = parseInt(displayOrderField.value);
            if (isNaN(displayOrder) || displayOrder < 0) {
                isValid = false;
                displayOrderField.classList.add('is-invalid');
                errorMessages.push('Display Order must be a positive number');
            }
        }

        // Validate logo file if selected
        const logoField = document.getElementById('partner_logoFile');
        if (logoField && logoField.files.length > 0) {
            const file = logoField.files[0];
            const maxSize = 2 * 1024 * 1024; // 2MB
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'];

            if (file.size > maxSize) {
                isValid = false;
                logoField.classList.add('is-invalid');
                errorMessages.push('Logo file must be smaller than 2MB');
            }

            if (!allowedTypes.includes(file.type)) {
                isValid = false;
                logoField.classList.add('is-invalid');
                errorMessages.push('Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
            }
        }

        if (!isValid) {
            e.preventDefault();
            showValidationErrors(errorMessages);
        } else {
            // Show loading state
            const submitBtn = form.querySelector('button[type=\"submit\"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Updating Partner...';
        }
    });

    // Function to show validation errors
    function showValidationErrors(errors) {
        const errorHtml = `
            <div class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">
                <i class=\"fas fa-exclamation-triangle me-2\"></i>
                <strong>Please fix the following errors:</strong>
                <ul class=\"mb-0 mt-2\">
                    \${errors.map(error => `<li>\${error}</li>`).join('')}
                </ul>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        `;

        // Remove existing error alerts
        document.querySelectorAll('.alert-danger').forEach(alert => alert.remove());

        // Add new error alert at the top of the form
        const formCard = document.querySelector('.card');
        formCard.insertAdjacentHTML('beforebegin', errorHtml);

        // Scroll to top to show errors
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/partners/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  411 => 166,  399 => 156,  386 => 155,  357 => 136,  337 => 119,  333 => 118,  324 => 114,  307 => 100,  296 => 92,  291 => 89,  285 => 87,  283 => 86,  279 => 85,  274 => 83,  270 => 82,  264 => 78,  257 => 74,  254 => 73,  251 => 72,  245 => 70,  243 => 69,  239 => 68,  235 => 67,  231 => 66,  225 => 62,  219 => 60,  217 => 59,  213 => 58,  209 => 57,  205 => 56,  200 => 53,  194 => 51,  192 => 50,  188 => 49,  184 => 48,  180 => 47,  173 => 43,  155 => 28,  150 => 26,  139 => 17,  133 => 16,  123 => 12,  119 => 11,  114 => 10,  109 => 9,  105 => 8,  101 => 6,  88 => 5,  65 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Partner - Capitol Academy Admin{% endblock %}

{% block body %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for type, messages in app.flashes %}
        {% for message in messages %}
            <div class=\"alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show\" role=\"alert\">
                <i class=\"fas fa-{{ type == 'success' ? 'check-circle' : (type == 'error' ? 'exclamation-triangle' : 'info-circle') }} me-2\"></i>
                {{ message }}
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        {% endfor %}
    {% endfor %}

    <!-- Header Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"d-flex justify-content-between align-items-center\">
                <div>
                    <h1 class=\"h3 mb-0 text-gray-800\">
                        <i class=\"fas fa-edit me-2 text-primary\"></i>Edit Partner
                    </h1>
                    <p class=\"text-muted mb-0\">Update partner information for {{ partner.name }}</p>
                </div>
                <a href=\"{{ path('admin_partners') }}\" class=\"btn btn-secondary\">
                    <i class=\"fas fa-arrow-left me-2\"></i>Back to Partners
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class=\"row\">
        <div class=\"col-lg-8\">
            <div class=\"card shadow\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Partner Information</h6>
                </div>
                <div class=\"card-body\">
                    {{ form_start(form, {'attr': {'enctype': 'multipart/form-data', 'novalidate': 'novalidate'}}) }}
                    
                    <div class=\"row\">
                        <div class=\"col-md-6 mb-3\">
                            {{ form_label(form.name, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                            {{ form_widget(form.name, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.name) }}
                            {% if form.name.vars.help %}
                                <div class=\"form-text\">{{ form.name.vars.help }}</div>
                            {% endif %}
                        </div>
                        
                        <div class=\"col-md-6 mb-3\">
                            {{ form_label(form.displayOrder, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                            {{ form_widget(form.displayOrder, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.displayOrder) }}
                            {% if form.displayOrder.vars.help %}
                                <div class=\"form-text\">{{ form.displayOrder.vars.help }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class=\"mb-3\">
                        {{ form_label(form.logoFile, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                        {{ form_widget(form.logoFile, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.logoFile) }}
                        {% if form.logoFile.vars.help %}
                            <div class=\"form-text\">{{ form.logoFile.vars.help }}</div>
                        {% endif %}
                        {% if partner.logoPath %}
                            <div class=\"form-text\">
                                <strong>Current logo:</strong> {{ partner.logoPath }}
                                <br><small class=\"text-muted\">Leave empty to keep current logo</small>
                            </div>
                        {% endif %}
                    </div>

                    <div class=\"mb-3\">
                        <div class=\"form-check\">
                            {{ form_widget(form.isActive, {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(form.isActive, null, {'label_attr': {'class': 'form-check-label'}}) }}
                        </div>
                        {{ form_errors(form.isActive) }}
                        {% if form.isActive.vars.help %}
                            <div class=\"form-text\">{{ form.isActive.vars.help }}</div>
                        {% endif %}
                    </div>

                    <div class=\"d-flex justify-content-between\">
                        <a href=\"{{ path('admin_partners') }}\" class=\"btn btn-secondary\">
                            <i class=\"fas fa-times me-2\"></i>Cancel
                        </a>
                        <button type=\"submit\" class=\"btn btn-primary\">
                            <i class=\"fas fa-save me-2\"></i>Update Partner
                        </button>
                    </div>

                    {{ form_end(form) }}
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class=\"col-lg-4\">
            <div class=\"card shadow\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Current Logo</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"text-center\">
                        <div class=\"partner-preview mb-3\" style=\"min-height: 100px; border: 2px solid #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;\">
                            <img src=\"{{ partner.logoUrl }}\" alt=\"{{ partner.name }}\" 
                                 class=\"img-fluid\" style=\"max-height: 80px;\"
                                 onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                        </div>
                        <div class=\"h6 text-dark\">{{ partner.name }}</div>
                        <small class=\"text-muted\">Display Order: {{ partner.displayOrder }}</small>
                    </div>
                </div>
            </div>

            <div class=\"card shadow mt-3\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">New Logo Preview</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"text-center\">
                        <div class=\"partner-preview mb-3\" style=\"min-height: 100px; border: 2px dashed #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;\">
                            <div id=\"logoPreview\" class=\"text-muted\">
                                <i class=\"fas fa-image fa-2x mb-2\"></i>
                                <p class=\"mb-0\">New logo preview will appear here</p>
                            </div>
                        </div>
                        <div id=\"namePreview\" class=\"h6 text-dark\">{{ partner.name }}</div>
                    </div>
                    
                    <div class=\"mt-4\">
                        <h6 class=\"text-primary\">Tips:</h6>
                        <ul class=\"small text-muted\">
                            <li>Use PNG or SVG format for best quality</li>
                            <li>Recommended size: 200x100px</li>
                            <li>Keep file size under 2MB</li>
                            <li>Use transparent background if possible</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('partner_name');
    const logoInput = document.getElementById('partner_logoFile');
    const namePreview = document.getElementById('namePreview');
    const logoPreview = document.getElementById('logoPreview');

    // Name preview
    nameInput.addEventListener('input', function() {
        const name = this.value.trim();
        namePreview.textContent = name || '{{ partner.name }}';
    });

    // Logo preview
    logoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                logoPreview.innerHTML = `<img src=\"\${e.target.result}\" alt=\"Logo Preview\" class=\"img-fluid\" style=\"max-height: 80px;\">`;
            };
            reader.readAsDataURL(file);
        } else {
            logoPreview.innerHTML = `
                <i class=\"fas fa-image fa-2x mb-2\"></i>
                <p class=\"mb-0\">New logo preview will appear here</p>
            `;
        }
    });

    // Enhanced form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        let errorMessages = [];

        // Validate required fields
        const requiredFields = [
            { id: 'partner_name', name: 'Partner Name' },
            { id: 'partner_displayOrder', name: 'Display Order' }
        ];

        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (element && !element.value.trim()) {
                isValid = false;
                element.classList.add('is-invalid');
                errorMessages.push(`\${field.name} is required`);
            } else if (element) {
                element.classList.remove('is-invalid');
            }
        });

        // Validate display order is a positive number
        const displayOrderField = document.getElementById('partner_displayOrder');
        if (displayOrderField && displayOrderField.value.trim()) {
            const displayOrder = parseInt(displayOrderField.value);
            if (isNaN(displayOrder) || displayOrder < 0) {
                isValid = false;
                displayOrderField.classList.add('is-invalid');
                errorMessages.push('Display Order must be a positive number');
            }
        }

        // Validate logo file if selected
        const logoField = document.getElementById('partner_logoFile');
        if (logoField && logoField.files.length > 0) {
            const file = logoField.files[0];
            const maxSize = 2 * 1024 * 1024; // 2MB
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'];

            if (file.size > maxSize) {
                isValid = false;
                logoField.classList.add('is-invalid');
                errorMessages.push('Logo file must be smaller than 2MB');
            }

            if (!allowedTypes.includes(file.type)) {
                isValid = false;
                logoField.classList.add('is-invalid');
                errorMessages.push('Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
            }
        }

        if (!isValid) {
            e.preventDefault();
            showValidationErrors(errorMessages);
        } else {
            // Show loading state
            const submitBtn = form.querySelector('button[type=\"submit\"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Updating Partner...';
        }
    });

    // Function to show validation errors
    function showValidationErrors(errors) {
        const errorHtml = `
            <div class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">
                <i class=\"fas fa-exclamation-triangle me-2\"></i>
                <strong>Please fix the following errors:</strong>
                <ul class=\"mb-0 mt-2\">
                    \${errors.map(error => `<li>\${error}</li>`).join('')}
                </ul>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        `;

        // Remove existing error alerts
        document.querySelectorAll('.alert-danger').forEach(alert => alert.remove());

        // Add new error alert at the top of the form
        const formCard = document.querySelector('.card');
        formCard.insertAdjacentHTML('beforebegin', errorHtml);

        // Scroll to top to show errors
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
});
</script>
{% endblock %}
", "admin/partners/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\partners\\edit.html.twig");
    }
}
