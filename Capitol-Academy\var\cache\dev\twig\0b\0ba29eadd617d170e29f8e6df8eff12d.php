<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/dashboard/index.html.twig */
class __TwigTemplate_34d1aa48e5f52ba6d8257386d6d9ffab extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/dashboard/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/dashboard/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Admin Dashboard - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid\">
    <!-- Welcome Header -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card bg-gradient-primary text-white\">
                <div class=\"card-body\">
                    <div class=\"row align-items-center\">
                        <div class=\"col\">
                            <h2 class=\"mb-2\">Welcome to Capitol Academy Admin</h2>
                            <p class=\"mb-0\">Trading Education Platform Management Dashboard</p>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-graduation-cap fa-3x opacity-75\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class=\"row mb-4\">
        <!-- Users Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-primary shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">Total Users</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">";
        // line 35
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 35, $this->source); })()), "users", [], "any", false, false, false, 35), "total", [], "any", false, false, false, 35)), "html", null, true);
        yield "</div>
                            <div class=\"text-xs text-success\">
                                <i class=\"fas fa-arrow-up\"></i> ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 37, $this->source); })()), "users", [], "any", false, false, false, 37), "new_this_month", [], "any", false, false, false, 37), "html", null, true);
        yield " new this month
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-users fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-success shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">Total Revenue</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">\$";
        // line 55
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 55, $this->source); })()), "orders", [], "any", false, false, false, 55), "total_revenue", [], "any", false, false, false, 55), 2), "html", null, true);
        yield "</div>
                            <div class=\"text-xs text-info\">
                                <i class=\"fas fa-calendar\"></i> \$";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 57, $this->source); })()), "orders", [], "any", false, false, false, 57), "monthly_revenue", [], "any", false, false, false, 57), 2), "html", null, true);
        yield " this month
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-dollar-sign fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-info shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">Orders</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">";
        // line 75
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 75, $this->source); })()), "orders", [], "any", false, false, false, 75), "total", [], "any", false, false, false, 75)), "html", null, true);
        yield "</div>
                            <div class=\"text-xs text-success\">
                                ";
        // line 77
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 77, $this->source); })()), "orders", [], "any", false, false, false, 77), "conversion_rate", [], "any", false, false, false, 77), "html", null, true);
        yield "% conversion rate
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-shopping-cart fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-warning shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">Content Items</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">
                                ";
        // line 96
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 96, $this->source); })()), "content", [], "any", false, false, false, 96), "videos", [], "any", false, false, false, 96), "total", [], "any", false, false, false, 96) + CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 96, $this->source); })()), "content", [], "any", false, false, false, 96), "courses", [], "any", false, false, false, 96), "total", [], "any", false, false, false, 96)), "html", null, true);
        yield "
                            </div>
                            <div class=\"text-xs text-info\">
                                ";
        // line 99
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 99, $this->source); })()), "content", [], "any", false, false, false, 99), "videos", [], "any", false, false, false, 99), "total", [], "any", false, false, false, 99), "html", null, true);
        yield " videos, ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 99, $this->source); })()), "content", [], "any", false, false, false, 99), "courses", [], "any", false, false, false, 99), "total", [], "any", false, false, false, 99), "html", null, true);
        yield " courses
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-video fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class=\"row\">
        <!-- Revenue Chart -->
        <div class=\"col-xl-8 col-lg-7\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Revenue Overview</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"chart-area\">
                        <canvas id=\"revenueChart\"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Breakdown -->
        <div class=\"col-xl-4 col-lg-5\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Content Breakdown</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"chart-pie pt-4 pb-2\">
                        <canvas id=\"contentChart\"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Popular Content -->
    <div class=\"row\">
        <!-- Recent Orders -->
        <div class=\"col-lg-6\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Recent Orders</h6>
                    <a href=\"";
        // line 149
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_order_index");
        yield "\" class=\"btn btn-sm btn-primary\">View All</a>
                </div>
                <div class=\"card-body\">
                    ";
        // line 152
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["recent_activity"]) || array_key_exists("recent_activity", $context) ? $context["recent_activity"] : (function () { throw new RuntimeError('Variable "recent_activity" does not exist.', 152, $this->source); })()), "recent_orders", [], "any", false, false, false, 152)) > 0)) {
            // line 153
            yield "                        <div class=\"table-responsive\">
                            <table class=\"table table-sm\">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ";
            // line 164
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["recent_activity"]) || array_key_exists("recent_activity", $context) ? $context["recent_activity"] : (function () { throw new RuntimeError('Variable "recent_activity" does not exist.', 164, $this->source); })()), "recent_orders", [], "any", false, false, false, 164));
            foreach ($context['_seq'] as $context["_key"] => $context["order"]) {
                // line 165
                yield "                                        <tr>
                                            <td>
                                                <a href=\"";
                // line 167
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_order_show", ["id" => CoreExtension::getAttribute($this->env, $this->source, $context["order"], "id", [], "any", false, false, false, 167)]), "html", null, true);
                yield "\" class=\"text-decoration-none\">
                                                    #";
                // line 168
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "orderNumber", [], "any", false, false, false, 168), "html", null, true);
                yield "
                                                </a>
                                            </td>
                                            <td>";
                // line 171
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["order"], "user", [], "any", false, false, false, 171), "firstName", [], "any", false, false, false, 171), "html", null, true);
                yield " ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["order"], "user", [], "any", false, false, false, 171), "lastName", [], "any", false, false, false, 171), "html", null, true);
                yield "</td>
                                            <td>\$";
                // line 172
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "totalPrice", [], "any", false, false, false, 172), 2), "html", null, true);
                yield "</td>
                                            <td>
                                                ";
                // line 174
                $context["status_class"] = "secondary";
                // line 175
                yield "                                                ";
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["order"], "paymentStatus", [], "any", false, false, false, 175) == "completed")) {
                    // line 176
                    yield "                                                    ";
                    $context["status_class"] = "success";
                    // line 177
                    yield "                                                ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source, $context["order"], "paymentStatus", [], "any", false, false, false, 177) == "pending")) {
                    // line 178
                    yield "                                                    ";
                    $context["status_class"] = "warning";
                    // line 179
                    yield "                                                ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source, $context["order"], "paymentStatus", [], "any", false, false, false, 179) == "failed")) {
                    // line 180
                    yield "                                                    ";
                    $context["status_class"] = "danger";
                    // line 181
                    yield "                                                ";
                }
                // line 182
                yield "                                                <span class=\"badge bg-";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["status_class"]) || array_key_exists("status_class", $context) ? $context["status_class"] : (function () { throw new RuntimeError('Variable "status_class" does not exist.', 182, $this->source); })()), "html", null, true);
                yield "\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "statusLabel", [], "any", false, false, false, 182), "html", null, true);
                yield "</span>
                                            </td>
                                        </tr>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['order'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 186
            yield "                                </tbody>
                            </table>
                        </div>
                    ";
        } else {
            // line 190
            yield "                        <p class=\"text-muted text-center py-3\">No recent orders</p>
                    ";
        }
        // line 192
        yield "                </div>
            </div>
        </div>

        <!-- Popular Content -->
        <div class=\"col-lg-6\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Popular Content</h6>
                </div>
                <div class=\"card-body\">
                    ";
        // line 203
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["popular_content"]) || array_key_exists("popular_content", $context) ? $context["popular_content"] : (function () { throw new RuntimeError('Variable "popular_content" does not exist.', 203, $this->source); })())) > 0)) {
            // line 204
            yield "                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (isset($context["popular_content"]) || array_key_exists("popular_content", $context) ? $context["popular_content"] : (function () { throw new RuntimeError('Variable "popular_content" does not exist.', 204, $this->source); })()), 0, 5));
            foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
                // line 205
                yield "                            <div class=\"d-flex align-items-center mb-3\">
                                <div class=\"mr-3\">
                                    ";
                // line 207
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 207) == "video")) {
                    // line 208
                    yield "                                        <i class=\"fas fa-video text-primary\"></i>
                                    ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source,                 // line 209
$context["item"], "type", [], "any", false, false, false, 209) == "course")) {
                    // line 210
                    yield "                                        <i class=\"fas fa-graduation-cap text-success\"></i>
                                    ";
                } else {
                    // line 212
                    yield "                                        <i class=\"fas fa-layer-group text-info\"></i>
                                    ";
                }
                // line 214
                yield "                                </div>
                                <div class=\"flex-grow-1\">
                                    <div class=\"font-weight-bold\">";
                // line 216
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 216), "html", null, true);
                yield "</div>
                                    <div class=\"text-xs text-muted\">";
                // line 217
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 217)), "html", null, true);
                yield "</div>
                                </div>
                                <div class=\"text-right\">
                                    <div class=\"font-weight-bold\">";
                // line 220
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "count", [], "any", false, false, false, 220), "html", null, true);
                yield " sales</div>
                                    <div class=\"text-xs text-success\">\$";
                // line 221
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "revenue", [], "any", false, false, false, 221), 2), "html", null, true);
                yield "</div>
                                </div>
                            </div>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['item'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 225
            yield "                    ";
        } else {
            // line 226
            yield "                        <p class=\"text-muted text-center py-3\">No sales data available</p>
                    ";
        }
        // line 228
        yield "                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Quick Actions</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"row\">
                        <div class=\"col-md-3 mb-3\">
                            <a href=\"";
        // line 243
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_new");
        yield "\" class=\"btn btn-primary btn-block\">
                                <i class=\"fas fa-plus me-2\"></i>Add New Video
                            </a>
                        </div>
                        <div class=\"col-md-3 mb-3\">
                            <a href=\"";
        // line 248
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses_create");
        yield "\" class=\"btn btn-success btn-block\">
                                <i class=\"fas fa-plus me-2\"></i>Add New Course
                            </a>
                        </div>

                        <div class=\"col-md-3 mb-3\">
                            <a href=\"";
        // line 254
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_order_index");
        yield "\" class=\"btn btn-warning btn-block\">
                                <i class=\"fas fa-chart-line me-2\"></i>View Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 266
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 267
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>
    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [";
        // line 275
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["revenue_data"]) || array_key_exists("revenue_data", $context) ? $context["revenue_data"] : (function () { throw new RuntimeError('Variable "revenue_data" does not exist.', 275, $this->source); })()));
        $context['loop'] = [
          'parent' => $context['_parent'],
          'index0' => 0,
          'index'  => 1,
          'first'  => true,
        ];
        if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
            $length = count($context['_seq']);
            $context['loop']['revindex0'] = $length - 1;
            $context['loop']['revindex'] = $length;
            $context['loop']['length'] = $length;
            $context['loop']['last'] = 1 === $length;
        }
        foreach ($context['_seq'] as $context["_key"] => $context["data"]) {
            yield "'";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["data"], "month", [], "any", false, false, false, 275), "html", null, true);
            yield "'";
            if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "last", [], "any", false, false, false, 275)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield ",";
            }
            ++$context['loop']['index0'];
            ++$context['loop']['index'];
            $context['loop']['first'] = false;
            if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                --$context['loop']['revindex0'];
                --$context['loop']['revindex'];
                $context['loop']['last'] = 0 === $context['loop']['revindex0'];
            }
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['data'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        yield "],
                datasets: [{
                    label: 'Revenue',
                    data: [";
        // line 278
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["revenue_data"]) || array_key_exists("revenue_data", $context) ? $context["revenue_data"] : (function () { throw new RuntimeError('Variable "revenue_data" does not exist.', 278, $this->source); })()));
        $context['loop'] = [
          'parent' => $context['_parent'],
          'index0' => 0,
          'index'  => 1,
          'first'  => true,
        ];
        if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
            $length = count($context['_seq']);
            $context['loop']['revindex0'] = $length - 1;
            $context['loop']['revindex'] = $length;
            $context['loop']['length'] = $length;
            $context['loop']['last'] = 1 === $length;
        }
        foreach ($context['_seq'] as $context["_key"] => $context["data"]) {
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["data"], "revenue", [], "any", false, false, false, 278), "html", null, true);
            if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "last", [], "any", false, false, false, 278)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield ",";
            }
            ++$context['loop']['index0'];
            ++$context['loop']['index'];
            $context['loop']['first'] = false;
            if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                --$context['loop']['revindex0'];
                --$context['loop']['revindex'];
                $context['loop']['last'] = 0 === $context['loop']['revindex0'];
            }
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['data'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        yield "],
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '\$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Content Chart
        const contentCtx = document.getElementById('contentChart').getContext('2d');
        const contentChart = new Chart(contentCtx, {
            type: 'doughnut',
            data: {
                labels: ['Videos', 'Courses'],
                datasets: [{
                    data: [
                        ";
        // line 308
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 308, $this->source); })()), "content", [], "any", false, false, false, 308), "videos", [], "any", false, false, false, 308), "total", [], "any", false, false, false, 308), "html", null, true);
        yield ",
                        ";
        // line 309
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 309, $this->source); })()), "content", [], "any", false, false, false, 309), "courses", [], "any", false, false, false, 309), "total", [], "any", false, false, false, 309), "html", null, true);
        yield "
                    ],
                    backgroundColor: ['#4e73df', '#1cc88a'],
                    hoverBackgroundColor: ['#2e59d9', '#17a673'],
                    hoverBorderColor: \"rgba(234, 236, 244, 1)\",
                }],
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/dashboard/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  630 => 309,  626 => 308,  562 => 278,  523 => 275,  511 => 267,  498 => 266,  476 => 254,  467 => 248,  459 => 243,  442 => 228,  438 => 226,  435 => 225,  425 => 221,  421 => 220,  415 => 217,  411 => 216,  407 => 214,  403 => 212,  399 => 210,  397 => 209,  394 => 208,  392 => 207,  388 => 205,  383 => 204,  381 => 203,  368 => 192,  364 => 190,  358 => 186,  345 => 182,  342 => 181,  339 => 180,  336 => 179,  333 => 178,  330 => 177,  327 => 176,  324 => 175,  322 => 174,  317 => 172,  311 => 171,  305 => 168,  301 => 167,  297 => 165,  293 => 164,  280 => 153,  278 => 152,  272 => 149,  217 => 99,  211 => 96,  189 => 77,  184 => 75,  163 => 57,  158 => 55,  137 => 37,  132 => 35,  101 => 6,  88 => 5,  65 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Admin Dashboard - Capitol Academy{% endblock %}

{% block body %}
<div class=\"container-fluid\">
    <!-- Welcome Header -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card bg-gradient-primary text-white\">
                <div class=\"card-body\">
                    <div class=\"row align-items-center\">
                        <div class=\"col\">
                            <h2 class=\"mb-2\">Welcome to Capitol Academy Admin</h2>
                            <p class=\"mb-0\">Trading Education Platform Management Dashboard</p>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-graduation-cap fa-3x opacity-75\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class=\"row mb-4\">
        <!-- Users Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-primary shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">Total Users</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.users.total|number_format }}</div>
                            <div class=\"text-xs text-success\">
                                <i class=\"fas fa-arrow-up\"></i> {{ stats.users.new_this_month }} new this month
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-users fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-success shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">Total Revenue</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">\${{ stats.orders.total_revenue|number_format(2) }}</div>
                            <div class=\"text-xs text-info\">
                                <i class=\"fas fa-calendar\"></i> \${{ stats.orders.monthly_revenue|number_format(2) }} this month
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-dollar-sign fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-info shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">Orders</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.orders.total|number_format }}</div>
                            <div class=\"text-xs text-success\">
                                {{ stats.orders.conversion_rate }}% conversion rate
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-shopping-cart fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Stats -->
        <div class=\"col-xl-3 col-md-6\">
            <div class=\"card border-left-warning shadow h-100 py-2\">
                <div class=\"card-body\">
                    <div class=\"row no-gutters align-items-center\">
                        <div class=\"col mr-2\">
                            <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">Content Items</div>
                            <div class=\"h5 mb-0 font-weight-bold text-gray-800\">
                                {{ stats.content.videos.total + stats.content.courses.total }}
                            </div>
                            <div class=\"text-xs text-info\">
                                {{ stats.content.videos.total }} videos, {{ stats.content.courses.total }} courses
                            </div>
                        </div>
                        <div class=\"col-auto\">
                            <i class=\"fas fa-video fa-2x text-gray-300\"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class=\"row\">
        <!-- Revenue Chart -->
        <div class=\"col-xl-8 col-lg-7\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Revenue Overview</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"chart-area\">
                        <canvas id=\"revenueChart\"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Breakdown -->
        <div class=\"col-xl-4 col-lg-5\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Content Breakdown</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"chart-pie pt-4 pb-2\">
                        <canvas id=\"contentChart\"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Popular Content -->
    <div class=\"row\">
        <!-- Recent Orders -->
        <div class=\"col-lg-6\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Recent Orders</h6>
                    <a href=\"{{ path('admin_order_index') }}\" class=\"btn btn-sm btn-primary\">View All</a>
                </div>
                <div class=\"card-body\">
                    {% if recent_activity.recent_orders|length > 0 %}
                        <div class=\"table-responsive\">
                            <table class=\"table table-sm\">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in recent_activity.recent_orders %}
                                        <tr>
                                            <td>
                                                <a href=\"{{ path('admin_order_show', {id: order.id}) }}\" class=\"text-decoration-none\">
                                                    #{{ order.orderNumber }}
                                                </a>
                                            </td>
                                            <td>{{ order.user.firstName }} {{ order.user.lastName }}</td>
                                            <td>\${{ order.totalPrice|number_format(2) }}</td>
                                            <td>
                                                {% set status_class = 'secondary' %}
                                                {% if order.paymentStatus == 'completed' %}
                                                    {% set status_class = 'success' %}
                                                {% elseif order.paymentStatus == 'pending' %}
                                                    {% set status_class = 'warning' %}
                                                {% elseif order.paymentStatus == 'failed' %}
                                                    {% set status_class = 'danger' %}
                                                {% endif %}
                                                <span class=\"badge bg-{{ status_class }}\">{{ order.statusLabel }}</span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class=\"text-muted text-center py-3\">No recent orders</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Popular Content -->
        <div class=\"col-lg-6\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Popular Content</h6>
                </div>
                <div class=\"card-body\">
                    {% if popular_content|length > 0 %}
                        {% for item in popular_content|slice(0, 5) %}
                            <div class=\"d-flex align-items-center mb-3\">
                                <div class=\"mr-3\">
                                    {% if item.type == 'video' %}
                                        <i class=\"fas fa-video text-primary\"></i>
                                    {% elseif item.type == 'course' %}
                                        <i class=\"fas fa-graduation-cap text-success\"></i>
                                    {% else %}
                                        <i class=\"fas fa-layer-group text-info\"></i>
                                    {% endif %}
                                </div>
                                <div class=\"flex-grow-1\">
                                    <div class=\"font-weight-bold\">{{ item.title }}</div>
                                    <div class=\"text-xs text-muted\">{{ item.type|title }}</div>
                                </div>
                                <div class=\"text-right\">
                                    <div class=\"font-weight-bold\">{{ item.count }} sales</div>
                                    <div class=\"text-xs text-success\">\${{ item.revenue|number_format(2) }}</div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class=\"text-muted text-center py-3\">No sales data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"card shadow mb-4\">
                <div class=\"card-header py-3\">
                    <h6 class=\"m-0 font-weight-bold text-primary\">Quick Actions</h6>
                </div>
                <div class=\"card-body\">
                    <div class=\"row\">
                        <div class=\"col-md-3 mb-3\">
                            <a href=\"{{ path('admin_video_new') }}\" class=\"btn btn-primary btn-block\">
                                <i class=\"fas fa-plus me-2\"></i>Add New Video
                            </a>
                        </div>
                        <div class=\"col-md-3 mb-3\">
                            <a href=\"{{ path('admin_courses_create') }}\" class=\"btn btn-success btn-block\">
                                <i class=\"fas fa-plus me-2\"></i>Add New Course
                            </a>
                        </div>

                        <div class=\"col-md-3 mb-3\">
                            <a href=\"{{ path('admin_order_index') }}\" class=\"btn btn-warning btn-block\">
                                <i class=\"fas fa-chart-line me-2\"></i>View Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>
    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [{% for data in revenue_data %}'{{ data.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Revenue',
                    data: [{% for data in revenue_data %}{{ data.revenue }}{% if not loop.last %},{% endif %}{% endfor %}],
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '\$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Content Chart
        const contentCtx = document.getElementById('contentChart').getContext('2d');
        const contentChart = new Chart(contentCtx, {
            type: 'doughnut',
            data: {
                labels: ['Videos', 'Courses'],
                datasets: [{
                    data: [
                        {{ stats.content.videos.total }},
                        {{ stats.content.courses.total }}
                    ],
                    backgroundColor: ['#4e73df', '#1cc88a'],
                    hoverBackgroundColor: ['#2e59d9', '#17a673'],
                    hoverBorderColor: \"rgba(234, 236, 244, 1)\",
                }],
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    </script>
{% endblock %}
", "admin/dashboard/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\dashboard\\index.html.twig");
    }
}
