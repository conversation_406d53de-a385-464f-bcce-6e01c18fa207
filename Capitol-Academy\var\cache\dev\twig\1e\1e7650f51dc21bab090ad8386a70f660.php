<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/plans/create.html.twig */
class __TwigTemplate_ec3f6f25a4ef63137744a5b0b3e0fb85 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/create.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/create.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Plan - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create New Plan";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\">Plans</a></li>
<li class=\"breadcrumb-item active\">Create Plan</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-layer-group mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Plan
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Plans Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("plan_create"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Plan Code and Title Row -->
                            <div class=\"row\">
                                <!-- Plan Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                            Plan Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               placeholder=\"e.g., PLAN001, VB200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               title=\"Format: 2-4 letters followed by 1-4 numbers (e.g., PLAN001, VB200)\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid plan code (e.g., PLAN001, VB200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                            Plan Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter plan title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a plan title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Plan Details Row -->
                            <div class=\"row\">
                                <!-- Access Duration -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-calendar-alt text-primary mr-1\"></i>
                                            Access Duration (Days)
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               placeholder=\"e.g., 30, 60, 90\"
                                               min=\"1\"
                                               max=\"3650\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">

                                    </div>
                                </div>
                                
                                <!-- Price -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               placeholder=\"0.00\"
                                               step=\"0.01\"
                                               min=\"0\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\"
                                               required>
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Plan Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"4\"
                                          placeholder=\"Enter detailed plan description...\"
                                          required
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a plan description.
                                </div>
                            </div>
                            
                            <!-- Video Selection -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-video text-primary mr-1\"></i>
                                    Select Videos <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"video-selection-container\">
                                    <div class=\"input-group mb-2 video-selection-item\">
                                        <select class=\"form-select enhanced-dropdown video-dropdown\"
                                                name=\"videos[]\"
                                                required
                                                aria-describedby=\"videos_help videos_error\"
                                                aria-label=\"Select videos for this plan\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a video...</option>
                                            ";
        // line 188
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 188, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
            // line 189
            yield "                                                <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 189), "html", null, true);
            yield "\"
                                                        data-category=\"";
            // line 190
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", true, true, false, 190)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 190), "General")) : ("General")), "html", null, true);
            yield "\">
                                                    ";
            // line 191
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 191), "html", null, true);
            yield "
                                                    ";
            // line 192
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 192)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield " - ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 192), "html", null, true);
            }
            // line 193
            yield "                                                    ";
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isFree", [], "any", false, false, false, 193)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield "(Free)";
            } else {
                yield "(\$";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "price", [], "any", false, false, false, 193), "html", null, true);
                yield ")";
            }
            // line 194
            yield "                                                </option>
                                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 196
        yield "                                        </select>
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-video-selection\" style=\"border-radius: 0 8px 8px 0; border: 2px solid #28a745; background: #28a745; color: white; transition: all 0.3s ease;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div id=\"videos_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                    Select videos to include in this plan. Use arrow keys to navigate options.
                                </div>
                                <div id=\"videos_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                    Please select at least one video.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Click the + button to add more video selections. Each video can only be selected once.
                                </small>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Plan
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 228
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>

<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-field:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* Select2 Custom Styling - Force consistent height with other form fields */
.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
    padding-left: 8px !important;
    padding-right: 8px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.select2-container--bootstrap4 .select2-selection--multiple:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice {
    background-color: #1e3c72 !important;
    border: 1px solid #1e3c72 !important;
    color: white !important;
    border-radius: 4px !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove {
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove:hover {
    color: #ff6b6b !important;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Form Label Icons */
.form-label i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Professional Input Group Styling */
.input-group-append .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    height: calc(1.6em + 1.25rem + 4px);
    border: 2px solid #ced4da;
    border-left: none;
}

.input-group .form-select {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Remove Video Button Styling */
.btn-danger:hover {
    background: #c82333 !important;
    border-color: #c82333 !important;
}

.btn-success:hover {
    background: #218838 !important;
    border-color: #218838 !important;
}

/* Select2 Custom Styling for Video Dropdowns */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px 0 0 8px !important;
    font-size: 1rem !important;
    background-color: #fff !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 12px !important;
    padding-right: 12px !important;
    line-height: calc(1.6em + 1.25rem) !important;
    color: #495057 !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.select2-dropdown {
    border: 1px solid #011a2d !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(1, 26, 45, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted[aria-selected] {
    background-color: rgba(1, 26, 45, 0.1) !important;
    color: #011a2d !important;
}

.video-selection-item .select2-container {
    flex: 1;
}

.input-group .select2-container .select2-selection--single {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}
</style>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<script>
\$(document).ready(function() {
    // Store video options for dynamic creation
    var videoOptions = '';
    ";
        // line 420
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 420, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
            // line 421
            yield "        videoOptions += '<option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 421), "html", null, true);
            yield "\" data-category=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", true, true, false, 421)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 421), "General")) : ("General")), "html", null, true);
            yield "\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 421), "html", null, true);
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 421)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield " - ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 421), "html", null, true);
            }
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isFree", [], "any", false, false, false, 421)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield "(Free)";
            } else {
                yield "(\$";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "price", [], "any", false, false, false, 421), "html", null, true);
                yield ")";
            }
            yield "</option>';
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 423
        yield "
    // Enhanced video selection with search functionality
    \$('.video-dropdown').each(function() {
        \$(this).select2({
            placeholder: 'Search and select a video...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    });

    // Dynamic Video Selection Management
    \$(document).on('click', '.add-video-selection', function() {
        var container = \$('#video-selection-container');
        var newItem = \$(`
            <div class=\"input-group mb-2 video-selection-item\">
                <select class=\"form-select enhanced-dropdown video-dropdown\"
                        name=\"videos[]\"
                        required
                        aria-describedby=\"videos_help videos_error\"
                        aria-label=\"Select videos for this plan\"
                        style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                    <option value=\"\">Choose a video...</option>
                    \${videoOptions}
                </select>
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-video-selection\" style=\"border-radius: 0; border: 2px solid #dc3545; background: #dc3545; color: white; transition: all 0.3s ease;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-video-selection\" style=\"border-radius: 0 8px 8px 0; border: 2px solid #28a745; background: #28a745; color: white; transition: all 0.3s ease;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `);
        container.append(newItem);

        // Initialize Select2 for the new dropdown
        newItem.find('.video-dropdown').select2({
            placeholder: 'Search and select a video...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });

        updateVideoOptions();
    });

    // Remove video selection
    \$(document).on('click', '.remove-video-selection', function() {
        var item = \$(this).closest('.video-selection-item');
        // Destroy Select2 before removing the element
        item.find('.video-dropdown').select2('destroy');
        item.remove();
        updateVideoOptions();
    });

    // Update video options when selection changes
    \$(document).on('change', '.video-dropdown', function() {
        updateVideoOptions();
    });

    // Function to disable already selected videos in other dropdowns
    function updateVideoOptions() {
        var selectedVideos = [];
        \$('.video-dropdown').each(function() {
            var value = \$(this).val();
            if (value) {
                selectedVideos.push(value);
            }
        });

        \$('.video-dropdown').each(function() {
            var currentValue = \$(this).val();
            var \$select = \$(this);

            // Temporarily destroy Select2 to update options
            if (\$select.hasClass('select2-hidden-accessible')) {
                \$select.select2('destroy');
            }

            \$select.find('option').each(function() {
                var optionValue = \$(this).val();
                if (optionValue && selectedVideos.includes(optionValue) && optionValue !== currentValue) {
                    \$(this).prop('disabled', true);
                } else {
                    \$(this).prop('disabled', false);
                }
            });

            // Reinitialize Select2
            \$select.select2({
                placeholder: 'Search and select a video...',
                allowClear: true,
                width: '100%',
                theme: 'bootstrap4'
            });
        });
    }



    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/plans/create.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  660 => 423,  636 => 421,  632 => 420,  437 => 228,  403 => 196,  396 => 194,  387 => 193,  382 => 192,  378 => 191,  374 => 190,  369 => 189,  365 => 188,  231 => 57,  214 => 43,  198 => 29,  188 => 25,  185 => 24,  181 => 23,  178 => 22,  168 => 18,  165 => 17,  161 => 16,  157 => 14,  144 => 13,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Plan - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Plan{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_plans') }}\">Plans</a></li>
<li class=\"breadcrumb-item active\">Create Plan</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-layer-group mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Plan
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Plans Button -->
                        <a href=\"{{ path('admin_plans') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('plan_create') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Plan Code and Title Row -->
                            <div class=\"row\">
                                <!-- Plan Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                            Plan Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               placeholder=\"e.g., PLAN001, VB200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               title=\"Format: 2-4 letters followed by 1-4 numbers (e.g., PLAN001, VB200)\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid plan code (e.g., PLAN001, VB200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                            Plan Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter plan title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a plan title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Plan Details Row -->
                            <div class=\"row\">
                                <!-- Access Duration -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-calendar-alt text-primary mr-1\"></i>
                                            Access Duration (Days)
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               placeholder=\"e.g., 30, 60, 90\"
                                               min=\"1\"
                                               max=\"3650\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">

                                    </div>
                                </div>
                                
                                <!-- Price -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               placeholder=\"0.00\"
                                               step=\"0.01\"
                                               min=\"0\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\"
                                               required>
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Plan Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"4\"
                                          placeholder=\"Enter detailed plan description...\"
                                          required
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a plan description.
                                </div>
                            </div>
                            
                            <!-- Video Selection -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-video text-primary mr-1\"></i>
                                    Select Videos <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"video-selection-container\">
                                    <div class=\"input-group mb-2 video-selection-item\">
                                        <select class=\"form-select enhanced-dropdown video-dropdown\"
                                                name=\"videos[]\"
                                                required
                                                aria-describedby=\"videos_help videos_error\"
                                                aria-label=\"Select videos for this plan\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a video...</option>
                                            {% for video in videos %}
                                                <option value=\"{{ video.id }}\"
                                                        data-category=\"{{ video.category|default('General') }}\">
                                                    {{ video.title }}
                                                    {% if video.category %} - {{ video.category }}{% endif %}
                                                    {% if video.isFree %}(Free){% else %}(\${{ video.price }}){% endif %}
                                                </option>
                                            {% endfor %}
                                        </select>
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-video-selection\" style=\"border-radius: 0 8px 8px 0; border: 2px solid #28a745; background: #28a745; color: white; transition: all 0.3s ease;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div id=\"videos_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                    Select videos to include in this plan. Use arrow keys to navigate options.
                                </div>
                                <div id=\"videos_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                    Please select at least one video.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Click the + button to add more video selections. Each video can only be selected once.
                                </small>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Plan
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_plans') }}\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>

<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-field:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* Select2 Custom Styling - Force consistent height with other form fields */
.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
    padding-left: 8px !important;
    padding-right: 8px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.select2-container--bootstrap4 .select2-selection--multiple:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice {
    background-color: #1e3c72 !important;
    border: 1px solid #1e3c72 !important;
    color: white !important;
    border-radius: 4px !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove {
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove:hover {
    color: #ff6b6b !important;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* Form Label Icons */
.form-label i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Professional Input Group Styling */
.input-group-append .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    height: calc(1.6em + 1.25rem + 4px);
    border: 2px solid #ced4da;
    border-left: none;
}

.input-group .form-select {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Remove Video Button Styling */
.btn-danger:hover {
    background: #c82333 !important;
    border-color: #c82333 !important;
}

.btn-success:hover {
    background: #218838 !important;
    border-color: #218838 !important;
}

/* Select2 Custom Styling for Video Dropdowns */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px 0 0 8px !important;
    font-size: 1rem !important;
    background-color: #fff !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 12px !important;
    padding-right: 12px !important;
    line-height: calc(1.6em + 1.25rem) !important;
    color: #495057 !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.select2-dropdown {
    border: 1px solid #011a2d !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(1, 26, 45, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted[aria-selected] {
    background-color: rgba(1, 26, 45, 0.1) !important;
    color: #011a2d !important;
}

.video-selection-item .select2-container {
    flex: 1;
}

.input-group .select2-container .select2-selection--single {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}
</style>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<script>
\$(document).ready(function() {
    // Store video options for dynamic creation
    var videoOptions = '';
    {% for video in videos %}
        videoOptions += '<option value=\"{{ video.id }}\" data-category=\"{{ video.category|default('General') }}\">{{ video.title }}{% if video.category %} - {{ video.category }}{% endif %}{% if video.isFree %}(Free){% else %}(\${{ video.price }}){% endif %}</option>';
    {% endfor %}

    // Enhanced video selection with search functionality
    \$('.video-dropdown').each(function() {
        \$(this).select2({
            placeholder: 'Search and select a video...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    });

    // Dynamic Video Selection Management
    \$(document).on('click', '.add-video-selection', function() {
        var container = \$('#video-selection-container');
        var newItem = \$(`
            <div class=\"input-group mb-2 video-selection-item\">
                <select class=\"form-select enhanced-dropdown video-dropdown\"
                        name=\"videos[]\"
                        required
                        aria-describedby=\"videos_help videos_error\"
                        aria-label=\"Select videos for this plan\"
                        style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                    <option value=\"\">Choose a video...</option>
                    \${videoOptions}
                </select>
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-video-selection\" style=\"border-radius: 0; border: 2px solid #dc3545; background: #dc3545; color: white; transition: all 0.3s ease;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-video-selection\" style=\"border-radius: 0 8px 8px 0; border: 2px solid #28a745; background: #28a745; color: white; transition: all 0.3s ease;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `);
        container.append(newItem);

        // Initialize Select2 for the new dropdown
        newItem.find('.video-dropdown').select2({
            placeholder: 'Search and select a video...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });

        updateVideoOptions();
    });

    // Remove video selection
    \$(document).on('click', '.remove-video-selection', function() {
        var item = \$(this).closest('.video-selection-item');
        // Destroy Select2 before removing the element
        item.find('.video-dropdown').select2('destroy');
        item.remove();
        updateVideoOptions();
    });

    // Update video options when selection changes
    \$(document).on('change', '.video-dropdown', function() {
        updateVideoOptions();
    });

    // Function to disable already selected videos in other dropdowns
    function updateVideoOptions() {
        var selectedVideos = [];
        \$('.video-dropdown').each(function() {
            var value = \$(this).val();
            if (value) {
                selectedVideos.push(value);
            }
        });

        \$('.video-dropdown').each(function() {
            var currentValue = \$(this).val();
            var \$select = \$(this);

            // Temporarily destroy Select2 to update options
            if (\$select.hasClass('select2-hidden-accessible')) {
                \$select.select2('destroy');
            }

            \$select.find('option').each(function() {
                var optionValue = \$(this).val();
                if (optionValue && selectedVideos.includes(optionValue) && optionValue !== currentValue) {
                    \$(this).prop('disabled', true);
                } else {
                    \$(this).prop('disabled', false);
                }
            });

            // Reinitialize Select2
            \$select.select2({
                placeholder: 'Search and select a video...',
                allowClear: true,
                width: '100%',
                theme: 'bootstrap4'
            });
        });
    }



    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
{% endblock %}
", "admin/plans/create.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\plans\\create.html.twig");
    }
}
