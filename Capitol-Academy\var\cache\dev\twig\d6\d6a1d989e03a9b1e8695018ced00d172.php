<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/promotional_banners/index.html.twig */
class __TwigTemplate_82e1389598c74f6fbcf1d37944b5276f extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/promotional_banners/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/promotional_banners/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Promotional Banners - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Promotional Banners Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Promotional Banners</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Promotional Banners Management", "page_icon" => "fas fa-bullhorn", "search_placeholder" => "Search...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_promotional_banner_create"), "text" => "Create New Banner", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Banners", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 25
(isset($context["banners"]) || array_key_exists("banners", $context) ? $context["banners"] : (function () { throw new RuntimeError('Variable "banners" does not exist.', 25, $this->source); })())), "icon" => "fas fa-bullhorn", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 32
(isset($context["banners"]) || array_key_exists("banners", $context) ? $context["banners"] : (function () { throw new RuntimeError('Variable "banners" does not exist.', 32, $this->source); })()), function ($__banner__) use ($context, $macros) { $context["banner"] = $__banner__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 32, $this->source); })()), "isActive", [], "any", false, false, false, 32); })), "icon" => "fas fa-eye", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Inactive", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 39
(isset($context["banners"]) || array_key_exists("banners", $context) ? $context["banners"] : (function () { throw new RuntimeError('Variable "banners" does not exist.', 39, $this->source); })()), function ($__banner__) use ($context, $macros) { $context["banner"] = $__banner__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 39, $this->source); })()), "isActive", [], "any", false, false, false, 39); })), "icon" => "fas fa-eye-slash", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["banners"]) || array_key_exists("banners", $context) ? $context["banners"] : (function () { throw new RuntimeError('Variable "banners" does not exist.', 46, $this->source); })()), function ($__banner__) use ($context, $macros) { $context["banner"] = $__banner__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-calendar-plus", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/promotional_banners/index.html.twig", 54, "737336148")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 119
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 120
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.banner-row',
        ['.banner-title', '.banner-message']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Banner management functions
function toggleBannerStatus(bannerId, bannerTitle, isActive) {
    showStatusModal(bannerTitle, isActive, function() {
        executeBannerStatusToggle(bannerId);
    });
}

function deleteBanner(bannerId, bannerTitle) {
    showDeleteModal(bannerTitle, function() {
        executeBannerDelete(bannerId);
    });
}

// Actual execution functions
function executeBannerStatusToggle(bannerId) {
    fetch(`/admin/promotional-banners/\${bannerId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the banner status'); // REMOVED
    });
}

function executeBannerDelete(bannerId) {
    fetch(`/admin/promotional-banners/\${bannerId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `_token=\${encodeURIComponent('";
        // line 208
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("promotional_banner_delete"), "html", null, true);
        yield "')}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while deleting the banner'); // REMOVED
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/promotional_banners/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  277 => 208,  187 => 120,  174 => 119,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Promotional Banners - Capitol Academy Admin{% endblock %}

{% block page_title %}Promotional Banners Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Promotional Banners</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Promotional Banners Management',
    'page_icon': 'fas fa-bullhorn',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_promotional_banner_create'),
        'text': 'Create New Banner',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Banners',
            'value': banners|length,
            'icon': 'fas fa-bullhorn',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': banners|filter(banner => banner.isActive)|length,
            'icon': 'fas fa-eye',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': banners|filter(banner => not banner.isActive)|length,
            'icon': 'fas fa-eye-slash',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': banners|filter(banner => banner.createdAt and banner.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-calendar-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Title'},
            {'text': 'End Date'},
            {'text': 'Status'},
            {'text': 'Created'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for banner in banners %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center\">
                        <div class=\"rounded me-3\" style=\"width: 4px; height: 40px; background-color: ' ~ banner.backgroundColor ~ '\"></div>
                        <div>
                            <h6 class=\"banner-title mb-0 font-weight-bold text-dark\">' ~ banner.title ~ '</h6>
                            ' ~ (banner.description ? '<small class=\"text-muted\">' ~ (banner.description|length > 60 ? banner.description|slice(0, 60) ~ '...' : banner.description) ~ '</small>' : '') ~ '
                        </div>
                    </div>'
                },
                {
                    'content': banner.endDate ?
                        '<span class=\"text-dark font-weight-medium\">' ~ banner.endDate|date('M d, Y H:i') ~ '</span>' :
                        '<span class=\"text-muted\">No expiration</span>'
                },
                {
                    'content': banner.isCurrentlyValid ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        (banner.isActive ?
                            '<span class=\"badge\" style=\"background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-clock mr-1\"></i> Expired</span>' :
                            '<span class=\"badge\" style=\"background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Disabled</span>'
                        )
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ banner.createdAt|date('M d, Y H:i') ~ '</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_promotional_banner_edit', {'id': banner.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Banner\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (banner.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (banner.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (banner.isActive ? 'Deactivate' : 'Activate') ~ ' Banner\" onclick=\"toggleBannerStatus(' ~ banner.id ~ ', \\'' ~ banner.title ~ '\\', ' ~ banner.isActive ~ ')\"><i class=\"fas fa-' ~ (banner.isActive ? 'eye-slash' : 'eye') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Banner\" onclick=\"deleteBanner(' ~ banner.id ~ ', \\'' ~ banner.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'banner-row',
            'empty_message': 'No promotional banners found',
            'empty_icon': 'fas fa-bullhorn',
            'empty_description': 'Get started by creating your first promotional banner.',
            'search_config': {
                'fields': ['.banner-title', '.banner-message']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.banner-row',
        ['.banner-title', '.banner-message']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Banner management functions
function toggleBannerStatus(bannerId, bannerTitle, isActive) {
    showStatusModal(bannerTitle, isActive, function() {
        executeBannerStatusToggle(bannerId);
    });
}

function deleteBanner(bannerId, bannerTitle) {
    showDeleteModal(bannerTitle, function() {
        executeBannerDelete(bannerId);
    });
}

// Actual execution functions
function executeBannerStatusToggle(bannerId) {
    fetch(`/admin/promotional-banners/\${bannerId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the banner status'); // REMOVED
    });
}

function executeBannerDelete(bannerId) {
    fetch(`/admin/promotional-banners/\${bannerId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `_token=\${encodeURIComponent('{{ csrf_token('promotional_banner_delete') }}')}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while deleting the banner'); // REMOVED
    });
}
</script>
{% endblock %}
", "admin/promotional_banners/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\promotional_banners\\index.html.twig");
    }
}


/* admin/promotional_banners/index.html.twig */
class __TwigTemplate_82e1389598c74f6fbcf1d37944b5276f___737336148 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/promotional_banners/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/promotional_banners/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "        <!-- Standardized Table -->
        ";
        // line 57
        $context["table_headers"] = [["text" => "Title"], ["text" => "End Date"], ["text" => "Status"], ["text" => "Created"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 64
        yield "
        ";
        // line 65
        $context["table_rows"] = [];
        // line 66
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["banners"]) || array_key_exists("banners", $context) ? $context["banners"] : (function () { throw new RuntimeError('Variable "banners" does not exist.', 66, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["banner"]) {
            // line 67
            yield "            ";
            $context["row_cells"] = [["content" => (((((("<div class=\"d-flex align-items-center\">
                        <div class=\"rounded me-3\" style=\"width: 4px; height: 40px; background-color: " . CoreExtension::getAttribute($this->env, $this->source,             // line 70
$context["banner"], "backgroundColor", [], "any", false, false, false, 70)) . "\"></div>
                        <div>
                            <h6 class=\"banner-title mb-0 font-weight-bold text-dark\">") . CoreExtension::getAttribute($this->env, $this->source,             // line 72
$context["banner"], "title", [], "any", false, false, false, 72)) . "</h6>
                            ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["banner"], "description", [], "any", false, false, false, 73)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<small class=\"text-muted\">" . (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "description", [], "any", false, false, false, 73)) > 60)) ? ((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "description", [], "any", false, false, false, 73), 0, 60) . "...")) : (CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "description", [], "any", false, false, false, 73)))) . "</small>")) : (""))) . "
                        </div>
                    </div>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["banner"], "endDate", [], "any", false, false, false, 78)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<span class=\"text-dark font-weight-medium\">" . $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source,             // line 79
$context["banner"], "endDate", [], "any", false, false, false, 79), "M d, Y H:i")) . "</span>")) : ("<span class=\"text-muted\">No expiration</span>"))], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 83
$context["banner"], "isCurrentlyValid", [], "any", false, false, false, 83)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>") : ((((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 85
$context["banner"], "isActive", [], "any", false, false, false, 85)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-clock mr-1\"></i> Expired</span>") : ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Disabled</span>"))))], ["content" => (("<span class=\"text-dark font-weight-medium\">" . $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source,             // line 91
$context["banner"], "createdAt", [], "any", false, false, false, 91), "M d, Y H:i")) . "</span>")], ["content" => (((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_promotional_banner_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 95
$context["banner"], "id", [], "any", false, false, false, 95)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Banner\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 96
$context["banner"], "isActive", [], "any", false, false, false, 96)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . " 0%, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "isActive", [], "any", false, false, false, 96)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#5a6268") : ("#1e7e34"))) . " 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "isActive", [], "any", false, false, false, 96)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " Banner\" onclick=\"toggleBannerStatus(") . CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "id", [], "any", false, false, false, 96)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "title", [], "any", false, false, false, 96)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "isActive", [], "any", false, false, false, 96)) . ")\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "isActive", [], "any", false, false, false, 96)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("eye-slash") : ("eye"))) . "\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Banner\" onclick=\"deleteBanner(") . CoreExtension::getAttribute($this->env, $this->source,             // line 97
$context["banner"], "id", [], "any", false, false, false, 97)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["banner"], "title", [], "any", false, false, false, 97)) . "')\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 101
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 101, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 101, $this->source); })())]]);
            // line 102
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['banner'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 103
        yield "
        ";
        // line 104
        yield from $this->load("components/admin_table.html.twig", 104)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 105
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 105, $this->source); })()), "rows" =>         // line 106
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 106, $this->source); })()), "row_class" => "banner-row", "empty_message" => "No promotional banners found", "empty_icon" => "fas fa-bullhorn", "empty_description" => "Get started by creating your first promotional banner.", "search_config" => ["fields" => [".banner-title", ".banner-message"]]]));
        // line 115
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/promotional_banners/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  673 => 115,  671 => 106,  670 => 105,  669 => 104,  666 => 103,  660 => 102,  657 => 101,  654 => 97,  652 => 96,  650 => 95,  648 => 91,  647 => 85,  646 => 83,  645 => 79,  644 => 78,  641 => 73,  639 => 72,  636 => 70,  633 => 67,  628 => 66,  626 => 65,  623 => 64,  621 => 57,  618 => 56,  605 => 55,  582 => 54,  277 => 208,  187 => 120,  174 => 119,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Promotional Banners - Capitol Academy Admin{% endblock %}

{% block page_title %}Promotional Banners Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Promotional Banners</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Promotional Banners Management',
    'page_icon': 'fas fa-bullhorn',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_promotional_banner_create'),
        'text': 'Create New Banner',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Banners',
            'value': banners|length,
            'icon': 'fas fa-bullhorn',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': banners|filter(banner => banner.isActive)|length,
            'icon': 'fas fa-eye',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': banners|filter(banner => not banner.isActive)|length,
            'icon': 'fas fa-eye-slash',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': banners|filter(banner => banner.createdAt and banner.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-calendar-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Title'},
            {'text': 'End Date'},
            {'text': 'Status'},
            {'text': 'Created'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for banner in banners %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center\">
                        <div class=\"rounded me-3\" style=\"width: 4px; height: 40px; background-color: ' ~ banner.backgroundColor ~ '\"></div>
                        <div>
                            <h6 class=\"banner-title mb-0 font-weight-bold text-dark\">' ~ banner.title ~ '</h6>
                            ' ~ (banner.description ? '<small class=\"text-muted\">' ~ (banner.description|length > 60 ? banner.description|slice(0, 60) ~ '...' : banner.description) ~ '</small>' : '') ~ '
                        </div>
                    </div>'
                },
                {
                    'content': banner.endDate ?
                        '<span class=\"text-dark font-weight-medium\">' ~ banner.endDate|date('M d, Y H:i') ~ '</span>' :
                        '<span class=\"text-muted\">No expiration</span>'
                },
                {
                    'content': banner.isCurrentlyValid ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        (banner.isActive ?
                            '<span class=\"badge\" style=\"background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-clock mr-1\"></i> Expired</span>' :
                            '<span class=\"badge\" style=\"background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Disabled</span>'
                        )
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ banner.createdAt|date('M d, Y H:i') ~ '</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_promotional_banner_edit', {'id': banner.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Banner\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (banner.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (banner.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (banner.isActive ? 'Deactivate' : 'Activate') ~ ' Banner\" onclick=\"toggleBannerStatus(' ~ banner.id ~ ', \\'' ~ banner.title ~ '\\', ' ~ banner.isActive ~ ')\"><i class=\"fas fa-' ~ (banner.isActive ? 'eye-slash' : 'eye') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Banner\" onclick=\"deleteBanner(' ~ banner.id ~ ', \\'' ~ banner.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'banner-row',
            'empty_message': 'No promotional banners found',
            'empty_icon': 'fas fa-bullhorn',
            'empty_description': 'Get started by creating your first promotional banner.',
            'search_config': {
                'fields': ['.banner-title', '.banner-message']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.banner-row',
        ['.banner-title', '.banner-message']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Banner management functions
function toggleBannerStatus(bannerId, bannerTitle, isActive) {
    showStatusModal(bannerTitle, isActive, function() {
        executeBannerStatusToggle(bannerId);
    });
}

function deleteBanner(bannerId, bannerTitle) {
    showDeleteModal(bannerTitle, function() {
        executeBannerDelete(bannerId);
    });
}

// Actual execution functions
function executeBannerStatusToggle(bannerId) {
    fetch(`/admin/promotional-banners/\${bannerId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the banner status'); // REMOVED
    });
}

function executeBannerDelete(bannerId) {
    fetch(`/admin/promotional-banners/\${bannerId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `_token=\${encodeURIComponent('{{ csrf_token('promotional_banner_delete') }}')}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while deleting the banner'); // REMOVED
    });
}
</script>
{% endblock %}
", "admin/promotional_banners/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\promotional_banners\\index.html.twig");
    }
}
