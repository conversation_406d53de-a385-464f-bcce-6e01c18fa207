<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/remote_courses/index.html.twig */
class __TwigTemplate_a7d128151b2dd6101c9513c814e887e1 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumb' => [$this, 'block_breadcrumb'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Remote Courses Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Remote Courses Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumb(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumb"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumb"));

        // line 8
        yield "    <nav aria-label=\"breadcrumb\">
        <ol class=\"breadcrumb\">
            <li class=\"breadcrumb-item\"><a href=\"";
        // line 10
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Dashboard</a></li>
            <li class=\"breadcrumb-item active\" aria-current=\"page\">Remote Courses</li>
        </ol>
    </nav>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 16
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 17
        yield "    <div class=\"container-fluid\">
        <!-- Statistics Cards -->
        <div class=\"row mb-4\">
            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-primary shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">Total Remote Courses</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">";
        // line 26
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 26, $this->source); })()), "total", [], "any", false, false, false, 26), "html", null, true);
        yield "</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-video fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-success shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">Active Courses</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">";
        // line 42
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 42, $this->source); })()), "active", [], "any", false, false, false, 42), "html", null, true);
        yield "</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-check-circle fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-info shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">Total Enrollments</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">";
        // line 58
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 58, $this->source); })()), "total_enrollments", [], "any", false, false, false, 58), "html", null, true);
        yield "</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-users fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-warning shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">Total Revenue</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">\$";
        // line 74
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 74, $this->source); })()), "total_revenue", [], "any", false, false, false, 74), 2), "html", null, true);
        yield "</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-dollar-sign fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class=\"card shadow mb-4\">
            <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">
                <h6 class=\"m-0 font-weight-bold text-primary\">
                    <i class=\"fas fa-video me-2\"></i>Remote Courses
                </h6>
                <div class=\"d-flex align-items-center\">
                    <!-- Search Form -->
                    <form method=\"GET\" class=\"d-flex me-3\">
                        <div class=\"input-group\">
                            <input type=\"text\" name=\"search\" class=\"form-control form-control-sm\" 
                                   placeholder=\"Search courses...\" value=\"";
        // line 96
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["search"]) || array_key_exists("search", $context) ? $context["search"] : (function () { throw new RuntimeError('Variable "search" does not exist.', 96, $this->source); })()), "html", null, true);
        yield "\" style=\"min-width: 200px;\">
                            <button class=\"btn btn-outline-secondary btn-sm\" type=\"submit\">
                                <i class=\"fas fa-search\"></i>
                            </button>
                            ";
        // line 100
        if ((($tmp = (isset($context["search"]) || array_key_exists("search", $context) ? $context["search"] : (function () { throw new RuntimeError('Variable "search" does not exist.', 100, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 101
            yield "                                <a href=\"";
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_index");
            yield "\" class=\"btn btn-outline-secondary btn-sm\">
                                    <i class=\"fas fa-times\"></i>
                                </a>
                            ";
        }
        // line 105
        yield "                        </div>
                    </form>

                    <!-- Export Button -->
                    <button class=\"btn btn-success btn-sm me-2\" onclick=\"exportTableToCSV('remote-courses.csv')\">
                        <i class=\"fas fa-download me-1\"></i>Export CSV
                    </button>

                    <!-- Add Button -->
                    <a href=\"";
        // line 114
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_create");
        yield "\" class=\"btn btn-primary btn-sm\">
                        <i class=\"fas fa-plus me-1\"></i>Add Remote Course
                    </a>
                </div>
            </div>

            <div class=\"card-body\">
                ";
        // line 121
        if (Twig\Extension\CoreExtension::testEmpty((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 121, $this->source); })()))) {
            // line 122
            yield "                    <div class=\"text-center py-5\">
                        <i class=\"fas fa-video fa-4x text-muted mb-3\"></i>
                        <h4 class=\"text-muted mb-3\">No remote courses found</h4>
                        ";
            // line 125
            if ((($tmp = (isset($context["search"]) || array_key_exists("search", $context) ? $context["search"] : (function () { throw new RuntimeError('Variable "search" does not exist.', 125, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 126
                yield "                            <p class=\"text-muted mb-4\">No remote courses match your search criteria.</p>
                            <a href=\"";
                // line 127
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_index");
                yield "\" class=\"btn btn-outline-primary\">
                                <i class=\"fas fa-arrow-left me-1\"></i>View All Courses
                            </a>
                        ";
            } else {
                // line 131
                yield "                            <p class=\"text-muted mb-4\">Get started by creating your first remote course.</p>
                            <a href=\"";
                // line 132
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_create");
                yield "\" class=\"btn btn-primary btn-lg\">
                                <i class=\"fas fa-plus me-2\"></i>Create Remote Course
                            </a>
                        ";
            }
            // line 136
            yield "                    </div>
                ";
        } else {
            // line 138
            yield "                    <!-- Table -->
                    <div class=\"table-responsive\">
                        <table class=\"table table-bordered table-hover\" id=\"dataTable\">
                            <thead class=\"table-light\">
                                <tr>
                                    ";
            // line 143
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 143, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["header"]) {
                // line 144
                yield "                                        <th class=\"";
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["header"], "sortable", [], "any", false, false, false, 144)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    yield "sortable";
                }
                yield "\">
                                            ";
                // line 145
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["header"], "label", [], "any", false, false, false, 145), "html", null, true);
                yield "
                                            ";
                // line 146
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["header"], "sortable", [], "any", false, false, false, 146)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 147
                    yield "                                                <i class=\"fas fa-sort text-muted ms-1\"></i>
                                            ";
                }
                // line 149
                yield "                                        </th>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['header'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 151
            yield "                                </tr>
                            </thead>
                            <tbody>
                                ";
            // line 154
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 154, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["row"]) {
                // line 155
                yield "                                    <tr>
                                        <td><strong>";
                // line 156
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 156), "code", [], "any", false, false, false, 156), "html", null, true);
                yield "</strong></td>
                                        <td>
                                            <div class=\"fw-bold\">";
                // line 158
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 158), "title", [], "any", false, false, false, 158), "html", null, true);
                yield "</div>
                                        </td>
                                        <td>
                                            <span class=\"badge bg-info\">";
                // line 161
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 161), "category", [], "any", false, false, false, 161), "html", null, true);
                yield "</span>
                                        </td>
                                        <td>";
                // line 163
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 163), "instructor", [], "any", false, false, false, 163), "html", null, true);
                yield "</td>
                                        <td>
                                            <span class=\"badge bg-secondary\">";
                // line 165
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 165), "chapters", [], "any", false, false, false, 165), "html", null, true);
                yield " chapters</span>
                                        </td>
                                        <td>
                                            <span class=\"badge bg-primary\">";
                // line 168
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 168), "videos", [], "any", false, false, false, 168), "html", null, true);
                yield " videos</span>
                                        </td>
                                        <td><strong>";
                // line 170
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 170), "price", [], "any", false, false, false, 170), "html", null, true);
                yield "</strong></td>
                                        <td>";
                // line 171
                yield CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 171), "status", [], "any", false, false, false, 171);
                yield "</td>
                                        <td>
                                            <div class=\"btn-group\" role=\"group\">
                                                <a href=\"";
                // line 174
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 174), "actions", [], "any", false, false, false, 174), "edit", [], "any", false, false, false, 174), "html", null, true);
                yield "\" class=\"btn btn-sm btn-outline-primary\" title=\"Edit\">
                                                    <i class=\"fas fa-edit\"></i>
                                                </a>
                                                <a href=\"";
                // line 177
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 177), "actions", [], "any", false, false, false, 177), "chapters", [], "any", false, false, false, 177), "html", null, true);
                yield "\" class=\"btn btn-sm btn-outline-info\" title=\"Manage Chapters\">
                                                    <i class=\"fas fa-list\"></i>
                                                </a>
                                                <a href=\"";
                // line 180
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 180), "actions", [], "any", false, false, false, 180), "preview", [], "any", false, false, false, 180), "html", null, true);
                yield "\" class=\"btn btn-sm btn-outline-success\" title=\"Preview\">
                                                    <i class=\"fas fa-eye\"></i>
                                                </a>
                                                <button type=\"button\" class=\"btn btn-sm btn-outline-danger\" 
                                                        onclick=\"confirmDelete('";
                // line 184
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 184), "actions", [], "any", false, false, false, 184), "delete", [], "any", false, false, false, 184), "html", null, true);
                yield "', '";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["row"], "data", [], "any", false, false, false, 184), "title", [], "any", false, false, false, 184), "html", null, true);
                yield "')\" title=\"Delete\">
                                                    <i class=\"fas fa-trash\"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['row'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 191
            yield "                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    ";
            // line 196
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 196, $this->source); })()), "total_pages", [], "any", false, false, false, 196) > 1)) {
                // line 197
                yield "                        <nav aria-label=\"Remote courses pagination\">
                            <ul class=\"pagination justify-content-center\">
                                ";
                // line 199
                if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 199, $this->source); })()), "current_page", [], "any", false, false, false, 199) > 1)) {
                    // line 200
                    yield "                                    <li class=\"page-item\">
                                        <a class=\"page-link\" href=\"";
                    // line 201
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_index", ["page" => (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 201, $this->source); })()), "current_page", [], "any", false, false, false, 201) - 1), "search" => (isset($context["search"]) || array_key_exists("search", $context) ? $context["search"] : (function () { throw new RuntimeError('Variable "search" does not exist.', 201, $this->source); })())]), "html", null, true);
                    yield "\">Previous</a>
                                    </li>
                                ";
                }
                // line 204
                yield "
                                ";
                // line 205
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(range(1, CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 205, $this->source); })()), "total_pages", [], "any", false, false, false, 205)));
                foreach ($context['_seq'] as $context["_key"] => $context["page"]) {
                    // line 206
                    yield "                                    ";
                    if (($context["page"] == CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 206, $this->source); })()), "current_page", [], "any", false, false, false, 206))) {
                        // line 207
                        yield "                                        <li class=\"page-item active\">
                                            <span class=\"page-link\">";
                        // line 208
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                        yield "</span>
                                        </li>
                                    ";
                    } else {
                        // line 211
                        yield "                                        <li class=\"page-item\">
                                            <a class=\"page-link\" href=\"";
                        // line 212
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_index", ["page" => $context["page"], "search" => (isset($context["search"]) || array_key_exists("search", $context) ? $context["search"] : (function () { throw new RuntimeError('Variable "search" does not exist.', 212, $this->source); })())]), "html", null, true);
                        yield "\">";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                        yield "</a>
                                        </li>
                                    ";
                    }
                    // line 215
                    yield "                                ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['page'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 216
                yield "
                                ";
                // line 217
                if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 217, $this->source); })()), "current_page", [], "any", false, false, false, 217) < CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 217, $this->source); })()), "total_pages", [], "any", false, false, false, 217))) {
                    // line 218
                    yield "                                    <li class=\"page-item\">
                                        <a class=\"page-link\" href=\"";
                    // line 219
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_index", ["page" => (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 219, $this->source); })()), "current_page", [], "any", false, false, false, 219) + 1), "search" => (isset($context["search"]) || array_key_exists("search", $context) ? $context["search"] : (function () { throw new RuntimeError('Variable "search" does not exist.', 219, $this->source); })())]), "html", null, true);
                    yield "\">Next</a>
                                    </li>
                                ";
                }
                // line 222
                yield "                            </ul>
                        </nav>
                    ";
            }
            // line 225
            yield "                ";
        }
        // line 226
        yield "            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class=\"modal fade\" id=\"deleteModal\" tabindex=\"-1\" aria-labelledby=\"deleteModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog\">
            <div class=\"modal-content\">
                <div class=\"modal-header\">
                    <h5 class=\"modal-title\" id=\"deleteModalLabel\">Confirm Deletion</h5>
                    <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\">
                    <p>Are you sure you want to delete the remote course \"<span id=\"courseTitle\"></span>\"?</p>
                    <p class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone. All chapters and video assignments will be permanently deleted.</p>
                </div>
                <div class=\"modal-footer\">
                    <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                    <form id=\"deleteForm\" method=\"POST\" style=\"display: inline;\">
                        <input type=\"hidden\" name=\"_token\" value=\"";
        // line 245
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("delete"), "html", null, true);
        yield "\">
                        <button type=\"submit\" class=\"btn btn-danger\">Delete Course</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 254
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 255
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        function confirmDelete(deleteUrl, courseTitle) {
            document.getElementById('courseTitle').textContent = courseTitle;
            document.getElementById('deleteForm').action = deleteUrl;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function exportTableToCSV(filename) {
            // Implementation for CSV export
            const table = document.getElementById('dataTable');
            const csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');
                for (let j = 0; j < cols.length - 1; j++) { // Exclude actions column
                    row.push(cols[j].innerText);
                }
                csv.push(row.join(','));
            }
            
            const csvFile = new Blob([csv.join('\\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = filename;
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/remote_courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  581 => 255,  568 => 254,  549 => 245,  528 => 226,  525 => 225,  520 => 222,  514 => 219,  511 => 218,  509 => 217,  506 => 216,  500 => 215,  492 => 212,  489 => 211,  483 => 208,  480 => 207,  477 => 206,  473 => 205,  470 => 204,  464 => 201,  461 => 200,  459 => 199,  455 => 197,  453 => 196,  446 => 191,  431 => 184,  424 => 180,  418 => 177,  412 => 174,  406 => 171,  402 => 170,  397 => 168,  391 => 165,  386 => 163,  381 => 161,  375 => 158,  370 => 156,  367 => 155,  363 => 154,  358 => 151,  351 => 149,  347 => 147,  345 => 146,  341 => 145,  334 => 144,  330 => 143,  323 => 138,  319 => 136,  312 => 132,  309 => 131,  302 => 127,  299 => 126,  297 => 125,  292 => 122,  290 => 121,  280 => 114,  269 => 105,  261 => 101,  259 => 100,  252 => 96,  227 => 74,  208 => 58,  189 => 42,  170 => 26,  159 => 17,  146 => 16,  130 => 10,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Remote Courses Management{% endblock %}

{% block page_title %}Remote Courses Management{% endblock %}

{% block breadcrumb %}
    <nav aria-label=\"breadcrumb\">
        <ol class=\"breadcrumb\">
            <li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Dashboard</a></li>
            <li class=\"breadcrumb-item active\" aria-current=\"page\">Remote Courses</li>
        </ol>
    </nav>
{% endblock %}

{% block content %}
    <div class=\"container-fluid\">
        <!-- Statistics Cards -->
        <div class=\"row mb-4\">
            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-primary shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">Total Remote Courses</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.total }}</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-video fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-success shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">Active Courses</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.active }}</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-check-circle fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-info shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">Total Enrollments</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ stats.total_enrollments }}</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-users fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"col-xl-3 col-md-6 mb-4\">
                <div class=\"card border-left-warning shadow h-100 py-2\">
                    <div class=\"card-body\">
                        <div class=\"row no-gutters align-items-center\">
                            <div class=\"col mr-2\">
                                <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">Total Revenue</div>
                                <div class=\"h5 mb-0 font-weight-bold text-gray-800\">\${{ stats.total_revenue|number_format(2) }}</div>
                            </div>
                            <div class=\"col-auto\">
                                <i class=\"fas fa-dollar-sign fa-2x text-gray-300\"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class=\"card shadow mb-4\">
            <div class=\"card-header py-3 d-flex flex-row align-items-center justify-content-between\">
                <h6 class=\"m-0 font-weight-bold text-primary\">
                    <i class=\"fas fa-video me-2\"></i>Remote Courses
                </h6>
                <div class=\"d-flex align-items-center\">
                    <!-- Search Form -->
                    <form method=\"GET\" class=\"d-flex me-3\">
                        <div class=\"input-group\">
                            <input type=\"text\" name=\"search\" class=\"form-control form-control-sm\" 
                                   placeholder=\"Search courses...\" value=\"{{ search }}\" style=\"min-width: 200px;\">
                            <button class=\"btn btn-outline-secondary btn-sm\" type=\"submit\">
                                <i class=\"fas fa-search\"></i>
                            </button>
                            {% if search %}
                                <a href=\"{{ path('admin_remote_course_index') }}\" class=\"btn btn-outline-secondary btn-sm\">
                                    <i class=\"fas fa-times\"></i>
                                </a>
                            {% endif %}
                        </div>
                    </form>

                    <!-- Export Button -->
                    <button class=\"btn btn-success btn-sm me-2\" onclick=\"exportTableToCSV('remote-courses.csv')\">
                        <i class=\"fas fa-download me-1\"></i>Export CSV
                    </button>

                    <!-- Add Button -->
                    <a href=\"{{ path('admin_remote_course_create') }}\" class=\"btn btn-primary btn-sm\">
                        <i class=\"fas fa-plus me-1\"></i>Add Remote Course
                    </a>
                </div>
            </div>

            <div class=\"card-body\">
                {% if courses is empty %}
                    <div class=\"text-center py-5\">
                        <i class=\"fas fa-video fa-4x text-muted mb-3\"></i>
                        <h4 class=\"text-muted mb-3\">No remote courses found</h4>
                        {% if search %}
                            <p class=\"text-muted mb-4\">No remote courses match your search criteria.</p>
                            <a href=\"{{ path('admin_remote_course_index') }}\" class=\"btn btn-outline-primary\">
                                <i class=\"fas fa-arrow-left me-1\"></i>View All Courses
                            </a>
                        {% else %}
                            <p class=\"text-muted mb-4\">Get started by creating your first remote course.</p>
                            <a href=\"{{ path('admin_remote_course_create') }}\" class=\"btn btn-primary btn-lg\">
                                <i class=\"fas fa-plus me-2\"></i>Create Remote Course
                            </a>
                        {% endif %}
                    </div>
                {% else %}
                    <!-- Table -->
                    <div class=\"table-responsive\">
                        <table class=\"table table-bordered table-hover\" id=\"dataTable\">
                            <thead class=\"table-light\">
                                <tr>
                                    {% for header in table_headers %}
                                        <th class=\"{% if header.sortable %}sortable{% endif %}\">
                                            {{ header.label }}
                                            {% if header.sortable %}
                                                <i class=\"fas fa-sort text-muted ms-1\"></i>
                                            {% endif %}
                                        </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in table_rows %}
                                    <tr>
                                        <td><strong>{{ row.data.code }}</strong></td>
                                        <td>
                                            <div class=\"fw-bold\">{{ row.data.title }}</div>
                                        </td>
                                        <td>
                                            <span class=\"badge bg-info\">{{ row.data.category }}</span>
                                        </td>
                                        <td>{{ row.data.instructor }}</td>
                                        <td>
                                            <span class=\"badge bg-secondary\">{{ row.data.chapters }} chapters</span>
                                        </td>
                                        <td>
                                            <span class=\"badge bg-primary\">{{ row.data.videos }} videos</span>
                                        </td>
                                        <td><strong>{{ row.data.price }}</strong></td>
                                        <td>{{ row.data.status|raw }}</td>
                                        <td>
                                            <div class=\"btn-group\" role=\"group\">
                                                <a href=\"{{ row.data.actions.edit }}\" class=\"btn btn-sm btn-outline-primary\" title=\"Edit\">
                                                    <i class=\"fas fa-edit\"></i>
                                                </a>
                                                <a href=\"{{ row.data.actions.chapters }}\" class=\"btn btn-sm btn-outline-info\" title=\"Manage Chapters\">
                                                    <i class=\"fas fa-list\"></i>
                                                </a>
                                                <a href=\"{{ row.data.actions.preview }}\" class=\"btn btn-sm btn-outline-success\" title=\"Preview\">
                                                    <i class=\"fas fa-eye\"></i>
                                                </a>
                                                <button type=\"button\" class=\"btn btn-sm btn-outline-danger\" 
                                                        onclick=\"confirmDelete('{{ row.data.actions.delete }}', '{{ row.data.title }}')\" title=\"Delete\">
                                                    <i class=\"fas fa-trash\"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if pagination.total_pages > 1 %}
                        <nav aria-label=\"Remote courses pagination\">
                            <ul class=\"pagination justify-content-center\">
                                {% if pagination.current_page > 1 %}
                                    <li class=\"page-item\">
                                        <a class=\"page-link\" href=\"{{ path('admin_remote_course_index', {page: pagination.current_page - 1, search: search}) }}\">Previous</a>
                                    </li>
                                {% endif %}

                                {% for page in 1..pagination.total_pages %}
                                    {% if page == pagination.current_page %}
                                        <li class=\"page-item active\">
                                            <span class=\"page-link\">{{ page }}</span>
                                        </li>
                                    {% else %}
                                        <li class=\"page-item\">
                                            <a class=\"page-link\" href=\"{{ path('admin_remote_course_index', {page: page, search: search}) }}\">{{ page }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if pagination.current_page < pagination.total_pages %}
                                    <li class=\"page-item\">
                                        <a class=\"page-link\" href=\"{{ path('admin_remote_course_index', {page: pagination.current_page + 1, search: search}) }}\">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class=\"modal fade\" id=\"deleteModal\" tabindex=\"-1\" aria-labelledby=\"deleteModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog\">
            <div class=\"modal-content\">
                <div class=\"modal-header\">
                    <h5 class=\"modal-title\" id=\"deleteModalLabel\">Confirm Deletion</h5>
                    <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\">
                    <p>Are you sure you want to delete the remote course \"<span id=\"courseTitle\"></span>\"?</p>
                    <p class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone. All chapters and video assignments will be permanently deleted.</p>
                </div>
                <div class=\"modal-footer\">
                    <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                    <form id=\"deleteForm\" method=\"POST\" style=\"display: inline;\">
                        <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('delete') }}\">
                        <button type=\"submit\" class=\"btn btn-danger\">Delete Course</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        function confirmDelete(deleteUrl, courseTitle) {
            document.getElementById('courseTitle').textContent = courseTitle;
            document.getElementById('deleteForm').action = deleteUrl;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function exportTableToCSV(filename) {
            // Implementation for CSV export
            const table = document.getElementById('dataTable');
            const csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');
                for (let j = 0; j < cols.length - 1; j++) { // Exclude actions column
                    row.push(cols[j].innerText);
                }
                csv.push(row.join(','));
            }
            
            const csvFile = new Blob([csv.join('\\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = filename;
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
{% endblock %}
", "admin/remote_courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\remote_courses\\index.html.twig");
    }
}
