<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* checkout/index.html.twig */
class __TwigTemplate_5dd487089cd44c1bda5934e860347d0b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "checkout/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "checkout/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Checkout - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 6
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        .checkout-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .checkout-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .checkout-header {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .checkout-content {
            padding: 40px;
        }

        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            position: sticky;
            top: 100px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #a90418;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-weight: 600;
            color: #a90418;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
        }

        .summary-row.total {
            border-top: 2px solid #dee2e6;
            padding-top: 15px;
            font-size: 1.2rem;
            font-weight: 700;
            color: #011a2d;
        }

        .payment-section {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .payment-method {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-method:hover,
        .payment-method.selected {
            border-color: #011a2d;
            background: #f8f9fa;
        }

        .payment-method input[type=\"radio\"] {
            margin-right: 15px;
        }

        .payment-method img {
            height: 30px;
            margin-left: auto;
        }

        .paypal-button-container {
            margin-top: 20px;
        }

        .security-info {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .security-info i {
            color: #28a745;
            margin-right: 8px;
        }

        .billing-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #011a2d;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .checkout-content {
                padding: 20px;
            }
            
            .order-summary {
                position: static;
                margin-top: 30px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 198
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 199
        yield "    <section class=\"checkout-section\">
        <div class=\"container\">
            <div class=\"checkout-container\">
                <div class=\"checkout-header\">
                    <h1 class=\"mb-3\">
                        <i class=\"fas fa-credit-card me-3\"></i>Secure Checkout
                    </h1>
                    <p class=\"mb-0\">Complete your purchase securely with PayPal</p>
                </div>

                <div class=\"checkout-content\">
                    ";
        // line 210
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["removed_items"]) || array_key_exists("removed_items", $context) ? $context["removed_items"] : (function () { throw new RuntimeError('Variable "removed_items" does not exist.', 210, $this->source); })())) > 0)) {
            // line 211
            yield "                        <div class=\"alert alert-warning\">
                            <h6><i class=\"fas fa-exclamation-triangle me-2\"></i>Items Removed</h6>
                            <p class=\"mb-0\">
                                ";
            // line 214
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["removed_items"]) || array_key_exists("removed_items", $context) ? $context["removed_items"] : (function () { throw new RuntimeError('Variable "removed_items" does not exist.', 214, $this->source); })())), "html", null, true);
            yield " item(s) were removed from your cart because they are no longer available.
                            </p>
                        </div>
                    ";
        }
        // line 218
        yield "
                    <div class=\"row\">
                        <div class=\"col-lg-8\">
                            <!-- Billing Information -->
                            <div class=\"billing-info\">
                                <h5 class=\"mb-3\">
                                    <i class=\"fas fa-user me-2\"></i>Billing Information
                                </h5>
                                <div class=\"user-info\">
                                    <span><strong>Name:</strong> ";
        // line 227
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 227, $this->source); })()), "firstName", [], "any", false, false, false, 227), "html", null, true);
        yield " ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 227, $this->source); })()), "lastName", [], "any", false, false, false, 227), "html", null, true);
        yield "</span>
                                    <span><strong>Email:</strong> ";
        // line 228
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 228, $this->source); })()), "email", [], "any", false, false, false, 228), "html", null, true);
        yield "</span>
                                </div>
                                ";
        // line 230
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 230, $this->source); })()), "country", [], "any", false, false, false, 230)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 231
            yield "                                    <div class=\"user-info\">
                                        <span><strong>Country:</strong> ";
            // line 232
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 232, $this->source); })()), "country", [], "any", false, false, false, 232), "html", null, true);
            yield "</span>
                                        ";
            // line 233
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 233, $this->source); })()), "city", [], "any", false, false, false, 233)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 234
                yield "                                            <span><strong>City:</strong> ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 234, $this->source); })()), "city", [], "any", false, false, false, 234), "html", null, true);
                yield "</span>
                                        ";
            }
            // line 236
            yield "                                    </div>
                                ";
        }
        // line 238
        yield "                            </div>

                            <!-- Payment Method -->
                            <div class=\"payment-section\">
                                <h5 class=\"mb-4\">
                                    <i class=\"fas fa-credit-card me-2\"></i>Payment Method
                                </h5>
                                
                                <div class=\"payment-method selected\" data-method=\"paypal\">
                                    <input type=\"radio\" name=\"payment_method\" value=\"paypal\" checked>
                                    <div>
                                        <h6 class=\"mb-1\">PayPal</h6>
                                        <small class=\"text-muted\">Pay securely with your PayPal account</small>
                                    </div>
                                    <img src=\"https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-100px.png\" alt=\"PayPal\">
                                </div>

                                <div class=\"paypal-button-container\">
                                    <div id=\"paypal-button-container\"></div>
                                </div>

                                <div class=\"security-info\">
                                    <i class=\"fas fa-shield-alt\"></i>
                                    <strong>Secure Payment:</strong> Your payment information is encrypted and secure. 
                                    We do not store your payment details.
                                </div>
                            </div>
                        </div>

                        <div class=\"col-lg-4\">
                            <div class=\"order-summary\">
                                <h5 class=\"mb-4\">Order Summary</h5>
                                
                                <div class=\"order-items\">
                                    ";
        // line 272
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 272, $this->source); })()), "items", [], "any", false, false, false, 272));
        foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
            // line 273
            yield "                                        <div class=\"order-item\">
                                            <div class=\"item-info\">
                                                <h6>";
            // line 275
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 275), "html", null, true);
            yield "</h6>
                                                <span class=\"item-type\">";
            // line 276
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), Twig\Extension\CoreExtension::replace(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 276), ["_" => " "])), "html", null, true);
            yield "</span>
                                                ";
            // line 277
            if ((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 277) > 1)) {
                // line 278
                yield "                                                    <small class=\"text-muted d-block\">Qty: ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 278), "html", null, true);
                yield "</small>
                                                ";
            }
            // line 280
            yield "                                            </div>
                                            <div class=\"item-price\">
                                                \$";
            // line 282
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "price", [], "any", false, false, false, 282) * CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 282)), 2), "html", null, true);
            yield "
                                            </div>
                                        </div>
                                    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['item'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 286
        yield "                                </div>
                                
                                <div class=\"summary-totals mt-4\">
                                    <div class=\"summary-row\">
                                        <span>Subtotal (";
        // line 290
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 290, $this->source); })()), "total_items", [], "any", false, false, false, 290), "html", null, true);
        yield " items)</span>
                                        <span>\$";
        // line 291
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 291, $this->source); })()), "subtotal", [], "any", false, false, false, 291), 2), "html", null, true);
        yield "</span>
                                    </div>
                                    
                                    <div class=\"summary-row\">
                                        <span>Tax</span>
                                        <span>\$";
        // line 296
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 296, $this->source); })()), "tax", [], "any", false, false, false, 296), 2), "html", null, true);
        yield "</span>
                                    </div>
                                    
                                    <div class=\"summary-row total\">
                                        <span>Total</span>
                                        <span>\$";
        // line 301
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 301, $this->source); })()), "total", [], "any", false, false, false, 301), 2), "html", null, true);
        yield "</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Loading Overlay -->
    <div class=\"loading-overlay\" id=\"loading-overlay\">
        <div class=\"loading-content\">
            <div class=\"spinner\"></div>
            <h5>Processing Payment...</h5>
            <p class=\"text-muted\">Please wait while we process your payment securely.</p>
        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 322
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 323
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script src=\"https://www.paypal.com/sdk/js?client-id=sb&currency=USD\"></script>
    <script>
        // PayPal Button Integration
        paypal.Buttons({
            createOrder: function(data, actions) {
                document.getElementById('loading-overlay').style.display = 'flex';
                
                return fetch('";
        // line 331
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_checkout_create_order");
        yield "', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    
                    if (data.success) {
                        return data.order_id;
                    } else {
                        throw new Error(data.message || 'Failed to create order');
                    }
                })
                .catch(error => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    alert('Error creating order: ' + error.message);
                    throw error;
                });
            },
            
            onApprove: function(data, actions) {
                document.getElementById('loading-overlay').style.display = 'flex';
                
                return fetch('";
        // line 357
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_checkout_capture_order");
        yield "', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `order_id=\${data.orderID}`
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    
                    if (data.success) {
                        window.location.href = data.redirect_url;
                    } else {
                        alert('Payment processing failed: ' + data.message);
                    }
                })
                .catch(error => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    alert('Error processing payment: ' + error.message);
                });
            },
            
            onError: function(err) {
                document.getElementById('loading-overlay').style.display = 'none';
                console.error('PayPal error:', err);
                alert('An error occurred with PayPal. Please try again.');
            },
            
            onCancel: function(data) {
                document.getElementById('loading-overlay').style.display = 'none';
                window.location.href = '";
        // line 388
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_checkout_cancel");
        yield "';
            }
        }).render('#paypal-button-container');

        // Payment method selection
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input[type=\"radio\"]').checked = true;
            });
        });
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "checkout/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  607 => 388,  573 => 357,  544 => 331,  532 => 323,  519 => 322,  488 => 301,  480 => 296,  472 => 291,  468 => 290,  462 => 286,  452 => 282,  448 => 280,  442 => 278,  440 => 277,  436 => 276,  432 => 275,  428 => 273,  424 => 272,  388 => 238,  384 => 236,  378 => 234,  376 => 233,  372 => 232,  369 => 231,  367 => 230,  362 => 228,  356 => 227,  345 => 218,  338 => 214,  333 => 211,  331 => 210,  318 => 199,  305 => 198,  102 => 6,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Checkout - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .checkout-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .checkout-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .checkout-header {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .checkout-content {
            padding: 40px;
        }

        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            position: sticky;
            top: 100px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #a90418;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-weight: 600;
            color: #a90418;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
        }

        .summary-row.total {
            border-top: 2px solid #dee2e6;
            padding-top: 15px;
            font-size: 1.2rem;
            font-weight: 700;
            color: #011a2d;
        }

        .payment-section {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .payment-method {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-method:hover,
        .payment-method.selected {
            border-color: #011a2d;
            background: #f8f9fa;
        }

        .payment-method input[type=\"radio\"] {
            margin-right: 15px;
        }

        .payment-method img {
            height: 30px;
            margin-left: auto;
        }

        .paypal-button-container {
            margin-top: 20px;
        }

        .security-info {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .security-info i {
            color: #28a745;
            margin-right: 8px;
        }

        .billing-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #011a2d;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .checkout-content {
                padding: 20px;
            }
            
            .order-summary {
                position: static;
                margin-top: 30px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class=\"checkout-section\">
        <div class=\"container\">
            <div class=\"checkout-container\">
                <div class=\"checkout-header\">
                    <h1 class=\"mb-3\">
                        <i class=\"fas fa-credit-card me-3\"></i>Secure Checkout
                    </h1>
                    <p class=\"mb-0\">Complete your purchase securely with PayPal</p>
                </div>

                <div class=\"checkout-content\">
                    {% if removed_items|length > 0 %}
                        <div class=\"alert alert-warning\">
                            <h6><i class=\"fas fa-exclamation-triangle me-2\"></i>Items Removed</h6>
                            <p class=\"mb-0\">
                                {{ removed_items|length }} item(s) were removed from your cart because they are no longer available.
                            </p>
                        </div>
                    {% endif %}

                    <div class=\"row\">
                        <div class=\"col-lg-8\">
                            <!-- Billing Information -->
                            <div class=\"billing-info\">
                                <h5 class=\"mb-3\">
                                    <i class=\"fas fa-user me-2\"></i>Billing Information
                                </h5>
                                <div class=\"user-info\">
                                    <span><strong>Name:</strong> {{ user.firstName }} {{ user.lastName }}</span>
                                    <span><strong>Email:</strong> {{ user.email }}</span>
                                </div>
                                {% if user.country %}
                                    <div class=\"user-info\">
                                        <span><strong>Country:</strong> {{ user.country }}</span>
                                        {% if user.city %}
                                            <span><strong>City:</strong> {{ user.city }}</span>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Payment Method -->
                            <div class=\"payment-section\">
                                <h5 class=\"mb-4\">
                                    <i class=\"fas fa-credit-card me-2\"></i>Payment Method
                                </h5>
                                
                                <div class=\"payment-method selected\" data-method=\"paypal\">
                                    <input type=\"radio\" name=\"payment_method\" value=\"paypal\" checked>
                                    <div>
                                        <h6 class=\"mb-1\">PayPal</h6>
                                        <small class=\"text-muted\">Pay securely with your PayPal account</small>
                                    </div>
                                    <img src=\"https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-100px.png\" alt=\"PayPal\">
                                </div>

                                <div class=\"paypal-button-container\">
                                    <div id=\"paypal-button-container\"></div>
                                </div>

                                <div class=\"security-info\">
                                    <i class=\"fas fa-shield-alt\"></i>
                                    <strong>Secure Payment:</strong> Your payment information is encrypted and secure. 
                                    We do not store your payment details.
                                </div>
                            </div>
                        </div>

                        <div class=\"col-lg-4\">
                            <div class=\"order-summary\">
                                <h5 class=\"mb-4\">Order Summary</h5>
                                
                                <div class=\"order-items\">
                                    {% for item in cart.items %}
                                        <div class=\"order-item\">
                                            <div class=\"item-info\">
                                                <h6>{{ item.title }}</h6>
                                                <span class=\"item-type\">{{ item.type|replace({'_': ' '})|title }}</span>
                                                {% if item.quantity > 1 %}
                                                    <small class=\"text-muted d-block\">Qty: {{ item.quantity }}</small>
                                                {% endif %}
                                            </div>
                                            <div class=\"item-price\">
                                                \${{ (item.price * item.quantity)|number_format(2) }}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                
                                <div class=\"summary-totals mt-4\">
                                    <div class=\"summary-row\">
                                        <span>Subtotal ({{ cart.total_items }} items)</span>
                                        <span>\${{ cart.subtotal|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class=\"summary-row\">
                                        <span>Tax</span>
                                        <span>\${{ cart.tax|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class=\"summary-row total\">
                                        <span>Total</span>
                                        <span>\${{ cart.total|number_format(2) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Loading Overlay -->
    <div class=\"loading-overlay\" id=\"loading-overlay\">
        <div class=\"loading-content\">
            <div class=\"spinner\"></div>
            <h5>Processing Payment...</h5>
            <p class=\"text-muted\">Please wait while we process your payment securely.</p>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src=\"https://www.paypal.com/sdk/js?client-id=sb&currency=USD\"></script>
    <script>
        // PayPal Button Integration
        paypal.Buttons({
            createOrder: function(data, actions) {
                document.getElementById('loading-overlay').style.display = 'flex';
                
                return fetch('{{ path('app_checkout_create_order') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    
                    if (data.success) {
                        return data.order_id;
                    } else {
                        throw new Error(data.message || 'Failed to create order');
                    }
                })
                .catch(error => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    alert('Error creating order: ' + error.message);
                    throw error;
                });
            },
            
            onApprove: function(data, actions) {
                document.getElementById('loading-overlay').style.display = 'flex';
                
                return fetch('{{ path('app_checkout_capture_order') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `order_id=\${data.orderID}`
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    
                    if (data.success) {
                        window.location.href = data.redirect_url;
                    } else {
                        alert('Payment processing failed: ' + data.message);
                    }
                })
                .catch(error => {
                    document.getElementById('loading-overlay').style.display = 'none';
                    alert('Error processing payment: ' + error.message);
                });
            },
            
            onError: function(err) {
                document.getElementById('loading-overlay').style.display = 'none';
                console.error('PayPal error:', err);
                alert('An error occurred with PayPal. Please try again.');
            },
            
            onCancel: function(data) {
                document.getElementById('loading-overlay').style.display = 'none';
                window.location.href = '{{ path('app_checkout_cancel') }}';
            }
        }).render('#paypal-button-container');

        // Payment method selection
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input[type=\"radio\"]').checked = true;
            });
        });
    </script>
{% endblock %}
", "checkout/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\checkout\\index.html.twig");
    }
}
