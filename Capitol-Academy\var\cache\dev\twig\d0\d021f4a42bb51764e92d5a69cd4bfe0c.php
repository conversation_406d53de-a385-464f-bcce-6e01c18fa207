<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/edit.html.twig */
class __TwigTemplate_9a97b85801d75e4a035de3db29911f67 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Onsite Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Onsite Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Edit ";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Onsite Course: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 37, $this->source); })()), "code", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0 me-2\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                        <!-- Preview Button -->
                        <a href=\"";
        // line 52
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 52, $this->source); })()), "code", [], "any", false, false, false, 52)]), "html", null, true);
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; border: 2px solid #17a2b8; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#138496';\"
                           onmouseout=\"this.style.background='#17a2b8';\">
                            <i class=\"fas fa-eye me-2\"></i>
                            Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"onsite-course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 66
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("onsite_course_edit"), "html", null, true);
        yield "\">
            <div class=\"card-body\">
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Code <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"code\"
                                           name=\"code\"
                                           value=\"";
        // line 83
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 83, $this->source); })()), "code", [], "any", false, false, false, 83), "html", null, true);
        yield "\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Title <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"title\"
                                           name=\"title\"
                                           value=\"";
        // line 104
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 104, $this->source); })()), "title", [], "any", false, false, false, 104), "html", null, true);
        yield "\"
                                           placeholder=\"Enter onsite course title\"
                                           required
                                           maxlength=\"255\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group mb-3\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Description
                            </label>
                            <textarea class=\"form-control\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Enter onsite course description\"
                                      maxlength=\"2000\">";
        // line 126
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 126, $this->source); })()), "description", [], "any", false, false, false, 126), "html", null, true);
        yield "</textarea>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Category <span class=\"text-danger\">*</span>
                                    </label>
                                    <select class=\"form-select\" id=\"category\" name=\"category\" required>
                                        <option value=\"\">Select category</option>
                                        ";
        // line 139
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 139, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 140
            yield "                                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 140), "html", null, true);
            yield "\" ";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 140, $this->source); })()), "category", [], "any", false, false, false, 140) == CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 140))) ? ("selected") : (""));
            yield ">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 140), "html", null, true);
            yield "</option>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 142
        yield "                                    </select>
                                    <div class=\"invalid-feedback\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Difficulty Level
                                    </label>
                                    <select class=\"form-select\" id=\"level\" name=\"level\">
                                        <option value=\"\">Select level</option>
                                        <option value=\"Beginner\" ";
        // line 157
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 157, $this->source); })()), "level", [], "any", false, false, false, 157) == "Beginner")) ? ("selected") : (""));
        yield ">Beginner</option>
                                        <option value=\"Intermediate\" ";
        // line 158
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 158, $this->source); })()), "level", [], "any", false, false, false, 158) == "Intermediate")) ? ("selected") : (""));
        yield ">Intermediate</option>
                                        <option value=\"Advanced\" ";
        // line 159
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 159, $this->source); })()), "level", [], "any", false, false, false, 159) == "Advanced")) ? ("selected") : (""));
        yield ">Advanced</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Duration and Price Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"duration\" class=\"form-label\">
                                        <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Duration (minutes)
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"duration\"
                                           name=\"duration\"
                                           value=\"";
        // line 177
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 177, $this->source); })()), "duration", [], "any", false, false, false, 177), "html", null, true);
        yield "\"
                                           placeholder=\"Enter duration in minutes\"
                                           min=\"0\">
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"price\" class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Price (USD) <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"price\"
                                           name=\"price\"
                                           value=\"";
        // line 193
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 193, $this->source); })()), "price", [], "any", false, false, false, 193), "html", null, true);
        yield "\"
                                           placeholder=\"0.00\"
                                           step=\"0.01\"
                                           min=\"0\"
                                           required>
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid price.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Uploads Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"thumbnail_image\" class=\"form-label\">
                                        <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Thumbnail Image
                                    </label>
                                    ";
        // line 213
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 213, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 213)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 214
            yield "                                        <div class=\"mb-2\">
                                            <img src=\"";
            // line 215
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 215, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 215), "html", null, true);
            yield "\" alt=\"Current thumbnail\" class=\"img-thumbnail\" style=\"max-width: 150px;\">
                                            <small class=\"text-muted d-block\">Current thumbnail</small>
                                        </div>
                                    ";
        }
        // line 219
        yield "                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"thumbnail_image\"
                                           name=\"thumbnail_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a new thumbnail image to replace the current one. Max size: 5MB</div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"banner_image\" class=\"form-label\">
                                        <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Banner Image
                                    </label>
                                    ";
        // line 234
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 234, $this->source); })()), "bannerImage", [], "any", false, false, false, 234)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 235
            yield "                                        <div class=\"mb-2\">
                                            <img src=\"/uploads/courses/";
            // line 236
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 236, $this->source); })()), "bannerImage", [], "any", false, false, false, 236), "html", null, true);
            yield "\" alt=\"Current banner\" class=\"img-thumbnail\" style=\"max-width: 200px;\">
                                            <small class=\"text-muted d-block\">Current banner</small>
                                        </div>
                                    ";
        }
        // line 240
        yield "                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"banner_image\"
                                           name=\"banner_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a new banner image to replace the current one. Max size: 10MB</div>
                                </div>
                            </div>
                        </div>

                        <!-- Status and Modules Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"is_active\" name=\"is_active\" value=\"1\" ";
        // line 255
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 255, $this->source); })()), "isActive", [], "any", false, false, false, 255)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("checked") : (""));
        yield ">
                                        <label class=\"form-check-label\" for=\"is_active\">
                                            <i class=\"fas fa-toggle-on\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Active
                                        </label>
                                        <div class=\"form-text\">Check this to make the onsite course visible to students</div>
                                    </div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" ";
        // line 268
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 268, $this->source); })()), "hasModules", [], "any", false, false, false, 268)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("checked") : (""));
        yield ">
                                        <label class=\"form-check-label\" for=\"has_modules\">
                                            <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Enable Modules
                                        </label>
                                        <div class=\"form-text\">Check this to enable modular structure for this onsite course</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Footer -->
            <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"";
        // line 285
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\" class=\"btn btn-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <button type=\"submit\" class=\"btn btn-primary\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border: none; padding: 0.75rem 2rem;\">
                        <i class=\"fas fa-save me-2\"></i>Update Onsite Course
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  533 => 285,  513 => 268,  497 => 255,  480 => 240,  473 => 236,  470 => 235,  468 => 234,  451 => 219,  444 => 215,  441 => 214,  439 => 213,  416 => 193,  397 => 177,  376 => 159,  372 => 158,  368 => 157,  351 => 142,  338 => 140,  334 => 139,  318 => 126,  293 => 104,  269 => 83,  249 => 66,  232 => 52,  220 => 43,  211 => 37,  201 => 29,  191 => 25,  188 => 24,  184 => 23,  181 => 22,  171 => 18,  168 => 17,  164 => 16,  160 => 14,  147 => 13,  134 => 10,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Onsite Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Onsite Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Edit {{ course.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Onsite Course: {{ course.code }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0 me-2\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                        <!-- Preview Button -->
                        <a href=\"{{ path('admin_onsite_course_preview', {'code': course.code}) }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; border: 2px solid #17a2b8; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#138496';\"
                           onmouseout=\"this.style.background='#17a2b8';\">
                            <i class=\"fas fa-eye me-2\"></i>
                            Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"onsite-course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('onsite_course_edit') }}\">
            <div class=\"card-body\">
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Code <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"code\"
                                           name=\"code\"
                                           value=\"{{ course.code }}\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Title <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"title\"
                                           name=\"title\"
                                           value=\"{{ course.title }}\"
                                           placeholder=\"Enter onsite course title\"
                                           required
                                           maxlength=\"255\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group mb-3\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Description
                            </label>
                            <textarea class=\"form-control\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Enter onsite course description\"
                                      maxlength=\"2000\">{{ course.description }}</textarea>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Category <span class=\"text-danger\">*</span>
                                    </label>
                                    <select class=\"form-select\" id=\"category\" name=\"category\" required>
                                        <option value=\"\">Select category</option>
                                        {% for category in categories %}
                                            <option value=\"{{ category.name }}\" {{ course.category == category.name ? 'selected' : '' }}>{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class=\"invalid-feedback\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Difficulty Level
                                    </label>
                                    <select class=\"form-select\" id=\"level\" name=\"level\">
                                        <option value=\"\">Select level</option>
                                        <option value=\"Beginner\" {{ course.level == 'Beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value=\"Intermediate\" {{ course.level == 'Intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value=\"Advanced\" {{ course.level == 'Advanced' ? 'selected' : '' }}>Advanced</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Duration and Price Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"duration\" class=\"form-label\">
                                        <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Duration (minutes)
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"duration\"
                                           name=\"duration\"
                                           value=\"{{ course.duration }}\"
                                           placeholder=\"Enter duration in minutes\"
                                           min=\"0\">
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"price\" class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Price (USD) <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"price\"
                                           name=\"price\"
                                           value=\"{{ course.price }}\"
                                           placeholder=\"0.00\"
                                           step=\"0.01\"
                                           min=\"0\"
                                           required>
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid price.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Uploads Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"thumbnail_image\" class=\"form-label\">
                                        <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Thumbnail Image
                                    </label>
                                    {% if course.thumbnailImage %}
                                        <div class=\"mb-2\">
                                            <img src=\"{{ course.thumbnailUrl }}\" alt=\"Current thumbnail\" class=\"img-thumbnail\" style=\"max-width: 150px;\">
                                            <small class=\"text-muted d-block\">Current thumbnail</small>
                                        </div>
                                    {% endif %}
                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"thumbnail_image\"
                                           name=\"thumbnail_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a new thumbnail image to replace the current one. Max size: 5MB</div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"banner_image\" class=\"form-label\">
                                        <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Banner Image
                                    </label>
                                    {% if course.bannerImage %}
                                        <div class=\"mb-2\">
                                            <img src=\"/uploads/courses/{{ course.bannerImage }}\" alt=\"Current banner\" class=\"img-thumbnail\" style=\"max-width: 200px;\">
                                            <small class=\"text-muted d-block\">Current banner</small>
                                        </div>
                                    {% endif %}
                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"banner_image\"
                                           name=\"banner_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a new banner image to replace the current one. Max size: 10MB</div>
                                </div>
                            </div>
                        </div>

                        <!-- Status and Modules Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"is_active\" name=\"is_active\" value=\"1\" {{ course.isActive ? 'checked' : '' }}>
                                        <label class=\"form-check-label\" for=\"is_active\">
                                            <i class=\"fas fa-toggle-on\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Active
                                        </label>
                                        <div class=\"form-text\">Check this to make the onsite course visible to students</div>
                                    </div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" {{ course.hasModules ? 'checked' : '' }}>
                                        <label class=\"form-check-label\" for=\"has_modules\">
                                            <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Enable Modules
                                        </label>
                                        <div class=\"form-text\">Check this to enable modular structure for this onsite course</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Footer -->
            <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"{{ path('admin_onsite_courses') }}\" class=\"btn btn-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <button type=\"submit\" class=\"btn btn-primary\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border: none; padding: 0.75rem 2rem;\">
                        <i class=\"fas fa-save me-2\"></i>Update Onsite Course
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
", "admin/onsite_courses/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\edit.html.twig");
    }
}
