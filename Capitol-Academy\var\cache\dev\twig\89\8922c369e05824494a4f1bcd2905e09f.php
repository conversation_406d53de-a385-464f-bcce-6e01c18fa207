<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* user/profile.html.twig */
class __TwigTemplate_34131605fa3d0cc0390bca20193fb0ed extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "user/profile.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "user/profile.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "My Profile - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Manage your Capitol Academy profile, update personal information, and track your trading education progress.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 8
        yield "<div class=\"container-fluid\">
    <!-- Profile Header Section -->
    <section class=\"py-5\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">
                        <i class=\"fas fa-user-circle me-3\"></i>
                        My Profile
                    </h1>
                    <p class=\"lead mb-4\">
                        Manage your Capitol Academy account settings and track your trading education journey.
                    </p>
                    <nav aria-label=\"breadcrumb\">
                        <ol class=\"breadcrumb bg-transparent p-0 mb-0\">
                            <li class=\"breadcrumb-item\">
                                <a href=\"";
        // line 24
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_home");
        yield "\" class=\"text-white-50 text-decoration-none\">
                                    <i class=\"fas fa-home me-1\"></i>Dashboard
                                </a>
                            </li>
                            <li class=\"breadcrumb-item active text-white\" aria-current=\"page\">Profile</li>
                        </ol>
                    </nav>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <div class=\"user-avatar-large\">
                        ";
        // line 34
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 34, $this->source); })()), "profilePicture", [], "any", false, false, false, 34)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 35
            yield "                            <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("images/uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 35, $this->source); })()), "profilePicture", [], "any", false, false, false, 35))), "html", null, true);
            yield "\" 
                                 alt=\"Profile Picture\" 
                                 class=\"rounded-circle img-fluid shadow-lg\"
                                 style=\"max-width: 200px; border: 5px solid rgba(255,255,255,0.3);\">
                        ";
        } else {
            // line 40
            yield "                            <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/user-default-pp.png"), "html", null, true);
            yield "\" 
                                 alt=\"Default Profile\" 
                                 class=\"rounded-circle img-fluid shadow-lg\"
                                 style=\"max-width: 200px; border: 5px solid rgba(255,255,255,0.3);\">
                        ";
        }
        // line 45
        yield "                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- Profile Information Card -->
                <div class=\"col-lg-8 mb-4\">
                    <div class=\"card shadow-sm border-0\">
                        <div class=\"card-header\" style=\"background: #011a2d; color: white;\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-user me-2\"></i>
                                Profile Information
                            </h5>
                        </div>
                        <div class=\"card-body p-4\">
                            <div class=\"row\">
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">First Name</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        ";
        // line 69
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 69, $this->source); })()), "firstName", [], "any", false, false, false, 69), "html", null, true);
        yield "
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Last Name</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        ";
        // line 75
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 75, $this->source); })()), "lastName", [], "any", false, false, false, 75), "html", null, true);
        yield "
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Email Address</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        <i class=\"fas fa-envelope me-2 text-primary\"></i>
                                        ";
        // line 82
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 82, $this->source); })()), "email", [], "any", false, false, false, 82), "html", null, true);
        yield "
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Phone Number</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        ";
        // line 88
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 88, $this->source); })()), "phone", [], "any", false, false, false, 88)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 89
            yield "                                            <i class=\"fas fa-phone me-2 text-success\"></i>
                                            ";
            // line 90
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 90, $this->source); })()), "phone", [], "any", false, false, false, 90), "html", null, true);
            yield "
                                        ";
        } else {
            // line 92
            yield "                                            <span class=\"text-muted\">Not provided</span>
                                        ";
        }
        // line 94
        yield "                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Country</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        ";
        // line 99
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 99, $this->source); })()), "country", [], "any", false, false, false, 99)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 100
            yield "                                            <i class=\"fas fa-flag me-2 text-info\"></i>
                                            ";
            // line 101
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 101, $this->source); })()), "country", [], "any", false, false, false, 101), "name", [], "any", false, false, false, 101), "html", null, true);
            yield "
                                        ";
        } else {
            // line 103
            yield "                                            <span class=\"text-muted\">Not specified</span>
                                        ";
        }
        // line 105
        yield "                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Member Since</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        <i class=\"fas fa-calendar me-2 text-warning\"></i>
                                        ";
        // line 111
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 111, $this->source); })()), "createdAt", [], "any", false, false, false, 111), "F j, Y"), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class=\"d-flex flex-wrap gap-3 mt-4\">
                                <button class=\"btn btn-primary btn-lg\" onclick=\"enableEditMode()\">
                                    <i class=\"fas fa-edit me-2\"></i>
                                    Edit Profile
                                </button>
                                <button class=\"btn btn-outline-secondary btn-lg\" onclick=\"changePassword()\">
                                    <i class=\"fas fa-key me-2\"></i>
                                    Change Password
                                </button>
                                <button class=\"btn btn-outline-info btn-lg\" onclick=\"uploadProfilePicture()\">
                                    <i class=\"fas fa-camera me-2\"></i>
                                    Update Photo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Status & Statistics -->
                <div class=\"col-lg-4\">
                    <!-- Account Status Card -->
                    <div class=\"card shadow-sm border-0 mb-4\">
                        <div class=\"card-header\" style=\"background: #a90418; color: white;\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-shield-alt me-2\"></i>
                                Account Status
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"mb-3\">
                                <strong>Verification Status:</strong><br>
                                ";
        // line 148
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 148, $this->source); })()), "isVerified", [], "any", false, false, false, 148)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 149
            yield "                                    <span class=\"badge bg-success fs-6 mt-1\">
                                        <i class=\"fas fa-check-circle me-1\"></i>Verified
                                    </span>
                                ";
        } else {
            // line 153
            yield "                                    <span class=\"badge bg-warning fs-6 mt-1\">
                                        <i class=\"fas fa-clock me-1\"></i>Pending Verification
                                    </span>
                                ";
        }
        // line 157
        yield "                            </div>
                            <div class=\"mb-3\">
                                <strong>Account Status:</strong><br>
                                ";
        // line 160
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 160, $this->source); })()), "isBlocked", [], "any", false, false, false, 160)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 161
            yield "                                    <span class=\"badge bg-danger fs-6 mt-1\">
                                        <i class=\"fas fa-ban me-1\"></i>Blocked
                                    </span>
                                ";
        } else {
            // line 165
            yield "                                    <span class=\"badge bg-success fs-6 mt-1\">
                                        <i class=\"fas fa-check me-1\"></i>Active
                                    </span>
                                ";
        }
        // line 169
        yield "                            </div>
                            <div class=\"mb-3\">
                                <strong>User Role:</strong><br>
                                <span class=\"badge bg-primary fs-6 mt-1\">
                                    <i class=\"fas fa-user me-1\"></i>Student
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class=\"card shadow-sm border-0 mb-4\">
                        <div class=\"card-header bg-light\">
                            <h5 class=\"card-title mb-0 text-dark\">
                                <i class=\"fas fa-bolt me-2\"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"d-grid gap-2\">
                                <a href=\"";
        // line 189
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>
                                    Browse Courses
                                </a>
                                <a href=\"";
        // line 193
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_courses");
        yield "\" class=\"btn btn-outline-success\">
                                    <i class=\"fas fa-book me-2\"></i>
                                    My Courses
                                </a>
                                <a href=\"";
        // line 197
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_orders");
        yield "\" class=\"btn btn-outline-info\">
                                    <i class=\"fas fa-shopping-cart me-2\"></i>
                                    Order History
                                </a>
                                <a href=\"";
        // line 201
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_free_videos");
        yield "\" class=\"btn btn-outline-warning\">
                                    <i class=\"fas fa-play me-2\"></i>
                                    Free Videos
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Trading Progress Card -->
                    <div class=\"card shadow-sm border-0\">
                        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-chart-line me-2\"></i>
                                Trading Progress
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"mb-3\">
                                <small class=\"text-muted\">Courses Enrolled</small>
                                <div class=\"progress mt-1\" style=\"height: 8px;\">
                                    <div class=\"progress-bar bg-success\" role=\"progressbar\" style=\"width: 0%\"></div>
                                </div>
                                <small class=\"text-muted\">0 courses</small>
                            </div>
                            <div class=\"mb-3\">
                                <small class=\"text-muted\">Videos Watched</small>
                                <div class=\"progress mt-1\" style=\"height: 8px;\">
                                    <div class=\"progress-bar bg-info\" role=\"progressbar\" style=\"width: 0%\"></div>
                                </div>
                                <small class=\"text-muted\">0 videos</small>
                            </div>
                            <div class=\"text-center mt-3\">
                                <a href=\"";
        // line 233
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_home");
        yield "\" class=\"btn btn-sm btn-outline-primary\">
                                    <i class=\"fas fa-tachometer-alt me-1\"></i>
                                    View Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Profile Edit Modal (Placeholder for future enhancement) -->
<div class=\"modal fade\" id=\"editProfileModal\" tabindex=\"-1\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-lg\">
        <div class=\"modal-content\">
            <div class=\"modal-header\" style=\"background: #011a2d; color: white;\">
                <h5 class=\"modal-title\">
                    <i class=\"fas fa-edit me-2\"></i>
                    Edit Profile
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\"></button>
            </div>
            <div class=\"modal-body\">
                <p class=\"text-muted\">Profile editing functionality will be available soon. Please contact support for any profile changes.</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function enableEditMode() {
    // Show modal for now - can be enhanced later with actual edit form
    var editModal = new bootstrap.Modal(document.getElementById('editProfileModal'));
    editModal.show();
}

function changePassword() {
    alert('Password change functionality will be available soon. Please contact support.');
}

function uploadProfilePicture() {
    alert('Profile picture upload will be available soon. Please contact support.');
}
</script>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.form-control-plaintext {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
}

.badge {
    font-size: 0.875rem;
}

.progress {
    background-color: #e9ecef;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "user/profile.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  423 => 233,  388 => 201,  381 => 197,  374 => 193,  367 => 189,  345 => 169,  339 => 165,  333 => 161,  331 => 160,  326 => 157,  320 => 153,  314 => 149,  312 => 148,  272 => 111,  264 => 105,  260 => 103,  255 => 101,  252 => 100,  250 => 99,  243 => 94,  239 => 92,  234 => 90,  231 => 89,  229 => 88,  220 => 82,  210 => 75,  201 => 69,  175 => 45,  166 => 40,  157 => 35,  155 => 34,  142 => 24,  124 => 8,  111 => 7,  88 => 5,  65 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}My Profile - Capitol Academy{% endblock %}

{% block meta_description %}Manage your Capitol Academy profile, update personal information, and track your trading education progress.{% endblock %}

{% block body %}
<div class=\"container-fluid\">
    <!-- Profile Header Section -->
    <section class=\"py-5\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">
                        <i class=\"fas fa-user-circle me-3\"></i>
                        My Profile
                    </h1>
                    <p class=\"lead mb-4\">
                        Manage your Capitol Academy account settings and track your trading education journey.
                    </p>
                    <nav aria-label=\"breadcrumb\">
                        <ol class=\"breadcrumb bg-transparent p-0 mb-0\">
                            <li class=\"breadcrumb-item\">
                                <a href=\"{{ path('app_user_home') }}\" class=\"text-white-50 text-decoration-none\">
                                    <i class=\"fas fa-home me-1\"></i>Dashboard
                                </a>
                            </li>
                            <li class=\"breadcrumb-item active text-white\" aria-current=\"page\">Profile</li>
                        </ol>
                    </nav>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <div class=\"user-avatar-large\">
                        {% if user.profilePicture %}
                            <img src=\"{{ asset('images/uploads/profiles/' ~ user.profilePicture) }}\" 
                                 alt=\"Profile Picture\" 
                                 class=\"rounded-circle img-fluid shadow-lg\"
                                 style=\"max-width: 200px; border: 5px solid rgba(255,255,255,0.3);\">
                        {% else %}
                            <img src=\"{{ asset('images/user-default-pp.png') }}\" 
                                 alt=\"Default Profile\" 
                                 class=\"rounded-circle img-fluid shadow-lg\"
                                 style=\"max-width: 200px; border: 5px solid rgba(255,255,255,0.3);\">
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- Profile Information Card -->
                <div class=\"col-lg-8 mb-4\">
                    <div class=\"card shadow-sm border-0\">
                        <div class=\"card-header\" style=\"background: #011a2d; color: white;\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-user me-2\"></i>
                                Profile Information
                            </h5>
                        </div>
                        <div class=\"card-body p-4\">
                            <div class=\"row\">
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">First Name</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        {{ user.firstName }}
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Last Name</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        {{ user.lastName }}
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Email Address</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        <i class=\"fas fa-envelope me-2 text-primary\"></i>
                                        {{ user.email }}
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Phone Number</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        {% if user.phone %}
                                            <i class=\"fas fa-phone me-2 text-success\"></i>
                                            {{ user.phone }}
                                        {% else %}
                                            <span class=\"text-muted\">Not provided</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Country</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        {% if user.country %}
                                            <i class=\"fas fa-flag me-2 text-info\"></i>
                                            {{ user.country.name }}
                                        {% else %}
                                            <span class=\"text-muted\">Not specified</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class=\"col-md-6 mb-4\">
                                    <label class=\"form-label fw-bold text-muted\">Member Since</label>
                                    <div class=\"form-control-plaintext border rounded p-3 bg-light\">
                                        <i class=\"fas fa-calendar me-2 text-warning\"></i>
                                        {{ user.createdAt|date('F j, Y') }}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class=\"d-flex flex-wrap gap-3 mt-4\">
                                <button class=\"btn btn-primary btn-lg\" onclick=\"enableEditMode()\">
                                    <i class=\"fas fa-edit me-2\"></i>
                                    Edit Profile
                                </button>
                                <button class=\"btn btn-outline-secondary btn-lg\" onclick=\"changePassword()\">
                                    <i class=\"fas fa-key me-2\"></i>
                                    Change Password
                                </button>
                                <button class=\"btn btn-outline-info btn-lg\" onclick=\"uploadProfilePicture()\">
                                    <i class=\"fas fa-camera me-2\"></i>
                                    Update Photo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Status & Statistics -->
                <div class=\"col-lg-4\">
                    <!-- Account Status Card -->
                    <div class=\"card shadow-sm border-0 mb-4\">
                        <div class=\"card-header\" style=\"background: #a90418; color: white;\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-shield-alt me-2\"></i>
                                Account Status
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"mb-3\">
                                <strong>Verification Status:</strong><br>
                                {% if user.isVerified %}
                                    <span class=\"badge bg-success fs-6 mt-1\">
                                        <i class=\"fas fa-check-circle me-1\"></i>Verified
                                    </span>
                                {% else %}
                                    <span class=\"badge bg-warning fs-6 mt-1\">
                                        <i class=\"fas fa-clock me-1\"></i>Pending Verification
                                    </span>
                                {% endif %}
                            </div>
                            <div class=\"mb-3\">
                                <strong>Account Status:</strong><br>
                                {% if user.isBlocked %}
                                    <span class=\"badge bg-danger fs-6 mt-1\">
                                        <i class=\"fas fa-ban me-1\"></i>Blocked
                                    </span>
                                {% else %}
                                    <span class=\"badge bg-success fs-6 mt-1\">
                                        <i class=\"fas fa-check me-1\"></i>Active
                                    </span>
                                {% endif %}
                            </div>
                            <div class=\"mb-3\">
                                <strong>User Role:</strong><br>
                                <span class=\"badge bg-primary fs-6 mt-1\">
                                    <i class=\"fas fa-user me-1\"></i>Student
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class=\"card shadow-sm border-0 mb-4\">
                        <div class=\"card-header bg-light\">
                            <h5 class=\"card-title mb-0 text-dark\">
                                <i class=\"fas fa-bolt me-2\"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"d-grid gap-2\">
                                <a href=\"{{ path('app_courses') }}\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>
                                    Browse Courses
                                </a>
                                <a href=\"{{ path('app_user_courses') }}\" class=\"btn btn-outline-success\">
                                    <i class=\"fas fa-book me-2\"></i>
                                    My Courses
                                </a>
                                <a href=\"{{ path('app_user_orders') }}\" class=\"btn btn-outline-info\">
                                    <i class=\"fas fa-shopping-cart me-2\"></i>
                                    Order History
                                </a>
                                <a href=\"{{ path('app_free_videos') }}\" class=\"btn btn-outline-warning\">
                                    <i class=\"fas fa-play me-2\"></i>
                                    Free Videos
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Trading Progress Card -->
                    <div class=\"card shadow-sm border-0\">
                        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-chart-line me-2\"></i>
                                Trading Progress
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"mb-3\">
                                <small class=\"text-muted\">Courses Enrolled</small>
                                <div class=\"progress mt-1\" style=\"height: 8px;\">
                                    <div class=\"progress-bar bg-success\" role=\"progressbar\" style=\"width: 0%\"></div>
                                </div>
                                <small class=\"text-muted\">0 courses</small>
                            </div>
                            <div class=\"mb-3\">
                                <small class=\"text-muted\">Videos Watched</small>
                                <div class=\"progress mt-1\" style=\"height: 8px;\">
                                    <div class=\"progress-bar bg-info\" role=\"progressbar\" style=\"width: 0%\"></div>
                                </div>
                                <small class=\"text-muted\">0 videos</small>
                            </div>
                            <div class=\"text-center mt-3\">
                                <a href=\"{{ path('app_user_home') }}\" class=\"btn btn-sm btn-outline-primary\">
                                    <i class=\"fas fa-tachometer-alt me-1\"></i>
                                    View Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Profile Edit Modal (Placeholder for future enhancement) -->
<div class=\"modal fade\" id=\"editProfileModal\" tabindex=\"-1\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-lg\">
        <div class=\"modal-content\">
            <div class=\"modal-header\" style=\"background: #011a2d; color: white;\">
                <h5 class=\"modal-title\">
                    <i class=\"fas fa-edit me-2\"></i>
                    Edit Profile
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\"></button>
            </div>
            <div class=\"modal-body\">
                <p class=\"text-muted\">Profile editing functionality will be available soon. Please contact support for any profile changes.</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function enableEditMode() {
    // Show modal for now - can be enhanced later with actual edit form
    var editModal = new bootstrap.Modal(document.getElementById('editProfileModal'));
    editModal.show();
}

function changePassword() {
    alert('Password change functionality will be available soon. Please contact support.');
}

function uploadProfilePicture() {
    alert('Profile picture upload will be available soon. Please contact support.');
}
</script>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.form-control-plaintext {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
}

.badge {
    font-size: 0.875rem;
}

.progress {
    background-color: #e9ecef;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
", "user/profile.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\user\\profile.html.twig");
    }
}
