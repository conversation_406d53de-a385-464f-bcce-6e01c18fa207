<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/partners/create.html.twig */
class __TwigTemplate_266fbb76696917af5c8cdc7e5835d05d extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/create.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/create.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Partner - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create New Partner";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_partners");
        yield "\">Partners</a></li>
<li class=\"breadcrumb-item active\">Create Partner</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-handshake mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Partner
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Partners Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_partners");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Partners
                        </a>
                    </div>
                </div>
            </div>
        </div>

        ";
        // line 56
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 56, $this->source); })()), 'form_start', ["attr" => ["class" => "needs-validation", "novalidate" => true, "enctype" => "multipart/form-data"]]);
        yield "
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Partner Name -->
                            <div class=\"form-group\">
                                <label for=\"";
        // line 63
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 63, $this->source); })()), "name", [], "any", false, false, false, 63), "vars", [], "any", false, false, false, 63), "id", [], "any", false, false, false, 63), "html", null, true);
        yield "\" class=\"form-label\">
                                    <i class=\"fas fa-building text-primary mr-1\"></i>
                                    Partner Name <span class=\"text-danger\">*</span>
                                </label>
                                ";
        // line 67
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 67, $this->source); })()), "name", [], "any", false, false, false, 67), 'widget', ["attr" => ["class" => "form-control enhanced-field", "style" => "height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;", "placeholder" => "Enter partner organization name", "required" => "required"]]);
        yield "
                                <div class=\"invalid-feedback\">
                                    Please provide a partner name.
                                </div>
                                ";
        // line 71
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 71, $this->source); })()), "name", [], "any", false, false, false, 71), 'errors');
        yield "
                            </div>

                            <!-- Website URL and Display Order Row -->
                            <div class=\"row\">
                                <!-- Website URL -->
                                <div class=\"col-md-8\">
                                    <div class=\"form-group\">
                                        <label for=\"";
        // line 79
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 79, $this->source); })()), "websiteUrl", [], "any", false, false, false, 79), "vars", [], "any", false, false, false, 79), "id", [], "any", false, false, false, 79), "html", null, true);
        yield "\" class=\"form-label\">
                                            <i class=\"fas fa-globe text-primary mr-1\"></i>
                                            Website URL
                                        </label>
                                        ";
        // line 83
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 83, $this->source); })()), "websiteUrl", [], "any", false, false, false, 83), 'widget', ["attr" => ["class" => "form-control enhanced-field", "style" => "height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;", "placeholder" => "https://www.partner-website.com"]]);
        yield "
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Partner's official website URL.
                                        </small>
                                        ";
        // line 87
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 87, $this->source); })()), "websiteUrl", [], "any", false, false, false, 87), 'errors');
        yield "
                                    </div>
                                </div>

                                <!-- Display Order -->
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label for=\"displayOrder\" class=\"form-label\">
                                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                                            Display Order
                                        </label>
                                        ";
        // line 98
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 98, $this->source); })()), "displayOrder", [], "any", false, false, false, 98), 'widget', ["attr" => ["class" => "form-control enhanced-field", "style" => "height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;", "placeholder" => "e.g., 1, 2, 3...", "min" => "0"]]);
        yield "
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Order in which partner appears on the website.
                                        </small>
                                        ";
        // line 102
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 102, $this->source); })()), "displayOrder", [], "any", false, false, false, 102), 'errors');
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class=\"form-group\">
                                <label for=\"";
        // line 109
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 109, $this->source); })()), "description", [], "any", false, false, false, 109), "vars", [], "any", false, false, false, 109), "id", [], "any", false, false, false, 109), "html", null, true);
        yield "\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Description
                                </label>
                                ";
        // line 113
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 113, $this->source); })()), "description", [], "any", false, false, false, 113), 'widget', ["attr" => ["class" => "form-control enhanced-field", "style" => "border: 2px solid #ced4da;", "rows" => "4", "placeholder" => "Brief description of the partnership..."]]);
        yield "
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Provide a brief description of the partnership.
                                </small>
                                ";
        // line 117
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 117, $this->source); })()), "description", [], "any", false, false, false, 117), 'errors');
        yield "
                            </div>
                            <!-- Logo Upload -->
                            <div class=\"form-group\">
                                <label for=\"";
        // line 121
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 121, $this->source); })()), "logoFile", [], "any", false, false, false, 121), "vars", [], "any", false, false, false, 121), "id", [], "any", false, false, false, 121), "html", null, true);
        yield "\" class=\"form-label\">
                                    <i class=\"fas fa-image text-primary mr-1\"></i>
                                    Partner Logo
                                </label>
                                ";
        // line 125
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 125, $this->source); })()), "logoFile", [], "any", false, false, false, 125), 'widget', ["attr" => ["class" => "form-control enhanced-field", "style" => "height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;", "accept" => "image/jpeg,image/png,image/jpg"]]);
        yield "
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <strong>Professional dimensions:</strong> 200x100px (2:1 ratio)<br>
                                    JPEG/PNG, max 2MB. Optimal for partner logo display.
                                </small>
                                ";
        // line 130
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 130, $this->source); })()), "logoFile", [], "any", false, false, false, 130), 'errors');
        yield "
                                <div class=\"image-preview mt-2\" id=\"logo-preview\" style=\"display: none;\">
                                    <div class=\"d-flex justify-content-center\">
                                        <div class=\"professional-image-container\" style=\"width: 300px; height: 150px; border: 2px solid #1e3c72; border-radius: 8px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; overflow: hidden;\">
                                            <img src=\"\" alt=\"Logo Preview\" style=\"max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 6px;\">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" id=\"submitBtn\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                <span id=\"submitText\">Create Partner</span>
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 153
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_partners");
        yield "\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        ";
        // line 160
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 160, $this->source); })()), 'form_end');
        yield "
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 165
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 166
        yield "<script>
\$(document).ready(function() {
    // Enhanced form validation and submit handling
    const form = document.querySelector('form.needs-validation');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');

    if (form) {
        form.addEventListener('submit', function(event) {
            // Custom validation logic
            let isValid = true;
            let errorMessages = [];

            // Clear previous validation states
            clearFieldValidation();

            // Validate partner name (required field)
            const nameField = form.querySelector('[name=\"partner[name]\"]');
            if (nameField && !nameField.value.trim()) {
                isValid = false;
                setFieldError(nameField, 'Partner name is required');
                errorMessages.push('Partner name is required');
            }

            // Validate display order (required field)
            const displayOrderField = form.querySelector('[name=\"partner[displayOrder]\"]');
            if (displayOrderField && (!displayOrderField.value || displayOrderField.value < 0)) {
                isValid = false;
                setFieldError(displayOrderField, 'Display order is required and must be a positive number');
                errorMessages.push('Display order is required and must be a positive number');
            }

            // Validate website URL format if provided
            const websiteField = form.querySelector('[name=\"partner[websiteUrl]\"]');
            if (websiteField && websiteField.value.trim()) {
                const urlPattern = /^https?:\\/\\/.+/;
                if (!urlPattern.test(websiteField.value.trim())) {
                    isValid = false;
                    setFieldError(websiteField, 'Website URL must be a valid URL starting with http:// or https://');
                    errorMessages.push('Website URL must be a valid URL starting with http:// or https://');
                }
            }

            // Validate logo file if selected
            const logoField = form.querySelector('[name=\"partner[logoFile]\"]');
            if (logoField && logoField.files.length > 0) {
                const file = logoField.files[0];
                const maxSize = 2 * 1024 * 1024; // 2MB
                const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'];

                if (file.size > maxSize) {
                    isValid = false;
                    setFieldError(logoField, 'Logo file must be smaller than 2MB');
                    errorMessages.push('Logo file must be smaller than 2MB');
                }

                if (!allowedTypes.includes(file.type)) {
                    isValid = false;
                    setFieldError(logoField, 'Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
                    errorMessages.push('Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
                }
            }

            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();

                // Only show error banner if there are actual errors
                if (errorMessages.length > 0) {
                    showValidationErrors(errorMessages);
                }

                // Reset button state on validation error
                resetSubmitButton();

                // Scroll to first error field
                scrollToFirstError();
            } else {
                // Form is valid, show processing state and allow submission
                if (submitBtn && submitText) {
                    submitBtn.disabled = true;
                    submitText.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating...';
                }
                // Remove any existing error messages
                removeValidationErrors();
            }

            form.classList.add('was-validated');
        });

        // Reset button state when form fields are modified
        const formInputs = form.querySelectorAll('input, select, textarea');
        formInputs.forEach(function(input) {
            input.addEventListener('input', function() {
                // Only reset button if it's not currently processing a valid submission
                if (submitBtn && !submitBtn.disabled) {
                    resetSubmitButton();
                }
                // Remove validation classes when user starts typing
                clearFieldValidation(input);
                // Remove general error messages when user starts correcting
                removeValidationErrors();
            });
        });
    }

    // Function to reset submit button
    function resetSubmitButton() {
        if (submitBtn && submitText) {
            submitBtn.disabled = false;
            submitText.innerHTML = 'Create Partner';
        }
    }

    // Function to show validation errors
    function showValidationErrors(errors) {
        // Remove existing error alerts
        removeValidationErrors();

        const errorHtml = `
            <div class=\"alert alert-danger alert-dismissible fade show validation-error\" role=\"alert\">
                <i class=\"fas fa-exclamation-triangle me-2\"></i>
                <strong>Please fix the following errors:</strong>
                <ul class=\"mb-0 mt-2\">
                    \${errors.map(error => `<li>\${error}</li>`).join('')}
                </ul>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        `;

        // Insert error message at the top of the form
        const cardBody = form.querySelector('.card-body');
        if (cardBody) {
            cardBody.insertAdjacentHTML('afterbegin', errorHtml);
        }
    }

    // Function to remove validation errors
    function removeValidationErrors() {
        const existingErrors = document.querySelectorAll('.validation-error');
        existingErrors.forEach(error => error.remove());
    }

    // Function to set field-specific error
    function setFieldError(field, message) {
        field.classList.add('is-invalid');

        // Find or create invalid-feedback div for this field
        let feedbackDiv = field.parentNode.querySelector('.invalid-feedback');
        if (feedbackDiv) {
            feedbackDiv.textContent = message;
            feedbackDiv.style.display = 'block';
        }
    }

    // Function to clear field validation
    function clearFieldValidation(specificField = null) {
        if (specificField) {
            specificField.classList.remove('is-invalid', 'is-valid');
            const feedbackDiv = specificField.parentNode.querySelector('.invalid-feedback');
            if (feedbackDiv) {
                feedbackDiv.style.display = 'none';
            }
        } else {
            // Clear all field validations
            const allFields = form.querySelectorAll('input, select, textarea');
            allFields.forEach(field => {
                field.classList.remove('is-invalid', 'is-valid');
                const feedbackDiv = field.parentNode.querySelector('.invalid-feedback');
                if (feedbackDiv) {
                    feedbackDiv.style.display = 'none';
                }
            });
        }
    }

    // Function to scroll to first error field
    function scrollToFirstError() {
        const firstErrorField = form.querySelector('.is-invalid');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstErrorField.focus();
        }
    }

    // Reset button state if there are server-side validation errors
    ";
        // line 352
        if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 352, $this->source); })()), "vars", [], "any", false, false, false, 352), "valid", [], "any", false, false, false, 352)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 353
            yield "        resetSubmitButton();
    ";
        }
        // line 355
        yield "
    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Logo Preview Functionality
    \$('#";
        // line 364
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 364, $this->source); })()), "logoFile", [], "any", false, false, false, 364), "vars", [], "any", false, false, false, 364), "id", [], "any", false, false, false, 364), "html", null, true);
        yield "').on('change', function() {
        previewImage(this, '#logo-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/partners/create.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  616 => 364,  605 => 355,  601 => 353,  599 => 352,  411 => 166,  398 => 165,  383 => 160,  373 => 153,  347 => 130,  339 => 125,  332 => 121,  325 => 117,  318 => 113,  311 => 109,  301 => 102,  294 => 98,  280 => 87,  273 => 83,  266 => 79,  255 => 71,  248 => 67,  241 => 63,  231 => 56,  215 => 43,  199 => 29,  189 => 25,  186 => 24,  182 => 23,  179 => 22,  169 => 18,  166 => 17,  162 => 16,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Partner - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Partner{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_partners') }}\">Partners</a></li>
<li class=\"breadcrumb-item active\">Create Partner</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-handshake mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Partner
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Partners Button -->
                        <a href=\"{{ path('admin_partners') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Partners
                        </a>
                    </div>
                </div>
            </div>
        </div>

        {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true, 'enctype': 'multipart/form-data'}}) }}
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Partner Name -->
                            <div class=\"form-group\">
                                <label for=\"{{ form.name.vars.id }}\" class=\"form-label\">
                                    <i class=\"fas fa-building text-primary mr-1\"></i>
                                    Partner Name <span class=\"text-danger\">*</span>
                                </label>
                                {{ form_widget(form.name, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'placeholder': 'Enter partner organization name', 'required': 'required'}}) }}
                                <div class=\"invalid-feedback\">
                                    Please provide a partner name.
                                </div>
                                {{ form_errors(form.name) }}
                            </div>

                            <!-- Website URL and Display Order Row -->
                            <div class=\"row\">
                                <!-- Website URL -->
                                <div class=\"col-md-8\">
                                    <div class=\"form-group\">
                                        <label for=\"{{ form.websiteUrl.vars.id }}\" class=\"form-label\">
                                            <i class=\"fas fa-globe text-primary mr-1\"></i>
                                            Website URL
                                        </label>
                                        {{ form_widget(form.websiteUrl, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'placeholder': 'https://www.partner-website.com'}}) }}
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Partner's official website URL.
                                        </small>
                                        {{ form_errors(form.websiteUrl) }}
                                    </div>
                                </div>

                                <!-- Display Order -->
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label for=\"displayOrder\" class=\"form-label\">
                                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                                            Display Order
                                        </label>
                                        {{ form_widget(form.displayOrder, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'placeholder': 'e.g., 1, 2, 3...', 'min': '0'}}) }}
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Order in which partner appears on the website.
                                        </small>
                                        {{ form_errors(form.displayOrder) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class=\"form-group\">
                                <label for=\"{{ form.description.vars.id }}\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Description
                                </label>
                                {{ form_widget(form.description, {'attr': {'class': 'form-control enhanced-field', 'style': 'border: 2px solid #ced4da;', 'rows': '4', 'placeholder': 'Brief description of the partnership...'}}) }}
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Provide a brief description of the partnership.
                                </small>
                                {{ form_errors(form.description) }}
                            </div>
                            <!-- Logo Upload -->
                            <div class=\"form-group\">
                                <label for=\"{{ form.logoFile.vars.id }}\" class=\"form-label\">
                                    <i class=\"fas fa-image text-primary mr-1\"></i>
                                    Partner Logo
                                </label>
                                {{ form_widget(form.logoFile, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'accept': 'image/jpeg,image/png,image/jpg'}}) }}
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <strong>Professional dimensions:</strong> 200x100px (2:1 ratio)<br>
                                    JPEG/PNG, max 2MB. Optimal for partner logo display.
                                </small>
                                {{ form_errors(form.logoFile) }}
                                <div class=\"image-preview mt-2\" id=\"logo-preview\" style=\"display: none;\">
                                    <div class=\"d-flex justify-content-center\">
                                        <div class=\"professional-image-container\" style=\"width: 300px; height: 150px; border: 2px solid #1e3c72; border-radius: 8px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; overflow: hidden;\">
                                            <img src=\"\" alt=\"Logo Preview\" style=\"max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 6px;\">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" id=\"submitBtn\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                <span id=\"submitText\">Create Partner</span>
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_partners') }}\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        {{ form_end(form) }}
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Enhanced form validation and submit handling
    const form = document.querySelector('form.needs-validation');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');

    if (form) {
        form.addEventListener('submit', function(event) {
            // Custom validation logic
            let isValid = true;
            let errorMessages = [];

            // Clear previous validation states
            clearFieldValidation();

            // Validate partner name (required field)
            const nameField = form.querySelector('[name=\"partner[name]\"]');
            if (nameField && !nameField.value.trim()) {
                isValid = false;
                setFieldError(nameField, 'Partner name is required');
                errorMessages.push('Partner name is required');
            }

            // Validate display order (required field)
            const displayOrderField = form.querySelector('[name=\"partner[displayOrder]\"]');
            if (displayOrderField && (!displayOrderField.value || displayOrderField.value < 0)) {
                isValid = false;
                setFieldError(displayOrderField, 'Display order is required and must be a positive number');
                errorMessages.push('Display order is required and must be a positive number');
            }

            // Validate website URL format if provided
            const websiteField = form.querySelector('[name=\"partner[websiteUrl]\"]');
            if (websiteField && websiteField.value.trim()) {
                const urlPattern = /^https?:\\/\\/.+/;
                if (!urlPattern.test(websiteField.value.trim())) {
                    isValid = false;
                    setFieldError(websiteField, 'Website URL must be a valid URL starting with http:// or https://');
                    errorMessages.push('Website URL must be a valid URL starting with http:// or https://');
                }
            }

            // Validate logo file if selected
            const logoField = form.querySelector('[name=\"partner[logoFile]\"]');
            if (logoField && logoField.files.length > 0) {
                const file = logoField.files[0];
                const maxSize = 2 * 1024 * 1024; // 2MB
                const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'];

                if (file.size > maxSize) {
                    isValid = false;
                    setFieldError(logoField, 'Logo file must be smaller than 2MB');
                    errorMessages.push('Logo file must be smaller than 2MB');
                }

                if (!allowedTypes.includes(file.type)) {
                    isValid = false;
                    setFieldError(logoField, 'Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
                    errorMessages.push('Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
                }
            }

            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();

                // Only show error banner if there are actual errors
                if (errorMessages.length > 0) {
                    showValidationErrors(errorMessages);
                }

                // Reset button state on validation error
                resetSubmitButton();

                // Scroll to first error field
                scrollToFirstError();
            } else {
                // Form is valid, show processing state and allow submission
                if (submitBtn && submitText) {
                    submitBtn.disabled = true;
                    submitText.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating...';
                }
                // Remove any existing error messages
                removeValidationErrors();
            }

            form.classList.add('was-validated');
        });

        // Reset button state when form fields are modified
        const formInputs = form.querySelectorAll('input, select, textarea');
        formInputs.forEach(function(input) {
            input.addEventListener('input', function() {
                // Only reset button if it's not currently processing a valid submission
                if (submitBtn && !submitBtn.disabled) {
                    resetSubmitButton();
                }
                // Remove validation classes when user starts typing
                clearFieldValidation(input);
                // Remove general error messages when user starts correcting
                removeValidationErrors();
            });
        });
    }

    // Function to reset submit button
    function resetSubmitButton() {
        if (submitBtn && submitText) {
            submitBtn.disabled = false;
            submitText.innerHTML = 'Create Partner';
        }
    }

    // Function to show validation errors
    function showValidationErrors(errors) {
        // Remove existing error alerts
        removeValidationErrors();

        const errorHtml = `
            <div class=\"alert alert-danger alert-dismissible fade show validation-error\" role=\"alert\">
                <i class=\"fas fa-exclamation-triangle me-2\"></i>
                <strong>Please fix the following errors:</strong>
                <ul class=\"mb-0 mt-2\">
                    \${errors.map(error => `<li>\${error}</li>`).join('')}
                </ul>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        `;

        // Insert error message at the top of the form
        const cardBody = form.querySelector('.card-body');
        if (cardBody) {
            cardBody.insertAdjacentHTML('afterbegin', errorHtml);
        }
    }

    // Function to remove validation errors
    function removeValidationErrors() {
        const existingErrors = document.querySelectorAll('.validation-error');
        existingErrors.forEach(error => error.remove());
    }

    // Function to set field-specific error
    function setFieldError(field, message) {
        field.classList.add('is-invalid');

        // Find or create invalid-feedback div for this field
        let feedbackDiv = field.parentNode.querySelector('.invalid-feedback');
        if (feedbackDiv) {
            feedbackDiv.textContent = message;
            feedbackDiv.style.display = 'block';
        }
    }

    // Function to clear field validation
    function clearFieldValidation(specificField = null) {
        if (specificField) {
            specificField.classList.remove('is-invalid', 'is-valid');
            const feedbackDiv = specificField.parentNode.querySelector('.invalid-feedback');
            if (feedbackDiv) {
                feedbackDiv.style.display = 'none';
            }
        } else {
            // Clear all field validations
            const allFields = form.querySelectorAll('input, select, textarea');
            allFields.forEach(field => {
                field.classList.remove('is-invalid', 'is-valid');
                const feedbackDiv = field.parentNode.querySelector('.invalid-feedback');
                if (feedbackDiv) {
                    feedbackDiv.style.display = 'none';
                }
            });
        }
    }

    // Function to scroll to first error field
    function scrollToFirstError() {
        const firstErrorField = form.querySelector('.is-invalid');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstErrorField.focus();
        }
    }

    // Reset button state if there are server-side validation errors
    {% if not form.vars.valid %}
        resetSubmitButton();
    {% endif %}

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Logo Preview Functionality
    \$('#{{ form.logoFile.vars.id }}').on('change', function() {
        previewImage(this, '#logo-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
</style>
{% endblock %}
", "admin/partners/create.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\partners\\create.html.twig");
    }
}
