<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/remote_courses/create.html.twig */
class __TwigTemplate_60c44c044c43ce6576d969d2a7b87877 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumb' => [$this, 'block_breadcrumb'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/create.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/create.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Remote Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create Remote Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumb(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumb"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumb"));

        // line 8
        yield "    <nav aria-label=\"breadcrumb\">
        <ol class=\"breadcrumb\">
            <li class=\"breadcrumb-item\"><a href=\"";
        // line 10
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Dashboard</a></li>
            <li class=\"breadcrumb-item\"><a href=\"";
        // line 11
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_index");
        yield "\">Remote Courses</a></li>
            <li class=\"breadcrumb-item active\" aria-current=\"page\">Create</li>
        </ol>
    </nav>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 17
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 18
        yield "    <div class=\"container-fluid\">
        <div class=\"row\">
            <div class=\"col-lg-8\">
                <div class=\"card shadow mb-4\">
                    <div class=\"card-header py-3\">
                        <h6 class=\"m-0 font-weight-bold text-primary\">
                            <i class=\"fas fa-plus me-2\"></i>Create New Remote Course
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        ";
        // line 28
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 28, $this->source); })()), 'form_start', ["attr" => ["enctype" => "multipart/form-data"]]);
        yield "
                        
                        <div class=\"row\">
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-code text-primary me-1\"></i>";
        // line 34
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 34, $this->source); })()), "code", [], "any", false, false, false, 34), 'label');
        yield "
                                    </label>
                                    ";
        // line 36
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 36, $this->source); })()), "code", [], "any", false, false, false, 36), 'widget');
        yield "
                                    ";
        // line 37
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 37, $this->source); })()), "code", [], "any", false, false, false, 37), 'help');
        yield "
                                    ";
        // line 38
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 38, $this->source); })()), "code", [], "any", false, false, false, 38), 'errors');
        yield "
                                </div>
                            </div>
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tag text-primary me-1\"></i>";
        // line 44
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 44, $this->source); })()), "category", [], "any", false, false, false, 44), 'label');
        yield "
                                    </label>
                                    ";
        // line 46
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 46, $this->source); })()), "category", [], "any", false, false, false, 46), 'widget');
        yield "
                                    ";
        // line 47
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 47, $this->source); })()), "category", [], "any", false, false, false, 47), 'errors');
        yield "
                                </div>
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-heading text-primary me-1\"></i>";
        // line 55
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 55, $this->source); })()), "title", [], "any", false, false, false, 55), 'label');
        yield "
                                </label>
                                ";
        // line 57
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 57, $this->source); })()), "title", [], "any", false, false, false, 57), 'widget');
        yield "
                                ";
        // line 58
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 58, $this->source); })()), "title", [], "any", false, false, false, 58), 'errors');
        yield "
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary me-1\"></i>";
        // line 65
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 65, $this->source); })()), "description", [], "any", false, false, false, 65), 'label');
        yield "
                                </label>
                                ";
        // line 67
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 67, $this->source); })()), "description", [], "any", false, false, false, 67), 'widget');
        yield "
                                ";
        // line 68
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 68, $this->source); })()), "description", [], "any", false, false, false, 68), 'errors');
        yield "
                            </div>
                        </div>

                        <div class=\"row\">
                            <div class=\"col-md-4 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary me-1\"></i>";
        // line 76
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 76, $this->source); })()), "level", [], "any", false, false, false, 76), 'label');
        yield "
                                    </label>
                                    ";
        // line 78
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 78, $this->source); })()), "level", [], "any", false, false, false, 78), 'widget');
        yield "
                                    ";
        // line 79
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 79, $this->source); })()), "level", [], "any", false, false, false, 79), 'errors');
        yield "
                                </div>
                            </div>
                            <div class=\"col-md-4 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-user-tie text-primary me-1\"></i>";
        // line 85
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 85, $this->source); })()), "instructor", [], "any", false, false, false, 85), 'label');
        yield "
                                    </label>
                                    ";
        // line 87
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 87, $this->source); })()), "instructor", [], "any", false, false, false, 87), 'widget');
        yield "
                                    ";
        // line 88
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 88, $this->source); })()), "instructor", [], "any", false, false, false, 88), 'errors');
        yield "
                                </div>
                            </div>
                            <div class=\"col-md-4 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary me-1\"></i>";
        // line 94
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 94, $this->source); })()), "price", [], "any", false, false, false, 94), 'label');
        yield "
                                    </label>
                                    ";
        // line 96
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 96, $this->source); })()), "price", [], "any", false, false, false, 96), 'widget');
        yield "
                                    ";
        // line 97
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 97, $this->source); })()), "price", [], "any", false, false, false, 97), 'errors');
        yield "
                                </div>
                            </div>
                        </div>

                        <div class=\"row\">
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-image text-primary me-1\"></i>";
        // line 106
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 106, $this->source); })()), "thumbnail_image", [], "any", false, false, false, 106), 'label');
        yield "
                                    </label>
                                    ";
        // line 108
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 108, $this->source); })()), "thumbnail_image", [], "any", false, false, false, 108), 'widget');
        yield "
                                    ";
        // line 109
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 109, $this->source); })()), "thumbnail_image", [], "any", false, false, false, 109), 'help');
        yield "
                                    ";
        // line 110
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 110, $this->source); })()), "thumbnail_image", [], "any", false, false, false, 110), 'errors');
        yield "
                                </div>
                            </div>
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-panorama text-primary me-1\"></i>";
        // line 116
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 116, $this->source); })()), "banner_image", [], "any", false, false, false, 116), 'label');
        yield "
                                    </label>
                                    ";
        // line 118
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 118, $this->source); })()), "banner_image", [], "any", false, false, false, 118), 'widget');
        yield "
                                    ";
        // line 119
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 119, $this->source); })()), "banner_image", [], "any", false, false, false, 119), 'help');
        yield "
                                    ";
        // line 120
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 120, $this->source); })()), "banner_image", [], "any", false, false, false, 120), 'errors');
        yield "
                                </div>
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-bullseye text-primary me-1\"></i>";
        // line 128
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 128, $this->source); })()), "learning_outcomes", [], "any", false, false, false, 128), 'label');
        yield "
                                </label>
                                <div id=\"learning-outcomes-collection\" data-prototype=\"";
        // line 130
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 130, $this->source); })()), "learning_outcomes", [], "any", false, false, false, 130), "vars", [], "any", false, false, false, 130), "prototype", [], "any", false, false, false, 130), 'widget'), "html_attr");
        yield "\">
                                    ";
        // line 131
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 131, $this->source); })()), "learning_outcomes", [], "any", false, false, false, 131));
        foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
            // line 132
            yield "                                        <div class=\"learning-outcome-item mb-2\">
                                            <div class=\"input-group\">
                                                ";
            // line 134
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock($context["outcome"], 'widget');
            yield "
                                                <button type=\"button\" class=\"btn btn-outline-danger remove-item\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 141
        yield "                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-learning-outcome\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Learning Outcome
                                </button>
                                ";
        // line 145
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 145, $this->source); })()), "learning_outcomes", [], "any", false, false, false, 145), 'help');
        yield "
                                ";
        // line 146
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 146, $this->source); })()), "learning_outcomes", [], "any", false, false, false, 146), 'errors');
        yield "
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star text-primary me-1\"></i>";
        // line 153
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 153, $this->source); })()), "features", [], "any", false, false, false, 153), 'label');
        yield "
                                </label>
                                <div id=\"features-collection\" data-prototype=\"";
        // line 155
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 155, $this->source); })()), "features", [], "any", false, false, false, 155), "vars", [], "any", false, false, false, 155), "prototype", [], "any", false, false, false, 155), 'widget'), "html_attr");
        yield "\">
                                    ";
        // line 156
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 156, $this->source); })()), "features", [], "any", false, false, false, 156));
        foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
            // line 157
            yield "                                        <div class=\"feature-item mb-2\">
                                            <div class=\"input-group\">
                                                ";
            // line 159
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock($context["feature"], 'widget');
            yield "
                                                <button type=\"button\" class=\"btn btn-outline-danger remove-item\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 166
        yield "                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-feature\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Feature
                                </button>
                                ";
        // line 170
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 170, $this->source); })()), "features", [], "any", false, false, false, 170), 'help');
        yield "
                                ";
        // line 171
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 171, $this->source); })()), "features", [], "any", false, false, false, 171), 'errors');
        yield "
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-tags text-primary me-1\"></i>";
        // line 178
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 178, $this->source); })()), "tags", [], "any", false, false, false, 178), 'label');
        yield "
                                </label>
                                <div id=\"tags-collection\" data-prototype=\"";
        // line 180
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 180, $this->source); })()), "tags", [], "any", false, false, false, 180), "vars", [], "any", false, false, false, 180), "prototype", [], "any", false, false, false, 180), 'widget'), "html_attr");
        yield "\">
                                    ";
        // line 181
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 181, $this->source); })()), "tags", [], "any", false, false, false, 181));
        foreach ($context['_seq'] as $context["_key"] => $context["tag"]) {
            // line 182
            yield "                                        <div class=\"tag-item mb-2\">
                                            <div class=\"input-group\">
                                                ";
            // line 184
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock($context["tag"], 'widget');
            yield "
                                                <button type=\"button\" class=\"btn btn-outline-danger remove-item\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['tag'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 191
        yield "                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-tag\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Tag
                                </button>
                                ";
        // line 195
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 195, $this->source); })()), "tags", [], "any", false, false, false, 195), 'help');
        yield "
                                ";
        // line 196
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 196, $this->source); })()), "tags", [], "any", false, false, false, 196), 'errors');
        yield "
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary me-1\"></i>";
        // line 203
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 203, $this->source); })()), "is_active", [], "any", false, false, false, 203), 'label');
        yield "
                                </label>
                                ";
        // line 205
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 205, $this->source); })()), "is_active", [], "any", false, false, false, 205), 'widget');
        yield "
                                ";
        // line 206
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 206, $this->source); })()), "is_active", [], "any", false, false, false, 206), 'help');
        yield "
                                ";
        // line 207
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 207, $this->source); })()), "is_active", [], "any", false, false, false, 207), 'errors');
        yield "
                            </div>
                        </div>

                        <div class=\"form-footer mt-4 pt-3 border-top\">
                            <div class=\"d-flex justify-content-between\">
                                <a href=\"";
        // line 213
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_index");
        yield "\" class=\"btn btn-secondary\">
                                    <i class=\"fas fa-arrow-left me-1\"></i>Cancel
                                </a>
                                <button type=\"submit\" class=\"btn btn-success\">
                                    <i class=\"fas fa-save me-1\"></i>Create Remote Course
                                </button>
                            </div>
                        </div>

                        ";
        // line 222
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 222, $this->source); })()), 'form_end');
        yield "
                    </div>
                </div>
            </div>

            <div class=\"col-lg-4\">
                <div class=\"card shadow mb-4\">
                    <div class=\"card-header py-3\">
                        <h6 class=\"m-0 font-weight-bold text-info\">
                            <i class=\"fas fa-info-circle me-2\"></i>Course Creation Guide
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        <div class=\"mb-3\">
                            <h6 class=\"text-primary\">
                                <i class=\"fas fa-lightbulb me-1\"></i>Tips for Success
                            </h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Use a unique, descriptive course code
                                </li>
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Write clear, compelling course titles
                                </li>
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Add detailed learning outcomes
                                </li>
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Upload high-quality images
                                </li>
                            </ul>
                        </div>

                        <div class=\"mb-3\">
                            <h6 class=\"text-primary\">
                                <i class=\"fas fa-list-ol me-1\"></i>Next Steps
                            </h6>
                            <ol class=\"list-unstyled\">
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-primary me-2\">1</span>
                                    Create the course
                                </li>
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-secondary me-2\">2</span>
                                    Add chapters to organize content
                                </li>
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-secondary me-2\">3</span>
                                    Assign videos to chapters
                                </li>
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-secondary me-2\">4</span>
                                    Preview and publish
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 289
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 290
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        // Collection management for dynamic form fields
        document.addEventListener('DOMContentLoaded', function() {
            setupCollectionManagement('learning-outcomes-collection', 'add-learning-outcome', 'learning-outcome-item');
            setupCollectionManagement('features-collection', 'add-feature', 'feature-item');
            setupCollectionManagement('tags-collection', 'add-tag', 'tag-item');
        });

        function setupCollectionManagement(collectionId, addButtonClass, itemClass) {
            const collection = document.getElementById(collectionId);
            const addButton = document.querySelector('.' + addButtonClass);
            
            if (!collection || !addButton) return;

            let index = collection.children.length;

            addButton.addEventListener('click', function() {
                const prototype = collection.dataset.prototype;
                const newForm = prototype.replace(/__name__/g, index);
                
                const wrapper = document.createElement('div');
                wrapper.className = itemClass + ' mb-2';
                wrapper.innerHTML = '<div class=\"input-group\">' + newForm + 
                    '<button type=\"button\" class=\"btn btn-outline-danger remove-item\"><i class=\"fas fa-times\"></i></button></div>';
                
                collection.appendChild(wrapper);
                index++;
            });

            collection.addEventListener('click', function(e) {
                if (e.target.closest('.remove-item')) {
                    e.target.closest('.' + itemClass).remove();
                }
            });
        }
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/remote_courses/create.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  642 => 290,  629 => 289,  552 => 222,  540 => 213,  531 => 207,  527 => 206,  523 => 205,  518 => 203,  508 => 196,  504 => 195,  498 => 191,  485 => 184,  481 => 182,  477 => 181,  473 => 180,  468 => 178,  458 => 171,  454 => 170,  448 => 166,  435 => 159,  431 => 157,  427 => 156,  423 => 155,  418 => 153,  408 => 146,  404 => 145,  398 => 141,  385 => 134,  381 => 132,  377 => 131,  373 => 130,  368 => 128,  357 => 120,  353 => 119,  349 => 118,  344 => 116,  335 => 110,  331 => 109,  327 => 108,  322 => 106,  310 => 97,  306 => 96,  301 => 94,  292 => 88,  288 => 87,  283 => 85,  274 => 79,  270 => 78,  265 => 76,  254 => 68,  250 => 67,  245 => 65,  235 => 58,  231 => 57,  226 => 55,  215 => 47,  211 => 46,  206 => 44,  197 => 38,  193 => 37,  189 => 36,  184 => 34,  175 => 28,  163 => 18,  150 => 17,  134 => 11,  130 => 10,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Remote Course{% endblock %}

{% block page_title %}Create Remote Course{% endblock %}

{% block breadcrumb %}
    <nav aria-label=\"breadcrumb\">
        <ol class=\"breadcrumb\">
            <li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Dashboard</a></li>
            <li class=\"breadcrumb-item\"><a href=\"{{ path('admin_remote_course_index') }}\">Remote Courses</a></li>
            <li class=\"breadcrumb-item active\" aria-current=\"page\">Create</li>
        </ol>
    </nav>
{% endblock %}

{% block content %}
    <div class=\"container-fluid\">
        <div class=\"row\">
            <div class=\"col-lg-8\">
                <div class=\"card shadow mb-4\">
                    <div class=\"card-header py-3\">
                        <h6 class=\"m-0 font-weight-bold text-primary\">
                            <i class=\"fas fa-plus me-2\"></i>Create New Remote Course
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        {{ form_start(form, {'attr': {'enctype': 'multipart/form-data'}}) }}
                        
                        <div class=\"row\">
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-code text-primary me-1\"></i>{{ form_label(form.code) }}
                                    </label>
                                    {{ form_widget(form.code) }}
                                    {{ form_help(form.code) }}
                                    {{ form_errors(form.code) }}
                                </div>
                            </div>
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tag text-primary me-1\"></i>{{ form_label(form.category) }}
                                    </label>
                                    {{ form_widget(form.category) }}
                                    {{ form_errors(form.category) }}
                                </div>
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-heading text-primary me-1\"></i>{{ form_label(form.title) }}
                                </label>
                                {{ form_widget(form.title) }}
                                {{ form_errors(form.title) }}
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary me-1\"></i>{{ form_label(form.description) }}
                                </label>
                                {{ form_widget(form.description) }}
                                {{ form_errors(form.description) }}
                            </div>
                        </div>

                        <div class=\"row\">
                            <div class=\"col-md-4 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary me-1\"></i>{{ form_label(form.level) }}
                                    </label>
                                    {{ form_widget(form.level) }}
                                    {{ form_errors(form.level) }}
                                </div>
                            </div>
                            <div class=\"col-md-4 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-user-tie text-primary me-1\"></i>{{ form_label(form.instructor) }}
                                    </label>
                                    {{ form_widget(form.instructor) }}
                                    {{ form_errors(form.instructor) }}
                                </div>
                            </div>
                            <div class=\"col-md-4 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary me-1\"></i>{{ form_label(form.price) }}
                                    </label>
                                    {{ form_widget(form.price) }}
                                    {{ form_errors(form.price) }}
                                </div>
                            </div>
                        </div>

                        <div class=\"row\">
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-image text-primary me-1\"></i>{{ form_label(form.thumbnail_image) }}
                                    </label>
                                    {{ form_widget(form.thumbnail_image) }}
                                    {{ form_help(form.thumbnail_image) }}
                                    {{ form_errors(form.thumbnail_image) }}
                                </div>
                            </div>
                            <div class=\"col-md-6 mb-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-panorama text-primary me-1\"></i>{{ form_label(form.banner_image) }}
                                    </label>
                                    {{ form_widget(form.banner_image) }}
                                    {{ form_help(form.banner_image) }}
                                    {{ form_errors(form.banner_image) }}
                                </div>
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-bullseye text-primary me-1\"></i>{{ form_label(form.learning_outcomes) }}
                                </label>
                                <div id=\"learning-outcomes-collection\" data-prototype=\"{{ form_widget(form.learning_outcomes.vars.prototype)|e('html_attr') }}\">
                                    {% for outcome in form.learning_outcomes %}
                                        <div class=\"learning-outcome-item mb-2\">
                                            <div class=\"input-group\">
                                                {{ form_widget(outcome) }}
                                                <button type=\"button\" class=\"btn btn-outline-danger remove-item\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-learning-outcome\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Learning Outcome
                                </button>
                                {{ form_help(form.learning_outcomes) }}
                                {{ form_errors(form.learning_outcomes) }}
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star text-primary me-1\"></i>{{ form_label(form.features) }}
                                </label>
                                <div id=\"features-collection\" data-prototype=\"{{ form_widget(form.features.vars.prototype)|e('html_attr') }}\">
                                    {% for feature in form.features %}
                                        <div class=\"feature-item mb-2\">
                                            <div class=\"input-group\">
                                                {{ form_widget(feature) }}
                                                <button type=\"button\" class=\"btn btn-outline-danger remove-item\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-feature\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Feature
                                </button>
                                {{ form_help(form.features) }}
                                {{ form_errors(form.features) }}
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-tags text-primary me-1\"></i>{{ form_label(form.tags) }}
                                </label>
                                <div id=\"tags-collection\" data-prototype=\"{{ form_widget(form.tags.vars.prototype)|e('html_attr') }}\">
                                    {% for tag in form.tags %}
                                        <div class=\"tag-item mb-2\">
                                            <div class=\"input-group\">
                                                {{ form_widget(tag) }}
                                                <button type=\"button\" class=\"btn btn-outline-danger remove-item\">
                                                    <i class=\"fas fa-times\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <button type=\"button\" class=\"btn btn-outline-primary btn-sm add-tag\">
                                    <i class=\"fas fa-plus me-1\"></i>Add Tag
                                </button>
                                {{ form_help(form.tags) }}
                                {{ form_errors(form.tags) }}
                            </div>
                        </div>

                        <div class=\"mb-3\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary me-1\"></i>{{ form_label(form.is_active) }}
                                </label>
                                {{ form_widget(form.is_active) }}
                                {{ form_help(form.is_active) }}
                                {{ form_errors(form.is_active) }}
                            </div>
                        </div>

                        <div class=\"form-footer mt-4 pt-3 border-top\">
                            <div class=\"d-flex justify-content-between\">
                                <a href=\"{{ path('admin_remote_course_index') }}\" class=\"btn btn-secondary\">
                                    <i class=\"fas fa-arrow-left me-1\"></i>Cancel
                                </a>
                                <button type=\"submit\" class=\"btn btn-success\">
                                    <i class=\"fas fa-save me-1\"></i>Create Remote Course
                                </button>
                            </div>
                        </div>

                        {{ form_end(form) }}
                    </div>
                </div>
            </div>

            <div class=\"col-lg-4\">
                <div class=\"card shadow mb-4\">
                    <div class=\"card-header py-3\">
                        <h6 class=\"m-0 font-weight-bold text-info\">
                            <i class=\"fas fa-info-circle me-2\"></i>Course Creation Guide
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        <div class=\"mb-3\">
                            <h6 class=\"text-primary\">
                                <i class=\"fas fa-lightbulb me-1\"></i>Tips for Success
                            </h6>
                            <ul class=\"list-unstyled\">
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Use a unique, descriptive course code
                                </li>
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Write clear, compelling course titles
                                </li>
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Add detailed learning outcomes
                                </li>
                                <li class=\"mb-2\">
                                    <i class=\"fas fa-check text-success me-2\"></i>
                                    Upload high-quality images
                                </li>
                            </ul>
                        </div>

                        <div class=\"mb-3\">
                            <h6 class=\"text-primary\">
                                <i class=\"fas fa-list-ol me-1\"></i>Next Steps
                            </h6>
                            <ol class=\"list-unstyled\">
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-primary me-2\">1</span>
                                    Create the course
                                </li>
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-secondary me-2\">2</span>
                                    Add chapters to organize content
                                </li>
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-secondary me-2\">3</span>
                                    Assign videos to chapters
                                </li>
                                <li class=\"mb-2\">
                                    <span class=\"badge bg-secondary me-2\">4</span>
                                    Preview and publish
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Collection management for dynamic form fields
        document.addEventListener('DOMContentLoaded', function() {
            setupCollectionManagement('learning-outcomes-collection', 'add-learning-outcome', 'learning-outcome-item');
            setupCollectionManagement('features-collection', 'add-feature', 'feature-item');
            setupCollectionManagement('tags-collection', 'add-tag', 'tag-item');
        });

        function setupCollectionManagement(collectionId, addButtonClass, itemClass) {
            const collection = document.getElementById(collectionId);
            const addButton = document.querySelector('.' + addButtonClass);
            
            if (!collection || !addButton) return;

            let index = collection.children.length;

            addButton.addEventListener('click', function() {
                const prototype = collection.dataset.prototype;
                const newForm = prototype.replace(/__name__/g, index);
                
                const wrapper = document.createElement('div');
                wrapper.className = itemClass + ' mb-2';
                wrapper.innerHTML = '<div class=\"input-group\">' + newForm + 
                    '<button type=\"button\" class=\"btn btn-outline-danger remove-item\"><i class=\"fas fa-times\"></i></button></div>';
                
                collection.appendChild(wrapper);
                index++;
            });

            collection.addEventListener('click', function(e) {
                if (e.target.closest('.remove-item')) {
                    e.target.closest('.' + itemClass).remove();
                }
            });
        }
    </script>
{% endblock %}
", "admin/remote_courses/create.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\remote_courses\\create.html.twig");
    }
}
