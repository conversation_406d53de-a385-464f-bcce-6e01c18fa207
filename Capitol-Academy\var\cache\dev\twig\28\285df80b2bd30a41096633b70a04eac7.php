<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* @Twig/Exception/error.html.twig */
class __TwigTemplate_7048a7533bca6bc3cc48e6e28e0d5fc2 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "@Twig/Exception/error.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "@Twig/Exception/error.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Error ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("status_code", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["status_code"]) || array_key_exists("status_code", $context) ? $context["status_code"] : (function () { throw new RuntimeError('Variable "status_code" does not exist.', 3, $this->source); })()), "")) : ("")), "html", null, true);
        yield " - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "An error occurred while processing your request. Return to Capitol Academy's homepage or contact us for assistance.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 8
        yield "<div class=\"error-page-container\">
    <!-- Hero Section -->
    <section class=\"error-hero-section py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%); min-height: 70vh;\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-8 mx-auto text-center\">
                    <div class=\"error-content text-white\">
                        <!-- Error Code -->
                        <h1 class=\"error-number display-1 fw-bold mb-4\" style=\"font-size: 6rem; color: #a90418; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\">
                            ";
        // line 17
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("status_code", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["status_code"]) || array_key_exists("status_code", $context) ? $context["status_code"] : (function () { throw new RuntimeError('Variable "status_code" does not exist.', 17, $this->source); })()), "Error")) : ("Error")), "html", null, true);
        yield "
                        </h1>
                        
                        <!-- Error Title -->
                        <h2 class=\"error-title h1 fw-bold mb-4\">
                            ";
        // line 22
        if (((isset($context["status_code"]) || array_key_exists("status_code", $context) ? $context["status_code"] : (function () { throw new RuntimeError('Variable "status_code" does not exist.', 22, $this->source); })()) == 403)) {
            // line 23
            yield "                                Access Denied
                            ";
        } elseif ((        // line 24
(isset($context["status_code"]) || array_key_exists("status_code", $context) ? $context["status_code"] : (function () { throw new RuntimeError('Variable "status_code" does not exist.', 24, $this->source); })()) == 500)) {
            // line 25
            yield "                                Server Error
                            ";
        } else {
            // line 27
            yield "                                Something Went Wrong
                            ";
        }
        // line 29
        yield "                        </h2>
                        
                        <!-- Error Description -->
                        <p class=\"error-description lead mb-5\" style=\"font-size: 1.3rem; line-height: 1.8; opacity: 0.9;\">
                            ";
        // line 33
        if (((isset($context["status_code"]) || array_key_exists("status_code", $context) ? $context["status_code"] : (function () { throw new RuntimeError('Variable "status_code" does not exist.', 33, $this->source); })()) == 403)) {
            // line 34
            yield "                                You don't have permission to access this resource.
                            ";
        } elseif ((        // line 35
(isset($context["status_code"]) || array_key_exists("status_code", $context) ? $context["status_code"] : (function () { throw new RuntimeError('Variable "status_code" does not exist.', 35, $this->source); })()) == 500)) {
            // line 36
            yield "                                We're experiencing technical difficulties. Please try again later.
                            ";
        } else {
            // line 38
            yield "                                An unexpected error occurred while processing your request.
                            ";
        }
        // line 40
        yield "                            Our team has been notified and is working to resolve the issue.
                        </p>
                        
                        <!-- Action Buttons -->
                        <div class=\"error-actions d-flex flex-wrap justify-content-center gap-3\">
                            <a href=\"";
        // line 45
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\" class=\"btn btn-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-home me-2\"></i>
                                Return Home
                            </a>
                            <a href=\"javascript:history.back()\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-arrow-left me-2\"></i>
                                Go Back
                            </a>
                            <a href=\"";
        // line 53
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-envelope me-2\"></i>
                                Contact Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Helpful Links Section -->
    <section class=\"helpful-links-section py-5\" style=\"background: white;\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-8 mx-auto text-center mb-5\">
                    <h3 class=\"h2 fw-bold mb-4\" style=\"color: #011a2d;\">
                        Continue Your Learning Journey
                    </h3>
                    <p class=\"lead\" style=\"color: #6c757d;\">
                        Don't let this stop you from achieving your trading goals
                    </p>
                </div>
            </div>
            
            <div class=\"row g-4\">
                <!-- Trading Courses -->
                <div class=\"col-lg-6 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-graduation-cap\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Trading Courses</h5>
                            <p class=\"text-muted mb-4\">
                                Master financial markets with our comprehensive course catalog
                            </p>
                            <a href=\"";
        // line 90
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-outline-primary\">
                                Explore Courses
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Market Analysis -->
                <div class=\"col-lg-6 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-chart-line\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Market Analysis</h5>
                            <p class=\"text-muted mb-4\">
                                Stay ahead with expert insights on stocks, forex, and crypto
                            </p>
                            <a href=\"";
        // line 108
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "\" class=\"btn btn-outline-primary\">
                                Read Analysis
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* Error Page Styles */
.error-page-container {
    min-height: 100vh;
}

.error-number {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.helpful-link-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.helpful-link-card .icon-container {
    transition: transform 0.3s ease;
}

.helpful-link-card:hover .icon-container {
    transform: scale(1.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .error-number {
        font-size: 4rem !important;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1.1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "@Twig/Exception/error.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  260 => 108,  239 => 90,  199 => 53,  188 => 45,  181 => 40,  177 => 38,  173 => 36,  171 => 35,  168 => 34,  166 => 33,  160 => 29,  156 => 27,  152 => 25,  150 => 24,  147 => 23,  145 => 22,  137 => 17,  126 => 8,  113 => 7,  90 => 5,  65 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Error {{ status_code|default('') }} - Capitol Academy{% endblock %}

{% block meta_description %}An error occurred while processing your request. Return to Capitol Academy's homepage or contact us for assistance.{% endblock %}

{% block body %}
<div class=\"error-page-container\">
    <!-- Hero Section -->
    <section class=\"error-hero-section py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%); min-height: 70vh;\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-8 mx-auto text-center\">
                    <div class=\"error-content text-white\">
                        <!-- Error Code -->
                        <h1 class=\"error-number display-1 fw-bold mb-4\" style=\"font-size: 6rem; color: #a90418; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\">
                            {{ status_code|default('Error') }}
                        </h1>
                        
                        <!-- Error Title -->
                        <h2 class=\"error-title h1 fw-bold mb-4\">
                            {% if status_code == 403 %}
                                Access Denied
                            {% elseif status_code == 500 %}
                                Server Error
                            {% else %}
                                Something Went Wrong
                            {% endif %}
                        </h2>
                        
                        <!-- Error Description -->
                        <p class=\"error-description lead mb-5\" style=\"font-size: 1.3rem; line-height: 1.8; opacity: 0.9;\">
                            {% if status_code == 403 %}
                                You don't have permission to access this resource.
                            {% elseif status_code == 500 %}
                                We're experiencing technical difficulties. Please try again later.
                            {% else %}
                                An unexpected error occurred while processing your request.
                            {% endif %}
                            Our team has been notified and is working to resolve the issue.
                        </p>
                        
                        <!-- Action Buttons -->
                        <div class=\"error-actions d-flex flex-wrap justify-content-center gap-3\">
                            <a href=\"{{ path('app_home') }}\" class=\"btn btn-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-home me-2\"></i>
                                Return Home
                            </a>
                            <a href=\"javascript:history.back()\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-arrow-left me-2\"></i>
                                Go Back
                            </a>
                            <a href=\"{{ path('app_contact') }}\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-envelope me-2\"></i>
                                Contact Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Helpful Links Section -->
    <section class=\"helpful-links-section py-5\" style=\"background: white;\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-8 mx-auto text-center mb-5\">
                    <h3 class=\"h2 fw-bold mb-4\" style=\"color: #011a2d;\">
                        Continue Your Learning Journey
                    </h3>
                    <p class=\"lead\" style=\"color: #6c757d;\">
                        Don't let this stop you from achieving your trading goals
                    </p>
                </div>
            </div>
            
            <div class=\"row g-4\">
                <!-- Trading Courses -->
                <div class=\"col-lg-6 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-graduation-cap\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Trading Courses</h5>
                            <p class=\"text-muted mb-4\">
                                Master financial markets with our comprehensive course catalog
                            </p>
                            <a href=\"{{ path('app_courses') }}\" class=\"btn btn-outline-primary\">
                                Explore Courses
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Market Analysis -->
                <div class=\"col-lg-6 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-chart-line\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Market Analysis</h5>
                            <p class=\"text-muted mb-4\">
                                Stay ahead with expert insights on stocks, forex, and crypto
                            </p>
                            <a href=\"{{ path('app_market_analysis') }}\" class=\"btn btn-outline-primary\">
                                Read Analysis
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* Error Page Styles */
.error-page-container {
    min-height: 100vh;
}

.error-number {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.helpful-link-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.helpful-link-card .icon-container {
    transition: transform 0.3s ease;
}

.helpful-link-card:hover .icon-container {
    transform: scale(1.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .error-number {
        font-size: 4rem !important;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1.1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
{% endblock %}
", "@Twig/Exception/error.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\bundles\\TwigBundle\\Exception\\error.html.twig");
    }
}
