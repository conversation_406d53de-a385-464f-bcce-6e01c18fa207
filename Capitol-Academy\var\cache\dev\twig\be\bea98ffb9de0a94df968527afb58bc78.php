<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/index.html.twig */
class __TwigTemplate_fa46e75e41c3c93d612d6bf68c6a8e86 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Onsite Courses Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Onsite Courses Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Onsite Courses</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Onsite Courses Management", "page_icon" => "fas fa-graduation-cap", "search_placeholder" => "Search onsite courses...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_create"), "text" => "Add New Onsite Course", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Onsite Courses", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 25
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 25, $this->source); })())), "icon" => "fas fa-layer-group", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 32
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 32, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 32, $this->source); })()), "isActive", [], "any", false, false, false, 32); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Inactive", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 39
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 39, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 39, $this->source); })()), "isActive", [], "any", false, false, false, 39); })), "icon" => "fas fa-pause-circle", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 46, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-clock", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/onsite_courses/index.html.twig", 54, "1911002764")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        // line 94
        yield "
<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">Confirm Status Change</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p id=\"statusToggleMessage\"></p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-primary\" id=\"confirmStatusToggle\">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteConfirmModal\" tabindex=\"-1\" aria-labelledby=\"deleteConfirmModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"deleteConfirmModalLabel\">Confirm Deletion</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to delete the onsite course \"<span id=\"deleteItemTitle\"></span>\"?</p>
                <p class=\"text-danger\"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmDelete\">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
// Status toggle functionality
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusToggleMessage').textContent = 
        `Are you sure you want to \${action} the onsite course \"\${itemName}\"?`;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Onsite course management functions
function toggleOnsiteCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeOnsiteCourseStatusToggle(courseId);
    });
}

function deleteOnsiteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeOnsiteCourseDelete(courseId);
    });
}

function executeOnsiteCourseStatusToggle(courseId) {
    fetch(`/admin/onsite-courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the onsite course status.');
    });
}

function executeOnsiteCourseDelete(courseId) {
    fetch(`/admin/onsite-courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the onsite course.');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  164 => 94,  162 => 54,  159 => 53,  157 => 46,  156 => 39,  155 => 32,  154 => 25,  153 => 13,  140 => 12,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Onsite Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Onsite Courses Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Onsite Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Onsite Courses Management',
    'page_icon': 'fas fa-graduation-cap',
    'search_placeholder': 'Search onsite courses...',
    'create_button': {
        'url': path('admin_onsite_course_create'),
        'text': 'Add New Onsite Course',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Onsite Courses',
            'value': courses|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': courses|filter(course => course.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': courses|filter(course => not course.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Level'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set table_rows = table_rows|merge([{
                'cells': [
                    {'content': '<span class=\"badge bg-primary\">' ~ course.code ~ '</span>'},
                    {'content': '<strong>' ~ course.title ~ '</strong>'},
                    {'content': course.category ?: '<span class=\"text-muted\">Not specified</span>'},
                    {'content': course.level ?: '<span class=\"text-muted\">Not specified</span>'},
                    {'content': course.isActive ? '<span class=\"badge bg-success\">Active</span>' : '<span class=\"badge bg-secondary\">Inactive</span>'},
                    {
                        'content': '<div class=\"btn-group\" role=\"group\">
                            <a href=\"' ~ path('admin_onsite_course_preview', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Onsite Course\"><i class=\"fas fa-eye\"></i></a>
                            <a href=\"' ~ path('admin_onsite_course_edit', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Onsite Course\"><i class=\"fas fa-edit\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Onsite Course\" onclick=\"toggleOnsiteCourseStatus(' ~ course.id ~ ', \\'' ~ course.title ~ '\\', ' ~ course.isActive ~ ')\"><i class=\"fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Onsite Course\" onclick=\"deleteOnsiteCourse(' ~ course.id ~ ', \\'' ~ course.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                        </div>'
                    }
                ]
            }]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'empty_message': 'No onsite courses found. <a href=\"' ~ path('admin_onsite_course_create') ~ '\" class=\"text-primary\">Create your first onsite course</a>.'
        } %}
    {% endblock %}
{% endembed %}

<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">Confirm Status Change</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p id=\"statusToggleMessage\"></p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-primary\" id=\"confirmStatusToggle\">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteConfirmModal\" tabindex=\"-1\" aria-labelledby=\"deleteConfirmModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"deleteConfirmModalLabel\">Confirm Deletion</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to delete the onsite course \"<span id=\"deleteItemTitle\"></span>\"?</p>
                <p class=\"text-danger\"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmDelete\">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
// Status toggle functionality
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusToggleMessage').textContent = 
        `Are you sure you want to \${action} the onsite course \"\${itemName}\"?`;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Onsite course management functions
function toggleOnsiteCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeOnsiteCourseStatusToggle(courseId);
    });
}

function deleteOnsiteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeOnsiteCourseDelete(courseId);
    });
}

function executeOnsiteCourseStatusToggle(courseId) {
    fetch(`/admin/onsite-courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the onsite course status.');
    });
}

function executeOnsiteCourseDelete(courseId) {
    fetch(`/admin/onsite-courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the onsite course.');
    });
}
</script>
{% endblock %}
", "admin/onsite_courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\index.html.twig");
    }
}


/* admin/onsite_courses/index.html.twig */
class __TwigTemplate_fa46e75e41c3c93d612d6bf68c6a8e86___1911002764 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "        <!-- Standardized Table -->
        ";
        // line 57
        $context["table_headers"] = [["text" => "Code"], ["text" => "Title"], ["text" => "Category"], ["text" => "Level"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 65
        yield "
        ";
        // line 66
        $context["table_rows"] = [];
        // line 67
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 67, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
            // line 68
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 68, $this->source); })()), [["cells" => [["content" => (("<span class=\"badge bg-primary\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 70
$context["course"], "code", [], "any", false, false, false, 70)) . "</span>")], ["content" => (("<strong>" . CoreExtension::getAttribute($this->env, $this->source,             // line 71
$context["course"], "title", [], "any", false, false, false, 71)) . "</strong>")], ["content" => ((CoreExtension::getAttribute($this->env, $this->source,             // line 72
$context["course"], "category", [], "any", false, false, false, 72)) ? (CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 72)) : ("<span class=\"text-muted\">Not specified</span>"))], ["content" => ((CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["course"], "level", [], "any", false, false, false, 73)) ? (CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 73)) : ("<span class=\"text-muted\">Not specified</span>"))], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 74
$context["course"], "isActive", [], "any", false, false, false, 74)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge bg-success\">Active</span>") : ("<span class=\"badge bg-secondary\">Inactive</span>"))], ["content" => (((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                            <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 77
$context["course"], "code", [], "any", false, false, false, 77)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Onsite Course\"><i class=\"fas fa-eye\"></i></a>
                            <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["course"], "code", [], "any", false, false, false, 78)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Onsite Course\"><i class=\"fas fa-edit\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 79
$context["course"], "isActive", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . "; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " Onsite Course\" onclick=\"toggleOnsiteCourseStatus(") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "id", [], "any", false, false, false, 79)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 79)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 79)) . ")\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"))) . "\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Onsite Course\" onclick=\"deleteOnsiteCourse(") . CoreExtension::getAttribute($this->env, $this->source,             // line 80
$context["course"], "id", [], "any", false, false, false, 80)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 80)) . "')\"><i class=\"fas fa-trash\"></i></button>
                        </div>")]]]]);
            // line 85
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 86
        yield "
        ";
        // line 87
        yield from $this->load("components/admin_table.html.twig", 87)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 88
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 88, $this->source); })()), "rows" =>         // line 89
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 89, $this->source); })()), "empty_message" => (("No onsite courses found. <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_create")) . "\" class=\"text-primary\">Create your first onsite course</a>.")]));
        // line 92
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  657 => 92,  655 => 89,  654 => 88,  653 => 87,  650 => 86,  644 => 85,  641 => 80,  639 => 79,  637 => 78,  635 => 77,  633 => 74,  632 => 73,  631 => 72,  630 => 71,  629 => 70,  627 => 68,  622 => 67,  620 => 66,  617 => 65,  615 => 57,  612 => 56,  599 => 55,  576 => 54,  164 => 94,  162 => 54,  159 => 53,  157 => 46,  156 => 39,  155 => 32,  154 => 25,  153 => 13,  140 => 12,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Onsite Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Onsite Courses Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Onsite Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Onsite Courses Management',
    'page_icon': 'fas fa-graduation-cap',
    'search_placeholder': 'Search onsite courses...',
    'create_button': {
        'url': path('admin_onsite_course_create'),
        'text': 'Add New Onsite Course',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Onsite Courses',
            'value': courses|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': courses|filter(course => course.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': courses|filter(course => not course.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Level'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set table_rows = table_rows|merge([{
                'cells': [
                    {'content': '<span class=\"badge bg-primary\">' ~ course.code ~ '</span>'},
                    {'content': '<strong>' ~ course.title ~ '</strong>'},
                    {'content': course.category ?: '<span class=\"text-muted\">Not specified</span>'},
                    {'content': course.level ?: '<span class=\"text-muted\">Not specified</span>'},
                    {'content': course.isActive ? '<span class=\"badge bg-success\">Active</span>' : '<span class=\"badge bg-secondary\">Inactive</span>'},
                    {
                        'content': '<div class=\"btn-group\" role=\"group\">
                            <a href=\"' ~ path('admin_onsite_course_preview', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Onsite Course\"><i class=\"fas fa-eye\"></i></a>
                            <a href=\"' ~ path('admin_onsite_course_edit', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Onsite Course\"><i class=\"fas fa-edit\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Onsite Course\" onclick=\"toggleOnsiteCourseStatus(' ~ course.id ~ ', \\'' ~ course.title ~ '\\', ' ~ course.isActive ~ ')\"><i class=\"fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Onsite Course\" onclick=\"deleteOnsiteCourse(' ~ course.id ~ ', \\'' ~ course.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                        </div>'
                    }
                ]
            }]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'empty_message': 'No onsite courses found. <a href=\"' ~ path('admin_onsite_course_create') ~ '\" class=\"text-primary\">Create your first onsite course</a>.'
        } %}
    {% endblock %}
{% endembed %}

<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">Confirm Status Change</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p id=\"statusToggleMessage\"></p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-primary\" id=\"confirmStatusToggle\">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteConfirmModal\" tabindex=\"-1\" aria-labelledby=\"deleteConfirmModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"deleteConfirmModalLabel\">Confirm Deletion</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to delete the onsite course \"<span id=\"deleteItemTitle\"></span>\"?</p>
                <p class=\"text-danger\"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmDelete\">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
// Status toggle functionality
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusToggleMessage').textContent = 
        `Are you sure you want to \${action} the onsite course \"\${itemName}\"?`;
    
    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Onsite course management functions
function toggleOnsiteCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeOnsiteCourseStatusToggle(courseId);
    });
}

function deleteOnsiteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeOnsiteCourseDelete(courseId);
    });
}

function executeOnsiteCourseStatusToggle(courseId) {
    fetch(`/admin/onsite-courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the onsite course status.');
    });
}

function executeOnsiteCourseDelete(courseId) {
    fetch(`/admin/onsite-courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the onsite course.');
    });
}
</script>
{% endblock %}
", "admin/onsite_courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\index.html.twig");
    }
}
