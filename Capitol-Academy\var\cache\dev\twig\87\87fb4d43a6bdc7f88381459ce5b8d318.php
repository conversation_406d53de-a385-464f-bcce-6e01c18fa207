<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* emails/contact_notification.html.twig */
class __TwigTemplate_628503cb898a85a90902ff77acf1aff0 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "emails/contact_notification.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "emails/contact_notification.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html>
<head>
    <meta charset=\"UTF-8\">
    <title>New Contact Form Submission - Capitol Academy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 5px 5px;
            font-size: 12px;
        }
        .field {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
        }
        .field-value {
            margin-top: 5px;
            padding: 8px;
            background-color: white;
            border: 1px solid #ced4da;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class=\"header\">
        <h1>New Contact Form Submission</h1>
        <p>Capitol Academy - Financial Markets Education</p>
    </div>
    
    <div class=\"content\">
        <div class=\"field\">
            <div class=\"field-label\">Full Name:</div>
            <div class=\"field-value\">";
        // line 60
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 60, $this->source); })()), "fullName", [], "any", false, false, false, 60), "html", null, true);
        yield "</div>
        </div>
        
        <div class=\"field\">
            <div class=\"field-label\">Email:</div>
            <div class=\"field-value\">";
        // line 65
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 65, $this->source); })()), "email", [], "any", false, false, false, 65), "html", null, true);
        yield "</div>
        </div>
        
        ";
        // line 68
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 68, $this->source); })()), "phone", [], "any", false, false, false, 68)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 69
            yield "        <div class=\"field\">
            <div class=\"field-label\">Phone:</div>
            <div class=\"field-value\">";
            // line 71
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 71, $this->source); })()), "phone", [], "any", false, false, false, 71), "html", null, true);
            yield "</div>
        </div>
        ";
        }
        // line 74
        yield "        
        ";
        // line 75
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 75, $this->source); })()), "country", [], "any", false, false, false, 75)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 76
            yield "        <div class=\"field\">
            <div class=\"field-label\">Country:</div>
            <div class=\"field-value\">";
            // line 78
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 78, $this->source); })()), "country", [], "any", false, false, false, 78), "html", null, true);
            yield "</div>
        </div>
        ";
        }
        // line 81
        yield "        
        ";
        // line 82
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 82, $this->source); })()), "subject", [], "any", false, false, false, 82)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 83
            yield "        <div class=\"field\">
            <div class=\"field-label\">Subject:</div>
            <div class=\"field-value\">";
            // line 85
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 85, $this->source); })()), "subject", [], "any", false, false, false, 85), "html", null, true);
            yield "</div>
        </div>
        ";
        }
        // line 88
        yield "        
        ";
        // line 89
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 89, $this->source); })()), "message", [], "any", false, false, false, 89)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 90
            yield "        <div class=\"field\">
            <div class=\"field-label\">Message:</div>
            <div class=\"field-value\">";
            // line 92
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 92, $this->source); })()), "message", [], "any", false, false, false, 92), "html", null, true));
            yield "</div>
        </div>
        ";
        }
        // line 95
        yield "        
        <div class=\"field\">
            <div class=\"field-label\">Submitted:</div>
            <div class=\"field-value\">";
        // line 98
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 98, $this->source); })()), "createdAt", [], "any", false, false, false, 98), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</div>
        </div>
    </div>
    
    <div class=\"footer\">
        <p>This email was automatically generated by the Capitol Academy website contact form.</p>
        <p>Please respond to the customer at: ";
        // line 104
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 104, $this->source); })()), "email", [], "any", false, false, false, 104), "html", null, true);
        yield "</p>
    </div>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "emails/contact_notification.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  194 => 104,  185 => 98,  180 => 95,  174 => 92,  170 => 90,  168 => 89,  165 => 88,  159 => 85,  155 => 83,  153 => 82,  150 => 81,  144 => 78,  140 => 76,  138 => 75,  135 => 74,  129 => 71,  125 => 69,  123 => 68,  117 => 65,  109 => 60,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html>
<head>
    <meta charset=\"UTF-8\">
    <title>New Contact Form Submission - Capitol Academy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 5px 5px;
            font-size: 12px;
        }
        .field {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
        }
        .field-value {
            margin-top: 5px;
            padding: 8px;
            background-color: white;
            border: 1px solid #ced4da;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class=\"header\">
        <h1>New Contact Form Submission</h1>
        <p>Capitol Academy - Financial Markets Education</p>
    </div>
    
    <div class=\"content\">
        <div class=\"field\">
            <div class=\"field-label\">Full Name:</div>
            <div class=\"field-value\">{{ contact.fullName }}</div>
        </div>
        
        <div class=\"field\">
            <div class=\"field-label\">Email:</div>
            <div class=\"field-value\">{{ contact.email }}</div>
        </div>
        
        {% if contact.phone %}
        <div class=\"field\">
            <div class=\"field-label\">Phone:</div>
            <div class=\"field-value\">{{ contact.phone }}</div>
        </div>
        {% endif %}
        
        {% if contact.country %}
        <div class=\"field\">
            <div class=\"field-label\">Country:</div>
            <div class=\"field-value\">{{ contact.country }}</div>
        </div>
        {% endif %}
        
        {% if contact.subject %}
        <div class=\"field\">
            <div class=\"field-label\">Subject:</div>
            <div class=\"field-value\">{{ contact.subject }}</div>
        </div>
        {% endif %}
        
        {% if contact.message %}
        <div class=\"field\">
            <div class=\"field-label\">Message:</div>
            <div class=\"field-value\">{{ contact.message|nl2br }}</div>
        </div>
        {% endif %}
        
        <div class=\"field\">
            <div class=\"field-label\">Submitted:</div>
            <div class=\"field-value\">{{ contact.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}</div>
        </div>
    </div>
    
    <div class=\"footer\">
        <p>This email was automatically generated by the Capitol Academy website contact form.</p>
        <p>Please respond to the customer at: {{ contact.email }}</p>
    </div>
</body>
</html>
", "emails/contact_notification.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\emails\\contact_notification.html.twig");
    }
}
