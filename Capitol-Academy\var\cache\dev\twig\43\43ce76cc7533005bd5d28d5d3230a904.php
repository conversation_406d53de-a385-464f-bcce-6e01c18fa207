<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* search/results.html.twig */
class __TwigTemplate_b2d2e085c74a072fcfc7fd3737e76c7e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "search/results.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "search/results.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Search Results for \"";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["query"]) || array_key_exists("query", $context) ? $context["query"] : (function () { throw new RuntimeError('Variable "query" does not exist.', 3, $this->source); })()), "html", null, true);
        yield "\" | Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Search results for \"";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["query"]) || array_key_exists("query", $context) ? $context["query"] : (function () { throw new RuntimeError('Variable "query" does not exist.', 5, $this->source); })()), "html", null, true);
        yield "\" on Capitol Academy - Find trading courses, market analysis, and educational content.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
<style>
    .search-results-page {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .search-header {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
        border: 1px solid #e9ecef;
    }

    .search-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .search-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .search-result-item {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(1, 26, 45, 0.05);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .search-result-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(1, 26, 45, 0.12);
        border-color: #011a2d;
    }

    .result-category {
        background: #011a2d;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .result-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        text-decoration: none;
        display: block;
    }

    .result-title:hover {
        color: #a90418;
        text-decoration: none;
    }

    .result-excerpt {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .result-link {
        color: #011a2d;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: color 0.3s ease;
    }

    .result-link:hover {
        color: #a90418;
        text-decoration: none;
    }

    .no-results {
        text-align: center;
        padding: 3rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
    }

    .no-results-icon {
        font-size: 4rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .no-results-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .no-results-text {
        color: #6c757d;
        font-size: 1.1rem;
    }
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 129
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 130
        yield "<div class=\"search-results-page\">
    <div class=\"container\">
        <!-- Search Header -->
        <div class=\"search-header\">
            <h1 class=\"search-title\">Search Results</h1>
            <p class=\"search-subtitle\">
                ";
        // line 136
        if ((($tmp = (isset($context["query"]) || array_key_exists("query", $context) ? $context["query"] : (function () { throw new RuntimeError('Variable "query" does not exist.', 136, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 137
            yield "                    Found ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["total"]) || array_key_exists("total", $context) ? $context["total"] : (function () { throw new RuntimeError('Variable "total" does not exist.', 137, $this->source); })()), "html", null, true);
            yield " result";
            yield ((((isset($context["total"]) || array_key_exists("total", $context) ? $context["total"] : (function () { throw new RuntimeError('Variable "total" does not exist.', 137, $this->source); })()) != 1)) ? ("s") : (""));
            yield " for \"<strong>";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["query"]) || array_key_exists("query", $context) ? $context["query"] : (function () { throw new RuntimeError('Variable "query" does not exist.', 137, $this->source); })()), "html", null, true);
            yield "</strong>\"
                ";
        } else {
            // line 139
            yield "                    Please enter a search term
                ";
        }
        // line 141
        yield "            </p>
        </div>

        <!-- Search Results -->
        ";
        // line 145
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["results"]) || array_key_exists("results", $context) ? $context["results"] : (function () { throw new RuntimeError('Variable "results" does not exist.', 145, $this->source); })())) > 0)) {
            // line 146
            yield "            <div class=\"search-results\">
                ";
            // line 147
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["results"]) || array_key_exists("results", $context) ? $context["results"] : (function () { throw new RuntimeError('Variable "results" does not exist.', 147, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["result"]) {
                // line 148
                yield "                    <div class=\"search-result-item\">
                        <span class=\"result-category\">";
                // line 149
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["result"], "category", [], "any", false, false, false, 149), "html", null, true);
                yield "</span>
                        <a href=\"";
                // line 150
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["result"], "url", [], "any", false, false, false, 150), "html", null, true);
                yield "\" class=\"result-title\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["result"], "title", [], "any", false, false, false, 150), "html", null, true);
                yield "</a>
                        ";
                // line 151
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["result"], "excerpt", [], "any", false, false, false, 151)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 152
                    yield "                            <p class=\"result-excerpt\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["result"], "excerpt", [], "any", false, false, false, 152), "html", null, true);
                    yield "</p>
                        ";
                }
                // line 154
                yield "                        <a href=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["result"], "url", [], "any", false, false, false, 154), "html", null, true);
                yield "\" class=\"result-link\">
                            ";
                // line 155
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["result"], "type", [], "any", false, false, false, 155) == "video")) {
                    // line 156
                    yield "                                <i class=\"fas fa-video\"></i>
                                Watch Video
                            ";
                } else {
                    // line 159
                    yield "                                <i class=\"fas fa-arrow-right\"></i>
                                Read More
                            ";
                }
                // line 162
                yield "                        </a>
                    </div>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['result'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 165
            yield "            </div>
        ";
        } else {
            // line 167
            yield "            <div class=\"no-results\">
                <div class=\"no-results-icon\">
                    <i class=\"fas fa-search\"></i>
                </div>
                <h2 class=\"no-results-title\">No Results Found</h2>
                <p class=\"no-results-text\">
                    ";
            // line 173
            if ((($tmp = (isset($context["query"]) || array_key_exists("query", $context) ? $context["query"] : (function () { throw new RuntimeError('Variable "query" does not exist.', 173, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 174
                yield "                        We couldn't find any content matching \"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["query"]) || array_key_exists("query", $context) ? $context["query"] : (function () { throw new RuntimeError('Variable "query" does not exist.', 174, $this->source); })()), "html", null, true);
                yield "\". Try using different keywords or browse our categories.
                    ";
            } else {
                // line 176
                yield "                        Enter a search term to find trading courses, market analysis, and educational content.
                    ";
            }
            // line 178
            yield "                </p>
            </div>
        ";
        }
        // line 181
        yield "    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "search/results.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  387 => 181,  382 => 178,  378 => 176,  372 => 174,  370 => 173,  362 => 167,  358 => 165,  350 => 162,  345 => 159,  340 => 156,  338 => 155,  333 => 154,  327 => 152,  325 => 151,  319 => 150,  315 => 149,  312 => 148,  308 => 147,  305 => 146,  303 => 145,  297 => 141,  293 => 139,  283 => 137,  281 => 136,  273 => 130,  260 => 129,  129 => 8,  116 => 7,  91 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Search Results for \"{{ query }}\" | Capitol Academy{% endblock %}

{% block meta_description %}Search results for \"{{ query }}\" on Capitol Academy - Find trading courses, market analysis, and educational content.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .search-results-page {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .search-header {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
        border: 1px solid #e9ecef;
    }

    .search-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .search-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .search-result-item {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(1, 26, 45, 0.05);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .search-result-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(1, 26, 45, 0.12);
        border-color: #011a2d;
    }

    .result-category {
        background: #011a2d;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .result-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        text-decoration: none;
        display: block;
    }

    .result-title:hover {
        color: #a90418;
        text-decoration: none;
    }

    .result-excerpt {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .result-link {
        color: #011a2d;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: color 0.3s ease;
    }

    .result-link:hover {
        color: #a90418;
        text-decoration: none;
    }

    .no-results {
        text-align: center;
        padding: 3rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
    }

    .no-results-icon {
        font-size: 4rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .no-results-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .no-results-text {
        color: #6c757d;
        font-size: 1.1rem;
    }
</style>
{% endblock %}

{% block body %}
<div class=\"search-results-page\">
    <div class=\"container\">
        <!-- Search Header -->
        <div class=\"search-header\">
            <h1 class=\"search-title\">Search Results</h1>
            <p class=\"search-subtitle\">
                {% if query %}
                    Found {{ total }} result{{ total != 1 ? 's' : '' }} for \"<strong>{{ query }}</strong>\"
                {% else %}
                    Please enter a search term
                {% endif %}
            </p>
        </div>

        <!-- Search Results -->
        {% if results|length > 0 %}
            <div class=\"search-results\">
                {% for result in results %}
                    <div class=\"search-result-item\">
                        <span class=\"result-category\">{{ result.category }}</span>
                        <a href=\"{{ result.url }}\" class=\"result-title\">{{ result.title }}</a>
                        {% if result.excerpt %}
                            <p class=\"result-excerpt\">{{ result.excerpt }}</p>
                        {% endif %}
                        <a href=\"{{ result.url }}\" class=\"result-link\">
                            {% if result.type == 'video' %}
                                <i class=\"fas fa-video\"></i>
                                Watch Video
                            {% else %}
                                <i class=\"fas fa-arrow-right\"></i>
                                Read More
                            {% endif %}
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class=\"no-results\">
                <div class=\"no-results-icon\">
                    <i class=\"fas fa-search\"></i>
                </div>
                <h2 class=\"no-results-title\">No Results Found</h2>
                <p class=\"no-results-text\">
                    {% if query %}
                        We couldn't find any content matching \"{{ query }}\". Try using different keywords or browse our categories.
                    {% else %}
                        Enter a search term to find trading courses, market analysis, and educational content.
                    {% endif %}
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
", "search/results.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\search\\results.html.twig");
    }
}
