<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* components/admin_table.html.twig */
class __TwigTemplate_dc69b6f2d5fd42556a5ba940e5e54e35 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_table.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_table.html.twig"));

        // line 18
        yield "
<!-- Professional Table -->
<div class=\"table-responsive\">
    <table class=\"table table-hover border-0 shadow-sm admin-table\" 
           ";
        // line 22
        if (array_key_exists("table_id", $context)) {
            yield "id=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["table_id"]) || array_key_exists("table_id", $context) ? $context["table_id"] : (function () { throw new RuntimeError('Variable "table_id" does not exist.', 22, $this->source); })()), "html", null, true);
            yield "\"";
        }
        // line 23
        yield "           ";
        if (array_key_exists("table_attributes", $context)) {
            yield (isset($context["table_attributes"]) || array_key_exists("table_attributes", $context) ? $context["table_attributes"] : (function () { throw new RuntimeError('Variable "table_attributes" does not exist.', 23, $this->source); })());
        }
        // line 24
        yield "           style=\"background: white; border-radius: 12px; overflow: hidden;\">
        
        <!-- Table Header -->
        <thead style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
            <tr>
                ";
        // line 29
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["headers"]) || array_key_exists("headers", $context) ? $context["headers"] : (function () { throw new RuntimeError('Variable "headers" does not exist.', 29, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["header"]) {
            // line 30
            yield "                <th style=\"border: none; padding: 1rem; font-weight: 600; text-transform: uppercase; font-size: 0.85rem; letter-spacing: 0.5px; ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["header"], "style", [], "any", true, true, false, 30)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["header"], "style", [], "any", false, false, false, 30), "")) : ("")), "html", null, true);
            yield "\">
                    ";
            // line 31
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["header"], "text", [], "any", false, false, false, 31), "html", null, true);
            yield "
                </th>
                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['header'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 34
        yield "            </tr>
        </thead>
        
        <!-- Table Body -->
        <tbody>
            ";
        // line 39
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["rows"]) || array_key_exists("rows", $context) ? $context["rows"] : (function () { throw new RuntimeError('Variable "rows" does not exist.', 39, $this->source); })())) > 0)) {
            // line 40
            yield "                ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["rows"]) || array_key_exists("rows", $context) ? $context["rows"] : (function () { throw new RuntimeError('Variable "rows" does not exist.', 40, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["row"]) {
                // line 41
                yield "                <tr class=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("row_class", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["row_class"]) || array_key_exists("row_class", $context) ? $context["row_class"] : (function () { throw new RuntimeError('Variable "row_class" does not exist.', 41, $this->source); })()), "table-row")) : ("table-row")), "html", null, true);
                yield "\" 
                    style=\"transition: all 0.3s ease; border-bottom: 1px solid rgba(0,0,0,0.05);\"
                    ";
                // line 43
                if (CoreExtension::getAttribute($this->env, $this->source, $context["row"], "attributes", [], "any", true, true, false, 43)) {
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["row"], "attributes", [], "any", false, false, false, 43);
                }
                yield ">
                    ";
                // line 44
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["row"], "cells", [], "any", false, false, false, 44));
                foreach ($context['_seq'] as $context["_key"] => $context["cell"]) {
                    // line 45
                    yield "                    <td style=\"padding: 1rem; vertical-align: middle; border: none; ";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["cell"], "style", [], "any", true, true, false, 45)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["cell"], "style", [], "any", false, false, false, 45), "")) : ("")), "html", null, true);
                    yield "\">
                        ";
                    // line 46
                    yield CoreExtension::getAttribute($this->env, $this->source, $context["cell"], "content", [], "any", false, false, false, 46);
                    yield "
                    </td>
                    ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['cell'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 49
                yield "                </tr>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['row'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 51
            yield "            ";
        } else {
            // line 52
            yield "                <!-- Empty State -->
                <tr>
                    <td colspan=\"";
            // line 54
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["headers"]) || array_key_exists("headers", $context) ? $context["headers"] : (function () { throw new RuntimeError('Variable "headers" does not exist.', 54, $this->source); })())), "html", null, true);
            yield "\" class=\"text-center py-5\">
                        <div class=\"empty-state\">
                            <div class=\"mb-4\">
                                <i class=\"";
            // line 57
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("empty_icon", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["empty_icon"]) || array_key_exists("empty_icon", $context) ? $context["empty_icon"] : (function () { throw new RuntimeError('Variable "empty_icon" does not exist.', 57, $this->source); })()), "fas fa-inbox")) : ("fas fa-inbox")), "html", null, true);
            yield " fa-4x text-muted\"></i>
                            </div>
                            <h4 class=\"text-muted mb-3\">";
            // line 59
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("empty_message", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["empty_message"]) || array_key_exists("empty_message", $context) ? $context["empty_message"] : (function () { throw new RuntimeError('Variable "empty_message" does not exist.', 59, $this->source); })()), "No data found")) : ("No data found")), "html", null, true);
            yield "</h4>
                            ";
            // line 60
            if ((array_key_exists("empty_description", $context) && (isset($context["empty_description"]) || array_key_exists("empty_description", $context) ? $context["empty_description"] : (function () { throw new RuntimeError('Variable "empty_description" does not exist.', 60, $this->source); })()))) {
                // line 61
                yield "                                <p class=\"text-muted mb-4\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["empty_description"]) || array_key_exists("empty_description", $context) ? $context["empty_description"] : (function () { throw new RuntimeError('Variable "empty_description" does not exist.', 61, $this->source); })()), "html", null, true);
                yield "</p>
                            ";
            }
            // line 63
            yield "                            ";
            if (array_key_exists("empty_action", $context)) {
                // line 64
                yield "                                <a href=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["empty_action"]) || array_key_exists("empty_action", $context) ? $context["empty_action"] : (function () { throw new RuntimeError('Variable "empty_action" does not exist.', 64, $this->source); })()), "url", [], "any", false, false, false, 64), "html", null, true);
                yield "\" class=\"btn btn-primary btn-lg\">
                                    <i class=\"";
                // line 65
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["empty_action"] ?? null), "icon", [], "any", true, true, false, 65)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["empty_action"]) || array_key_exists("empty_action", $context) ? $context["empty_action"] : (function () { throw new RuntimeError('Variable "empty_action" does not exist.', 65, $this->source); })()), "icon", [], "any", false, false, false, 65), "fas fa-plus")) : ("fas fa-plus")), "html", null, true);
                yield " me-2\"></i>";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["empty_action"]) || array_key_exists("empty_action", $context) ? $context["empty_action"] : (function () { throw new RuntimeError('Variable "empty_action" does not exist.', 65, $this->source); })()), "text", [], "any", false, false, false, 65), "html", null, true);
                yield "
                                </a>
                            ";
            }
            // line 68
            yield "                        </div>
                    </td>
                </tr>
            ";
        }
        // line 72
        yield "        </tbody>
    </table>
</div>

<!-- Pagination -->
";
        // line 77
        if ((array_key_exists("pagination", $context) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 77, $this->source); })()), "total_pages", [], "any", false, false, false, 77) > 1))) {
            // line 78
            yield "<div class=\"row mt-4\">
    <div class=\"col-sm-12 col-md-5\">
        <div class=\"dataTables_info\">
            Showing ";
            // line 81
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 81, $this->source); })()), "current_page", [], "any", false, false, false, 81) - 1) * CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 81, $this->source); })()), "limit", [], "any", false, false, false, 81)) + 1), "html", null, true);
            yield " to 
            ";
            // line 82
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 82, $this->source); })()), "current_page", [], "any", false, false, false, 82) - 1) * CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 82, $this->source); })()), "limit", [], "any", false, false, false, 82)) + Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["rows"]) || array_key_exists("rows", $context) ? $context["rows"] : (function () { throw new RuntimeError('Variable "rows" does not exist.', 82, $this->source); })()))), "html", null, true);
            yield " of 
            ";
            // line 83
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 83, $this->source); })()), "total_items", [], "any", false, false, false, 83), "html", null, true);
            yield " entries
            ";
            // line 84
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 84, $this->source); })()), "search", [], "any", false, false, false, 84)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 85
                yield "                (filtered from total entries)
            ";
            }
            // line 87
            yield "        </div>
    </div>
    <div class=\"col-sm-12 col-md-7\">
        <div class=\"dataTables_paginate paging_simple_numbers\">
            <ul class=\"pagination justify-content-end\">
                <!-- Previous Button -->
                ";
            // line 93
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 93, $this->source); })()), "current_page", [], "any", false, false, false, 93) > 1)) {
                // line 94
                yield "                    <li class=\"paginate_button page-item previous\">
                        <a href=\"";
                // line 95
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 95, $this->source); })()), "base_url", [], "any", false, false, false, 95), "html", null, true);
                yield "?page=";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 95, $this->source); })()), "current_page", [], "any", false, false, false, 95) - 1), "html", null, true);
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 95, $this->source); })()), "search", [], "any", false, false, false, 95)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    yield "&search=";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 95, $this->source); })()), "search", [], "any", false, false, false, 95), "html", null, true);
                }
                yield "\" 
                           class=\"page-link\">Previous</a>
                    </li>
                ";
            } else {
                // line 99
                yield "                    <li class=\"paginate_button page-item previous disabled\">
                        <span class=\"page-link\">Previous</span>
                    </li>
                ";
            }
            // line 103
            yield "                
                <!-- Page Numbers -->
                ";
            // line 105
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 105, $this->source); })()), "total_pages", [], "any", false, false, false, 105)));
            foreach ($context['_seq'] as $context["_key"] => $context["page"]) {
                // line 106
                yield "                    ";
                if (($context["page"] == CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 106, $this->source); })()), "current_page", [], "any", false, false, false, 106))) {
                    // line 107
                    yield "                        <li class=\"paginate_button page-item active\">
                            <span class=\"page-link\">";
                    // line 108
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                    yield "</span>
                        </li>
                    ";
                } elseif ((((                // line 110
$context["page"] == 1) || ($context["page"] == CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 110, $this->source); })()), "total_pages", [], "any", false, false, false, 110))) || (($context["page"] >= (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 110, $this->source); })()), "current_page", [], "any", false, false, false, 110) - 2)) && ($context["page"] <= (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 110, $this->source); })()), "current_page", [], "any", false, false, false, 110) + 2))))) {
                    // line 111
                    yield "                        <li class=\"paginate_button page-item\">
                            <a href=\"";
                    // line 112
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 112, $this->source); })()), "base_url", [], "any", false, false, false, 112), "html", null, true);
                    yield "?page=";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                    if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 112, $this->source); })()), "search", [], "any", false, false, false, 112)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                        yield "&search=";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 112, $this->source); })()), "search", [], "any", false, false, false, 112), "html", null, true);
                    }
                    yield "\" 
                               class=\"page-link\">";
                    // line 113
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                    yield "</a>
                        </li>
                    ";
                } elseif (((                // line 115
$context["page"] == (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 115, $this->source); })()), "current_page", [], "any", false, false, false, 115) - 3)) || ($context["page"] == (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 115, $this->source); })()), "current_page", [], "any", false, false, false, 115) + 3)))) {
                    // line 116
                    yield "                        <li class=\"paginate_button page-item disabled\">
                            <span class=\"page-link\">...</span>
                        </li>
                    ";
                }
                // line 120
                yield "                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['page'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 121
            yield "                
                <!-- Next Button -->
                ";
            // line 123
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 123, $this->source); })()), "current_page", [], "any", false, false, false, 123) < CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 123, $this->source); })()), "total_pages", [], "any", false, false, false, 123))) {
                // line 124
                yield "                    <li class=\"paginate_button page-item next\">
                        <a href=\"";
                // line 125
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 125, $this->source); })()), "base_url", [], "any", false, false, false, 125), "html", null, true);
                yield "?page=";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 125, $this->source); })()), "current_page", [], "any", false, false, false, 125) + 1), "html", null, true);
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 125, $this->source); })()), "search", [], "any", false, false, false, 125)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    yield "&search=";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 125, $this->source); })()), "search", [], "any", false, false, false, 125), "html", null, true);
                }
                yield "\" 
                           class=\"page-link\">Next</a>
                    </li>
                ";
            } else {
                // line 129
                yield "                    <li class=\"paginate_button page-item next disabled\">
                        <span class=\"page-link\">Next</span>
                    </li>
                ";
            }
            // line 133
            yield "            </ul>
        </div>
    </div>
</div>
";
        }
        // line 138
        yield "
<!-- Table Styling -->
<style>
/* Table hover effects */
.admin-table tbody tr:hover {
    background-color: rgba(1, 26, 45, 0.05) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Empty state styling */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    opacity: 0.6;
}

.empty-state h4 {
    font-weight: 600;
}

/* Pagination styling */
.pagination .page-link {
    border: 1px solid #dee2e6;
    color: #011a2d;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #011a2d;
    border-color: #011a2d;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    border-color: #011a2d;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Responsive table */
@media (max-width: 768px) {
    .admin-table {
        font-size: 0.875rem;
    }
    
    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.75rem 0.5rem !important;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
    
    .empty-state i {
        font-size: 3rem !important;
    }
    
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .dataTables_info {
        text-align: center;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.5rem 0.25rem !important;
        font-size: 0.8rem;
    }
    
    .pagination {
        justify-content: center !important;
    }
    
    .pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Loading state */
.admin-table.loading {
    opacity: 0.6;
    pointer-events: none;
}

.admin-table.loading tbody::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #011a2d;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "components/admin_table.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  345 => 138,  338 => 133,  332 => 129,  319 => 125,  316 => 124,  314 => 123,  310 => 121,  304 => 120,  298 => 116,  296 => 115,  291 => 113,  281 => 112,  278 => 111,  276 => 110,  271 => 108,  268 => 107,  265 => 106,  261 => 105,  257 => 103,  251 => 99,  238 => 95,  235 => 94,  233 => 93,  225 => 87,  221 => 85,  219 => 84,  215 => 83,  211 => 82,  207 => 81,  202 => 78,  200 => 77,  193 => 72,  187 => 68,  179 => 65,  174 => 64,  171 => 63,  165 => 61,  163 => 60,  159 => 59,  154 => 57,  148 => 54,  144 => 52,  141 => 51,  134 => 49,  125 => 46,  120 => 45,  116 => 44,  110 => 43,  104 => 41,  99 => 40,  97 => 39,  90 => 34,  81 => 31,  76 => 30,  72 => 29,  65 => 24,  60 => 23,  54 => 22,  48 => 18,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{#
    Capitol Academy Admin Table Component
    Standardized table with consistent styling, pagination, and empty states
    
    Parameters:
    - headers: Array of header objects with {text, style} properties
    - rows: Array of row objects with {cells, attributes} properties
    - row_class: CSS class for table rows (for search targeting)
    - table_id: Optional table ID
    - table_attributes: Optional table attributes
    - empty_message: Message to show when no data
    - empty_icon: FontAwesome icon for empty state
    - empty_description: Description for empty state
    - empty_action: Object with {url, text, icon} for empty state action
    - search_config: Object with {fields} array for search targeting
    - pagination: Object with pagination data
#}

<!-- Professional Table -->
<div class=\"table-responsive\">
    <table class=\"table table-hover border-0 shadow-sm admin-table\" 
           {% if table_id is defined %}id=\"{{ table_id }}\"{% endif %}
           {% if table_attributes is defined %}{{ table_attributes|raw }}{% endif %}
           style=\"background: white; border-radius: 12px; overflow: hidden;\">
        
        <!-- Table Header -->
        <thead style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
            <tr>
                {% for header in headers %}
                <th style=\"border: none; padding: 1rem; font-weight: 600; text-transform: uppercase; font-size: 0.85rem; letter-spacing: 0.5px; {{ header.style|default('') }}\">
                    {{ header.text }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        
        <!-- Table Body -->
        <tbody>
            {% if rows|length > 0 %}
                {% for row in rows %}
                <tr class=\"{{ row_class|default('table-row') }}\" 
                    style=\"transition: all 0.3s ease; border-bottom: 1px solid rgba(0,0,0,0.05);\"
                    {% if row.attributes is defined %}{{ row.attributes|raw }}{% endif %}>
                    {% for cell in row.cells %}
                    <td style=\"padding: 1rem; vertical-align: middle; border: none; {{ cell.style|default('') }}\">
                        {{ cell.content|raw }}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            {% else %}
                <!-- Empty State -->
                <tr>
                    <td colspan=\"{{ headers|length }}\" class=\"text-center py-5\">
                        <div class=\"empty-state\">
                            <div class=\"mb-4\">
                                <i class=\"{{ empty_icon|default('fas fa-inbox') }} fa-4x text-muted\"></i>
                            </div>
                            <h4 class=\"text-muted mb-3\">{{ empty_message|default('No data found') }}</h4>
                            {% if empty_description is defined and empty_description %}
                                <p class=\"text-muted mb-4\">{{ empty_description }}</p>
                            {% endif %}
                            {% if empty_action is defined %}
                                <a href=\"{{ empty_action.url }}\" class=\"btn btn-primary btn-lg\">
                                    <i class=\"{{ empty_action.icon|default('fas fa-plus') }} me-2\"></i>{{ empty_action.text }}
                                </a>
                            {% endif %}
                        </div>
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if pagination is defined and pagination.total_pages > 1 %}
<div class=\"row mt-4\">
    <div class=\"col-sm-12 col-md-5\">
        <div class=\"dataTables_info\">
            Showing {{ ((pagination.current_page - 1) * pagination.limit) + 1 }} to 
            {{ ((pagination.current_page - 1) * pagination.limit) + rows|length }} of 
            {{ pagination.total_items }} entries
            {% if pagination.search %}
                (filtered from total entries)
            {% endif %}
        </div>
    </div>
    <div class=\"col-sm-12 col-md-7\">
        <div class=\"dataTables_paginate paging_simple_numbers\">
            <ul class=\"pagination justify-content-end\">
                <!-- Previous Button -->
                {% if pagination.current_page > 1 %}
                    <li class=\"paginate_button page-item previous\">
                        <a href=\"{{ pagination.base_url }}?page={{ pagination.current_page - 1 }}{% if pagination.search %}&search={{ pagination.search }}{% endif %}\" 
                           class=\"page-link\">Previous</a>
                    </li>
                {% else %}
                    <li class=\"paginate_button page-item previous disabled\">
                        <span class=\"page-link\">Previous</span>
                    </li>
                {% endif %}
                
                <!-- Page Numbers -->
                {% for page in 1..pagination.total_pages %}
                    {% if page == pagination.current_page %}
                        <li class=\"paginate_button page-item active\">
                            <span class=\"page-link\">{{ page }}</span>
                        </li>
                    {% elseif page == 1 or page == pagination.total_pages or (page >= pagination.current_page - 2 and page <= pagination.current_page + 2) %}
                        <li class=\"paginate_button page-item\">
                            <a href=\"{{ pagination.base_url }}?page={{ page }}{% if pagination.search %}&search={{ pagination.search }}{% endif %}\" 
                               class=\"page-link\">{{ page }}</a>
                        </li>
                    {% elseif page == pagination.current_page - 3 or page == pagination.current_page + 3 %}
                        <li class=\"paginate_button page-item disabled\">
                            <span class=\"page-link\">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                <!-- Next Button -->
                {% if pagination.current_page < pagination.total_pages %}
                    <li class=\"paginate_button page-item next\">
                        <a href=\"{{ pagination.base_url }}?page={{ pagination.current_page + 1 }}{% if pagination.search %}&search={{ pagination.search }}{% endif %}\" 
                           class=\"page-link\">Next</a>
                    </li>
                {% else %}
                    <li class=\"paginate_button page-item next disabled\">
                        <span class=\"page-link\">Next</span>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</div>
{% endif %}

<!-- Table Styling -->
<style>
/* Table hover effects */
.admin-table tbody tr:hover {
    background-color: rgba(1, 26, 45, 0.05) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Empty state styling */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    opacity: 0.6;
}

.empty-state h4 {
    font-weight: 600;
}

/* Pagination styling */
.pagination .page-link {
    border: 1px solid #dee2e6;
    color: #011a2d;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #011a2d;
    border-color: #011a2d;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    border-color: #011a2d;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Responsive table */
@media (max-width: 768px) {
    .admin-table {
        font-size: 0.875rem;
    }
    
    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.75rem 0.5rem !important;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
    
    .empty-state i {
        font-size: 3rem !important;
    }
    
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .dataTables_info {
        text-align: center;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.5rem 0.25rem !important;
        font-size: 0.8rem;
    }
    
    .pagination {
        justify-content: center !important;
    }
    
    .pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Loading state */
.admin-table.loading {
    opacity: 0.6;
    pointer-events: none;
}

.admin-table.loading tbody::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #011a2d;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>
", "components/admin_table.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\components\\admin_table.html.twig");
    }
}
