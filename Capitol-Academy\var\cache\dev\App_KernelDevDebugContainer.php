<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerXSTViyq\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerXSTViyq/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerXSTViyq.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerXSTViyq\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerXSTViyq\App_KernelDevDebugContainer([
    'container.build_hash' => 'XSTViyq',
    'container.build_id' => 'fc2aee48',
    'container.build_time' => 1752920159,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerXSTViyq');
