<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/instructor/show.html.twig */
class __TwigTemplate_6d345eec8644f30d0900f074e34ca826 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        // line 1
        yield from $this->load("admin/instructor/show.html.twig", 1, "1865317115")->unwrap()->yield(CoreExtension::merge($context, ["entity_name" => "Instructor", "entity_title" => CoreExtension::getAttribute($this->env, $this->source,         // line 3
(isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 3, $this->source); })()), "name", [], "any", false, false, false, 3), "entity_code" => CoreExtension::getAttribute($this->env, $this->source,         // line 4
(isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 4, $this->source); })()), "emailPrefix", [], "any", false, false, false, 4), "entity_icon" => "fas fa-chalkboard-teacher", "breadcrumb_items" => [["path" => "admin_dashboard", "title" => "Home"], ["path" => "admin_instructor_index", "title" => "Instructors"], ["title" => CoreExtension::getAttribute($this->env, $this->source,         // line 9
(isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 9, $this->source); })()), "name", [], "any", false, false, false, 9), "active" => true]], "back_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index"), "edit_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_edit", ["emailPrefix" => CoreExtension::getAttribute($this->env, $this->source,         // line 13
(isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 13, $this->source); })()), "emailPrefix", [], "any", false, false, false, 13)]), "print_function" => "printInstructorDetails"]));
        // line 206
        yield "
";
        // line 207
        yield from $this->unwrap()->yieldBlock('stylesheets', $context, $blocks);
        // line 223
        yield "
";
        // line 224
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    // line 207
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 208
        yield "<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    display: flex !important;
    align-items: center !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 224
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 225
        yield "<script>
// Print function for the preview layout
function printInstructorDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  126 => 225,  113 => 224,  88 => 208,  75 => 207,  64 => 224,  61 => 223,  59 => 207,  56 => 206,  54 => 13,  53 => 9,  52 => 4,  51 => 3,  50 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Instructor',
    'entity_title': instructor.name,
    'entity_code': instructor.emailPrefix,
    'entity_icon': 'fas fa-chalkboard-teacher',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_instructor_index', 'title': 'Instructors'},
        {'title': instructor.name, 'active': true}
    ],

    'back_path': path('admin_instructor_index'),
    'edit_path': path('admin_instructor_edit', {'emailPrefix': instructor.emailPrefix}),
    'print_function': 'printInstructorDetails'
} %}

{% block preview_content %}

    <!-- Profile Picture Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12 text-center\">
            <div class=\"profile-picture-display\" style=\"display: inline-block;\">
                {% if instructor.profileImage %}
                    <img src=\"{{ asset('uploads/instructors/' ~ instructor.profileImage) }}\"
                         alt=\"{{ instructor.name }}\"
                         class=\"rounded-circle shadow-lg\"
                         style=\"width: 150px; height: 150px; object-fit: cover; border: 4px solid #011a2d;\">
                {% else %}
                    <div class=\"rounded-circle shadow-lg d-flex align-items-center justify-content-center\"
                         style=\"width: 150px; height: 150px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-size: 3rem; font-weight: bold;\">
                        {{ instructor.name|split(' ')|first|first|upper }}{{ instructor.name|split(' ')|last|first|upper }}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Instructor Profile Information -->
    <div class=\"row\">
        <!-- Name and Email -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.name }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:{{ instructor.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ instructor.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Specialization (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-star text-primary mr-1\"></i>
            Specialization
        </label>
        <div class=\"enhanced-display-field\">
            {{ instructor.specialization ?? 'Not specified' }}
        </div>
    </div>

    <!-- Phone and Display Order Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-phone text-primary mr-1\"></i>
                    Phone
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.phone ?? 'Not provided' }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                    Display Order
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.displayOrder ?? 'Not set' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Biography -->
    {% if instructor.bio %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Biography
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            {{ instructor.bio|nl2br }}
        </div>
    </div>
    {% endif %}

    <!-- Qualifications -->
    {% if instructor.qualifications|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
            Qualifications
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for qualification in instructor.qualifications %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-certificate text-primary me-2\"></i>{{ qualification }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- Achievements -->
    {% if instructor.achievements|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-trophy text-primary mr-1\"></i>
            Achievements
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for achievement in instructor.achievements %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-award text-warning me-2\"></i>{{ achievement }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- LinkedIn URL -->
    {% if instructor.linkedinUrl %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
            LinkedIn Profile
        </label>
        <div class=\"enhanced-display-field\">
            <a href=\"{{ instructor.linkedinUrl }}\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                <i class=\"fas fa-external-link-alt me-2\"></i>View LinkedIn Profile
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Status and Created Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.isActive %}
                        <span class=\"badge bg-success\">Active</span>
                    {% else %}
                        <span class=\"badge bg-secondary\">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.createdAt %}
                        {{ instructor.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    display: flex !important;
    align-items: center !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printInstructorDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/instructor/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\show.html.twig");
    }
}


/* admin/instructor/show.html.twig */
class __TwigTemplate_6d345eec8644f30d0900f074e34ca826___1865317115 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'preview_content' => [$this, 'block_preview_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "components/admin_preview_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        $this->parent = $this->load("components/admin_preview_layout.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 17
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_preview_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        // line 18
        yield "
    <!-- Profile Picture Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12 text-center\">
            <div class=\"profile-picture-display\" style=\"display: inline-block;\">
                ";
        // line 23
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 23, $this->source); })()), "profileImage", [], "any", false, false, false, 23)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 24
            yield "                    <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/instructors/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 24, $this->source); })()), "profileImage", [], "any", false, false, false, 24))), "html", null, true);
            yield "\"
                         alt=\"";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 25, $this->source); })()), "name", [], "any", false, false, false, 25), "html", null, true);
            yield "\"
                         class=\"rounded-circle shadow-lg\"
                         style=\"width: 150px; height: 150px; object-fit: cover; border: 4px solid #011a2d;\">
                ";
        } else {
            // line 29
            yield "                    <div class=\"rounded-circle shadow-lg d-flex align-items-center justify-content-center\"
                         style=\"width: 150px; height: 150px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-size: 3rem; font-weight: bold;\">
                        ";
            // line 31
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), Twig\Extension\CoreExtension::split($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 31, $this->source); })()), "name", [], "any", false, false, false, 31), " ")))), "html", null, true);
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), Twig\Extension\CoreExtension::last($this->env->getCharset(), Twig\Extension\CoreExtension::split($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 31, $this->source); })()), "name", [], "any", false, false, false, 31), " ")))), "html", null, true);
            yield "
                    </div>
                ";
        }
        // line 34
        yield "            </div>
        </div>
    </div>

    <!-- Instructor Profile Information -->
    <div class=\"row\">
        <!-- Name and Email -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 48
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 48, $this->source); })()), "name", [], "any", false, false, false, 48), "html", null, true);
        yield "
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:";
        // line 60
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 60, $this->source); })()), "email", [], "any", false, false, false, 60), "html", null, true);
        yield "\" class=\"text-decoration-none\" style=\"color: #011a2d;\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 60, $this->source); })()), "email", [], "any", false, false, false, 60), "html", null, true);
        yield "</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Specialization (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-star text-primary mr-1\"></i>
            Specialization
        </label>
        <div class=\"enhanced-display-field\">
            ";
        // line 73
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["instructor"] ?? null), "specialization", [], "any", true, true, false, 73) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 73, $this->source); })()), "specialization", [], "any", false, false, false, 73)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 73, $this->source); })()), "specialization", [], "any", false, false, false, 73), "html", null, true)) : ("Not specified"));
        yield "
        </div>
    </div>

    <!-- Phone and Display Order Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-phone text-primary mr-1\"></i>
                    Phone
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 86
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["instructor"] ?? null), "phone", [], "any", true, true, false, 86) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 86, $this->source); })()), "phone", [], "any", false, false, false, 86)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 86, $this->source); })()), "phone", [], "any", false, false, false, 86), "html", null, true)) : ("Not provided"));
        yield "
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                    Display Order
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 98
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["instructor"] ?? null), "displayOrder", [], "any", true, true, false, 98) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 98, $this->source); })()), "displayOrder", [], "any", false, false, false, 98)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 98, $this->source); })()), "displayOrder", [], "any", false, false, false, 98), "html", null, true)) : ("Not set"));
        yield "
                </div>
            </div>
        </div>
    </div>

    <!-- Biography -->
    ";
        // line 105
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 105, $this->source); })()), "bio", [], "any", false, false, false, 105)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 106
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Biography
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            ";
            // line 112
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 112, $this->source); })()), "bio", [], "any", false, false, false, 112), "html", null, true));
            yield "
        </div>
    </div>
    ";
        }
        // line 116
        yield "
    <!-- Qualifications -->
    ";
        // line 118
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 118, $this->source); })()), "qualifications", [], "any", false, false, false, 118)) > 0)) {
            // line 119
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
            Qualifications
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                ";
            // line 126
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 126, $this->source); })()), "qualifications", [], "any", false, false, false, 126));
            foreach ($context['_seq'] as $context["_key"] => $context["qualification"]) {
                // line 127
                yield "                    <li class=\"mb-2\">
                        <i class=\"fas fa-certificate text-primary me-2\"></i>";
                // line 128
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["qualification"], "html", null, true);
                yield "
                    </li>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['qualification'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 131
            yield "            </ul>
        </div>
    </div>
    ";
        }
        // line 135
        yield "
    <!-- Achievements -->
    ";
        // line 137
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 137, $this->source); })()), "achievements", [], "any", false, false, false, 137)) > 0)) {
            // line 138
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-trophy text-primary mr-1\"></i>
            Achievements
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                ";
            // line 145
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 145, $this->source); })()), "achievements", [], "any", false, false, false, 145));
            foreach ($context['_seq'] as $context["_key"] => $context["achievement"]) {
                // line 146
                yield "                    <li class=\"mb-2\">
                        <i class=\"fas fa-award text-warning me-2\"></i>";
                // line 147
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["achievement"], "html", null, true);
                yield "
                    </li>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['achievement'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 150
            yield "            </ul>
        </div>
    </div>
    ";
        }
        // line 154
        yield "
    <!-- LinkedIn URL -->
    ";
        // line 156
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 156, $this->source); })()), "linkedinUrl", [], "any", false, false, false, 156)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 157
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
            LinkedIn Profile
        </label>
        <div class=\"enhanced-display-field\">
            <a href=\"";
            // line 163
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 163, $this->source); })()), "linkedinUrl", [], "any", false, false, false, 163), "html", null, true);
            yield "\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                <i class=\"fas fa-external-link-alt me-2\"></i>View LinkedIn Profile
            </a>
        </div>
    </div>
    ";
        }
        // line 169
        yield "
    <!-- Status and Created Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 179
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 179, $this->source); })()), "isActive", [], "any", false, false, false, 179)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 180
            yield "                        <span class=\"badge bg-success\">Active</span>
                    ";
        } else {
            // line 182
            yield "                        <span class=\"badge bg-secondary\">Inactive</span>
                    ";
        }
        // line 184
        yield "                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 195
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 195, $this->source); })()), "createdAt", [], "any", false, false, false, 195)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 196
            yield "                        ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 196, $this->source); })()), "createdAt", [], "any", false, false, false, 196), "F j, Y \\a\\t g:i A"), "html", null, true);
            yield "
                    ";
        } else {
            // line 198
            yield "                        Not available
                    ";
        }
        // line 200
        yield "                </div>
            </div>
        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  746 => 200,  742 => 198,  736 => 196,  734 => 195,  721 => 184,  717 => 182,  713 => 180,  711 => 179,  699 => 169,  690 => 163,  682 => 157,  680 => 156,  676 => 154,  670 => 150,  661 => 147,  658 => 146,  654 => 145,  645 => 138,  643 => 137,  639 => 135,  633 => 131,  624 => 128,  621 => 127,  617 => 126,  608 => 119,  606 => 118,  602 => 116,  595 => 112,  587 => 106,  585 => 105,  575 => 98,  560 => 86,  544 => 73,  526 => 60,  511 => 48,  495 => 34,  488 => 31,  484 => 29,  477 => 25,  472 => 24,  470 => 23,  463 => 18,  450 => 17,  427 => 1,  126 => 225,  113 => 224,  88 => 208,  75 => 207,  64 => 224,  61 => 223,  59 => 207,  56 => 206,  54 => 13,  53 => 9,  52 => 4,  51 => 3,  50 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Instructor',
    'entity_title': instructor.name,
    'entity_code': instructor.emailPrefix,
    'entity_icon': 'fas fa-chalkboard-teacher',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_instructor_index', 'title': 'Instructors'},
        {'title': instructor.name, 'active': true}
    ],

    'back_path': path('admin_instructor_index'),
    'edit_path': path('admin_instructor_edit', {'emailPrefix': instructor.emailPrefix}),
    'print_function': 'printInstructorDetails'
} %}

{% block preview_content %}

    <!-- Profile Picture Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12 text-center\">
            <div class=\"profile-picture-display\" style=\"display: inline-block;\">
                {% if instructor.profileImage %}
                    <img src=\"{{ asset('uploads/instructors/' ~ instructor.profileImage) }}\"
                         alt=\"{{ instructor.name }}\"
                         class=\"rounded-circle shadow-lg\"
                         style=\"width: 150px; height: 150px; object-fit: cover; border: 4px solid #011a2d;\">
                {% else %}
                    <div class=\"rounded-circle shadow-lg d-flex align-items-center justify-content-center\"
                         style=\"width: 150px; height: 150px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-size: 3rem; font-weight: bold;\">
                        {{ instructor.name|split(' ')|first|first|upper }}{{ instructor.name|split(' ')|last|first|upper }}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Instructor Profile Information -->
    <div class=\"row\">
        <!-- Name and Email -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.name }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:{{ instructor.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ instructor.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Specialization (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-star text-primary mr-1\"></i>
            Specialization
        </label>
        <div class=\"enhanced-display-field\">
            {{ instructor.specialization ?? 'Not specified' }}
        </div>
    </div>

    <!-- Phone and Display Order Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-phone text-primary mr-1\"></i>
                    Phone
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.phone ?? 'Not provided' }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                    Display Order
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.displayOrder ?? 'Not set' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Biography -->
    {% if instructor.bio %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Biography
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            {{ instructor.bio|nl2br }}
        </div>
    </div>
    {% endif %}

    <!-- Qualifications -->
    {% if instructor.qualifications|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
            Qualifications
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for qualification in instructor.qualifications %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-certificate text-primary me-2\"></i>{{ qualification }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- Achievements -->
    {% if instructor.achievements|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-trophy text-primary mr-1\"></i>
            Achievements
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for achievement in instructor.achievements %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-award text-warning me-2\"></i>{{ achievement }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- LinkedIn URL -->
    {% if instructor.linkedinUrl %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
            LinkedIn Profile
        </label>
        <div class=\"enhanced-display-field\">
            <a href=\"{{ instructor.linkedinUrl }}\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                <i class=\"fas fa-external-link-alt me-2\"></i>View LinkedIn Profile
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Status and Created Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.isActive %}
                        <span class=\"badge bg-success\">Active</span>
                    {% else %}
                        <span class=\"badge bg-secondary\">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.createdAt %}
                        {{ instructor.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    display: flex !important;
    align-items: center !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printInstructorDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/instructor/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\show.html.twig");
    }
}
