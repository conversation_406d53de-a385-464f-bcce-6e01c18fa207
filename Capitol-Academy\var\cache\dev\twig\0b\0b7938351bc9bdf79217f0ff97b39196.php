<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/plans/edit.html.twig */
class __TwigTemplate_b196b522275fd5dbfc03d798a028b08b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'stylesheets' => [$this, 'block_stylesheets'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Plan - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Plan: ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 5, $this->source); })()), "code", [], "any", false, false, false, 5), "html", null, true);
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\">Plans</a></li>
<li class=\"breadcrumb-item active\">Edit Plan</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-layer-group mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Plan: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 37, $this->source); })()), "code", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Plans Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"background: rgba(255, 255, 255, 0.2); color: white; border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 8px; padding: 0.75rem 1.5rem; font-weight: 500; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"
                           onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\">
                            <i class=\"fas fa-arrow-left mr-2\"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("plan_edit"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Plan Code and Title Row -->
                            <div class=\"row\">
                                <!-- Plan Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                            Plan Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"";
        // line 76
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 76, $this->source); })()), "code", [], "any", false, false, false, 76), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., PLAN001, VB200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid plan code (2-4 letters followed by 1-4 numbers).
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                            Plan Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"";
        // line 99
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 99, $this->source); })()), "title", [], "any", false, false, false, 99), "html", null, true);
        yield "\"
                                               placeholder=\"Enter plan title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a plan title.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Duration and Price Row -->
                            <div class=\"row\">
                                <!-- Duration -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock text-primary mr-1\"></i>
                                            Duration (Days) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"";
        // line 124
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 124, $this->source); })()), "duration", [], "any", false, false, false, 124), "html", null, true);
        yield "\"
                                               placeholder=\"30\"
                                               min=\"1\"
                                               max=\"365\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter a valid duration between 1 and 365 days.
                                        </div>
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               value=\"";
        // line 147
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 147, $this->source); })()), "price", [], "any", false, false, false, 147), "html", null, true);
        yield "\"
                                               placeholder=\"99.99\"
                                               min=\"0\"
                                               step=\"0.01\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Plan Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"4\"
                                          placeholder=\"Enter detailed plan description...\"
                                          required
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">";
        // line 172
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 172, $this->source); })()), "description", [], "any", false, false, false, 172), "html", null, true);
        yield "</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a plan description.
                                </div>
                            </div>

                            <!-- Video Selection -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-video text-primary mr-1\"></i>
                                    Select Videos <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"video-selection-container\">
                                    ";
        // line 186
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 186, $this->source); })()), "videos", [], "any", false, false, false, 186) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 186, $this->source); })()), "videos", [], "any", false, false, false, 186)) > 0))) {
            // line 187
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 187, $this->source); })()), "videos", [], "any", false, false, false, 187));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["selectedVideo"]) {
                // line 188
                yield "                                        <div class=\"input-group mb-2 video-selection-item\">
                                            <select class=\"form-control enhanced-field video-dropdown\"
                                                    name=\"videos[]\"
                                                    required
                                                    style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                                <option value=\"\">Select a video...</option>
                                                ";
                // line 194
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable((isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 194, $this->source); })()));
                foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
                    // line 195
                    yield "                                                    <option value=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 195), "html", null, true);
                    yield "\"
                                                            ";
                    // line 196
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 196) == CoreExtension::getAttribute($this->env, $this->source, $context["selectedVideo"], "id", [], "any", false, false, false, 196))) {
                        yield "selected";
                    }
                    // line 197
                    yield "                                                            data-category=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", true, true, false, 197)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 197), "General")) : ("General")), "html", null, true);
                    yield "\">
                                                        ";
                    // line 198
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 198), "html", null, true);
                    yield "
                                                        ";
                    // line 199
                    if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 199)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                        yield " - ";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 199), "html", null, true);
                    }
                    // line 200
                    yield "                                                        ";
                    if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isFree", [], "any", false, false, false, 200)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                        yield "(Free)";
                    } else {
                        yield "(\$";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "price", [], "any", false, false, false, 200), "html", null, true);
                        yield ")";
                    }
                    // line 201
                    yield "                                                    </option>
                                                ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 203
                yield "                                            </select>
                                            <div class=\"input-group-append\">
                                                ";
                // line 205
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 205)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 206
                    yield "                                                <button type=\"button\" class=\"btn btn-success add-video-selection\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                ";
                } else {
                    // line 210
                    yield "                                                <button type=\"button\" class=\"btn btn-danger remove-video-selection\">
                                                    <i class=\"fas fa-minus\"></i>
                                                </button>
                                                <button type=\"button\" class=\"btn btn-success add-video-selection\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                ";
                }
                // line 217
                yield "                                            </div>
                                        </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['selectedVideo'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 220
            yield "                                    ";
        } else {
            // line 221
            yield "                                        <div class=\"input-group mb-2 video-selection-item\">
                                            <select class=\"form-control enhanced-field video-dropdown\"
                                                    name=\"videos[]\"
                                                    required
                                                    style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                                <option value=\"\">Select a video...</option>
                                                ";
            // line 227
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 227, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
                // line 228
                yield "                                                    <option value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 228), "html", null, true);
                yield "\"
                                                            data-category=\"";
                // line 229
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", true, true, false, 229)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 229), "General")) : ("General")), "html", null, true);
                yield "\">
                                                        ";
                // line 230
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 230), "html", null, true);
                yield "
                                                        ";
                // line 231
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 231)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    yield " - ";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 231), "html", null, true);
                }
                // line 232
                yield "                                                        ";
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isFree", [], "any", false, false, false, 232)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    yield "(Free)";
                } else {
                    yield "(\$";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "price", [], "any", false, false, false, 232), "html", null, true);
                    yield ")";
                }
                // line 233
                yield "                                                    </option>
                                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 235
            yield "                                            </select>
                                            <div class=\"input-group-append\">
                                                <button type=\"button\" class=\"btn btn-success add-video-selection\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    ";
        }
        // line 243
        yield "                                </div>
                                <div class=\"invalid-feedback\">
                                    Please select at least one video.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Click the + button to add more video selections. Each video can only be selected once.
                                </small>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Plan
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 265
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 277
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 278
        yield "<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-field:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* Select2 Custom Styling - Force consistent height with other form fields */
.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
    padding-left: 8px !important;
    padding-right: 8px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.select2-container--bootstrap4 .select2-selection--multiple:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice {
    background-color: #1e3c72 !important;
    border: 1px solid #1e3c72 !important;
    color: white !important;
    border-radius: 4px !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove {
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove:hover {
    color: #ff6b6b !important;
}
</style>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<script>
\$(document).ready(function() {
    // Dynamic Video Selection Management
    \$(document).on('click', '.add-video-selection', function() {
        var container = \$('#video-selection-container');
        var newItem = `
            <div class=\"input-group mb-2 video-selection-item\">
                <select class=\"form-control enhanced-field video-dropdown\"
                        name=\"videos[]\"
                        required
                        style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                    <option value=\"\">Select a video...</option>
                    ";
        // line 376
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 376, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
            // line 377
            yield "                        <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 377), "html", null, true);
            yield "\"
                                data-category=\"";
            // line 378
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", true, true, false, 378)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 378), "General")) : ("General")), "html", null, true);
            yield "\">
                            ";
            // line 379
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 379), "html", null, true);
            yield "
                            ";
            // line 380
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 380)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield " - ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 380), "html", null, true);
            }
            // line 381
            yield "                            ";
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isFree", [], "any", false, false, false, 381)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield "(Free)";
            } else {
                yield "(\$";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "price", [], "any", false, false, false, 381), "html", null, true);
                yield ")";
            }
            // line 382
            yield "                        </option>
                    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 384
        yield "                </select>
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-video-selection\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-video-selection\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
        updateVideoOptions();
    });

    // Remove video selection
    \$(document).on('click', '.remove-video-selection', function() {
        \$(this).closest('.video-selection-item').remove();
        updateVideoOptions();
    });

    // Update video options when selection changes
    \$(document).on('change', '.video-dropdown', function() {
        updateVideoOptions();
    });

    // Function to disable already selected videos in other dropdowns
    function updateVideoOptions() {
        var selectedVideos = [];
        \$('.video-dropdown').each(function() {
            var value = \$(this).val();
            if (value) {
                selectedVideos.push(value);
            }
        });

        \$('.video-dropdown').each(function() {
            var currentValue = \$(this).val();
            \$(this).find('option').each(function() {
                var optionValue = \$(this).val();
                if (optionValue && selectedVideos.includes(optionValue) && optionValue !== currentValue) {
                    \$(this).prop('disabled', true);
                } else {
                    \$(this).prop('disabled', false);
                }
            });
        });
    }

    // Initialize video options on page load
    updateVideoOptions();

    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/plans/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  747 => 384,  740 => 382,  731 => 381,  726 => 380,  722 => 379,  718 => 378,  713 => 377,  709 => 376,  609 => 278,  596 => 277,  574 => 265,  550 => 243,  540 => 235,  533 => 233,  524 => 232,  519 => 231,  515 => 230,  511 => 229,  506 => 228,  502 => 227,  494 => 221,  491 => 220,  475 => 217,  466 => 210,  460 => 206,  458 => 205,  454 => 203,  447 => 201,  438 => 200,  433 => 199,  429 => 198,  424 => 197,  420 => 196,  415 => 195,  411 => 194,  403 => 188,  385 => 187,  383 => 186,  366 => 172,  338 => 147,  312 => 124,  284 => 99,  258 => 76,  236 => 57,  219 => 43,  210 => 37,  200 => 29,  190 => 25,  187 => 24,  183 => 23,  180 => 22,  170 => 18,  167 => 17,  163 => 16,  159 => 14,  146 => 13,  132 => 9,  127 => 8,  114 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Plan - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Plan: {{ plan.code }}{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_plans') }}\">Plans</a></li>
<li class=\"breadcrumb-item active\">Edit Plan</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-layer-group mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Plan: {{ plan.code }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Plans Button -->
                        <a href=\"{{ path('admin_plans') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"background: rgba(255, 255, 255, 0.2); color: white; border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 8px; padding: 0.75rem 1.5rem; font-weight: 500; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='rgba(255, 255, 255, 0.3)'\"
                           onmouseout=\"this.style.background='rgba(255, 255, 255, 0.2)'\">
                            <i class=\"fas fa-arrow-left mr-2\"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('plan_edit') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Plan Code and Title Row -->
                            <div class=\"row\">
                                <!-- Plan Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                            Plan Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"{{ plan.code }}\"
                                               placeholder=\"e.g., PLAN001, VB200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid plan code (2-4 letters followed by 1-4 numbers).
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                            Plan Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"{{ plan.title }}\"
                                               placeholder=\"Enter plan title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a plan title.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Duration and Price Row -->
                            <div class=\"row\">
                                <!-- Duration -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock text-primary mr-1\"></i>
                                            Duration (Days) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"{{ plan.duration }}\"
                                               placeholder=\"30\"
                                               min=\"1\"
                                               max=\"365\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter a valid duration between 1 and 365 days.
                                        </div>
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                            Price (USD) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               value=\"{{ plan.price }}\"
                                               placeholder=\"99.99\"
                                               min=\"0\"
                                               step=\"0.01\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Plan Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"4\"
                                          placeholder=\"Enter detailed plan description...\"
                                          required
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">{{ plan.description }}</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a plan description.
                                </div>
                            </div>

                            <!-- Video Selection -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-video text-primary mr-1\"></i>
                                    Select Videos <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"video-selection-container\">
                                    {% if plan.videos and plan.videos|length > 0 %}
                                        {% for selectedVideo in plan.videos %}
                                        <div class=\"input-group mb-2 video-selection-item\">
                                            <select class=\"form-control enhanced-field video-dropdown\"
                                                    name=\"videos[]\"
                                                    required
                                                    style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                                <option value=\"\">Select a video...</option>
                                                {% for video in videos %}
                                                    <option value=\"{{ video.id }}\"
                                                            {% if video.id == selectedVideo.id %}selected{% endif %}
                                                            data-category=\"{{ video.category|default('General') }}\">
                                                        {{ video.title }}
                                                        {% if video.category %} - {{ video.category }}{% endif %}
                                                        {% if video.isFree %}(Free){% else %}(\${{ video.price }}){% endif %}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                            <div class=\"input-group-append\">
                                                {% if loop.first %}
                                                <button type=\"button\" class=\"btn btn-success add-video-selection\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                {% else %}
                                                <button type=\"button\" class=\"btn btn-danger remove-video-selection\">
                                                    <i class=\"fas fa-minus\"></i>
                                                </button>
                                                <button type=\"button\" class=\"btn btn-success add-video-selection\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class=\"input-group mb-2 video-selection-item\">
                                            <select class=\"form-control enhanced-field video-dropdown\"
                                                    name=\"videos[]\"
                                                    required
                                                    style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                                <option value=\"\">Select a video...</option>
                                                {% for video in videos %}
                                                    <option value=\"{{ video.id }}\"
                                                            data-category=\"{{ video.category|default('General') }}\">
                                                        {{ video.title }}
                                                        {% if video.category %} - {{ video.category }}{% endif %}
                                                        {% if video.isFree %}(Free){% else %}(\${{ video.price }}){% endif %}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                            <div class=\"input-group-append\">
                                                <button type=\"button\" class=\"btn btn-success add-video-selection\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class=\"invalid-feedback\">
                                    Please select at least one video.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Click the + button to add more video selections. Each video can only be selected once.
                                </small>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Plan
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_plans') }}\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-field:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* Select2 Custom Styling - Force consistent height with other form fields */
.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
    padding-left: 8px !important;
    padding-right: 8px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.select2-container--bootstrap4 .select2-selection--multiple:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice {
    background-color: #1e3c72 !important;
    border: 1px solid #1e3c72 !important;
    color: white !important;
    border-radius: 4px !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove {
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove:hover {
    color: #ff6b6b !important;
}
</style>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<script>
\$(document).ready(function() {
    // Dynamic Video Selection Management
    \$(document).on('click', '.add-video-selection', function() {
        var container = \$('#video-selection-container');
        var newItem = `
            <div class=\"input-group mb-2 video-selection-item\">
                <select class=\"form-control enhanced-field video-dropdown\"
                        name=\"videos[]\"
                        required
                        style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                    <option value=\"\">Select a video...</option>
                    {% for video in videos %}
                        <option value=\"{{ video.id }}\"
                                data-category=\"{{ video.category|default('General') }}\">
                            {{ video.title }}
                            {% if video.category %} - {{ video.category }}{% endif %}
                            {% if video.isFree %}(Free){% else %}(\${{ video.price }}){% endif %}
                        </option>
                    {% endfor %}
                </select>
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-video-selection\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-video-selection\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
        updateVideoOptions();
    });

    // Remove video selection
    \$(document).on('click', '.remove-video-selection', function() {
        \$(this).closest('.video-selection-item').remove();
        updateVideoOptions();
    });

    // Update video options when selection changes
    \$(document).on('change', '.video-dropdown', function() {
        updateVideoOptions();
    });

    // Function to disable already selected videos in other dropdowns
    function updateVideoOptions() {
        var selectedVideos = [];
        \$('.video-dropdown').each(function() {
            var value = \$(this).val();
            if (value) {
                selectedVideos.push(value);
            }
        });

        \$('.video-dropdown').each(function() {
            var currentValue = \$(this).val();
            \$(this).find('option').each(function() {
                var optionValue = \$(this).val();
                if (optionValue && selectedVideos.includes(optionValue) && optionValue !== currentValue) {
                    \$(this).prop('disabled', true);
                } else {
                    \$(this).prop('disabled', false);
                }
            });
        });
    }

    // Initialize video options on page load
    updateVideoOptions();

    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
{% endblock %}
", "admin/plans/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\plans\\edit.html.twig");
    }
}
