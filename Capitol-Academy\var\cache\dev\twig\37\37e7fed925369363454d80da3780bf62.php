<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/dashboard.html.twig */
class __TwigTemplate_41a2b05be6b191a56339f5b6aa2eb46b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/dashboard.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/dashboard.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Dashboard - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Dashboard";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item active\">Dashboard</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 11
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 12
        yield "<!-- Professional Header Section -->
<div class=\"row mb-4\">
    <div class=\"col-12\">
        <div class=\"card shadow-sm border-0\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
            <div class=\"card-body py-4\">
                <div class=\"row align-items-center\">
                    <div class=\"col-lg-8\">
                        <h1 class=\"h3 mb-2 fw-bold\">
                            <i class=\"fas fa-tachometer-alt me-3\"></i>Capitol Academy Dashboard
                        </h1>
                        <p class=\"mb-0 opacity-75\">Welcome to your administrative control center</p>
                    </div>
                    <div class=\"col-lg-4 text-lg-end\">
                        <div class=\"d-flex flex-wrap gap-2 justify-content-lg-end\">
                            <span class=\"badge bg-light text-dark px-3 py-2 fs-6\">
                                <i class=\"fas fa-calendar me-1\"></i>";
        // line 27
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "M d, Y"), "html", null, true);
        yield "
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class=\"row mb-4\">
    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\">
                        <i class=\"fas fa-users text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-primary\">";
        // line 48
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 48, $this->source); })()), "total_users", [], "any", false, false, false, 48), "html", null, true);
        yield "</h3>
                <p class=\"text-muted mb-2\">Total Users</p>
                <small class=\"text-success\">
                    <i class=\"fas fa-arrow-up me-1\"></i>";
        // line 51
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 51, $this->source); })()), "new_users_this_month", [], "any", false, false, false, 51), "html", null, true);
        yield " this month
                </small>
            </div>
        </div>
    </div>

    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\">
                        <i class=\"fas fa-graduation-cap text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-success\">";
        // line 66
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 66, $this->source); })()), "active_courses", [], "any", false, false, false, 66), "html", null, true);
        yield "</h3>
                <p class=\"text-muted mb-2\">Active Courses</p>
                <small class=\"text-info\">
                    <i class=\"fas fa-book me-1\"></i>";
        // line 69
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 69, $this->source); })()), "total_courses", [], "any", false, false, false, 69), "html", null, true);
        yield " total
                </small>
            </div>
        </div>
    </div>

    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);\">
                        <i class=\"fas fa-envelope text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-danger\">";
        // line 84
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 84, $this->source); })()), "unprocessed_contacts", [], "any", false, false, false, 84), "html", null, true);
        yield "</h3>
                <p class=\"text-muted mb-2\">Pending Contacts</p>
                <small class=\"text-warning\">
                    <i class=\"fas fa-clock me-1\"></i>";
        // line 87
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 87, $this->source); })()), "total_contacts", [], "any", false, false, false, 87), "html", null, true);
        yield " total
                </small>
            </div>
        </div>
    </div>

    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);\">
                        <i class=\"fas fa-chart-line text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-warning\">";
        // line 102
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::round(((CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 102, $this->source); })()), "new_users_this_month", [], "any", false, false, false, 102) / CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 102, $this->source); })()), "total_users", [], "any", false, false, false, 102)) * 100), 1), "html", null, true);
        yield "%</h3>
                <p class=\"text-muted mb-2\">Growth Rate</p>
                <small class=\"text-primary\">
                    <i class=\"fas fa-trending-up me-1\"></i>This month
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class=\"row mb-4\">
    <div class=\"col-lg-8 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-chart-line me-2 text-primary\"></i>User Growth Trend
                </h5>
            </div>
            <div class=\"card-body\">
                <div style=\"height: 300px;\">
                    <canvas id=\"userGrowthChart\"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class=\"col-lg-4 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-chart-pie me-2 text-success\"></i>Course Distribution
                </h5>
            </div>
            <div class=\"card-body\">
                <div style=\"height: 300px;\">
                    <canvas id=\"courseDistributionChart\"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Section -->
<div class=\"row\">
    <div class=\"col-lg-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-user-plus me-2 text-primary\"></i>Recent Users
                </h5>
            </div>
            <div class=\"card-body\">
                ";
        // line 155
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["recent_users"]) || array_key_exists("recent_users", $context) ? $context["recent_users"] : (function () { throw new RuntimeError('Variable "recent_users" does not exist.', 155, $this->source); })())) > 0)) {
            // line 156
            yield "                    <div class=\"list-group list-group-flush\">
                        ";
            // line 157
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["recent_users"]) || array_key_exists("recent_users", $context) ? $context["recent_users"] : (function () { throw new RuntimeError('Variable "recent_users" does not exist.', 157, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["user"]) {
                // line 158
                yield "                            <div class=\"list-group-item border-0 px-0 py-3\">
                                <div class=\"d-flex align-items-center\">
                                    <div class=\"rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3\"
                                         style=\"width: 40px; height: 40px; font-size: 14px; font-weight: bold;\">
                                        ";
                // line 162
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["user"], "firstName", [], "any", false, false, false, 162))), "html", null, true);
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["user"], "lastName", [], "any", false, false, false, 162))), "html", null, true);
                yield "
                                    </div>
                                    <div class=\"flex-grow-1\">
                                        <h6 class=\"mb-1 fw-bold\">";
                // line 165
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["user"], "firstName", [], "any", false, false, false, 165), "html", null, true);
                yield " ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["user"], "lastName", [], "any", false, false, false, 165), "html", null, true);
                yield "</h6>
                                        <small class=\"text-muted\">";
                // line 166
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["user"], "email", [], "any", false, false, false, 166), "html", null, true);
                yield "</small>
                                    </div>
                                    <small class=\"text-muted\">";
                // line 168
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["user"], "createdAt", [], "any", false, false, false, 168), "M d"), "html", null, true);
                yield "</small>
                                </div>
                            </div>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['user'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 172
            yield "                    </div>
                ";
        } else {
            // line 174
            yield "                    <div class=\"text-center py-4\">
                        <i class=\"fas fa-users text-muted fs-1 mb-3\"></i>
                        <p class=\"text-muted\">No recent users</p>
                    </div>
                ";
        }
        // line 179
        yield "            </div>
        </div>
    </div>

    <div class=\"col-lg-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-envelope me-2 text-danger\"></i>Recent Contacts
                </h5>
            </div>
            <div class=\"card-body\">
                ";
        // line 191
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["recent_contacts"]) || array_key_exists("recent_contacts", $context) ? $context["recent_contacts"] : (function () { throw new RuntimeError('Variable "recent_contacts" does not exist.', 191, $this->source); })())) > 0)) {
            // line 192
            yield "                    <div class=\"list-group list-group-flush\">
                        ";
            // line 193
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["recent_contacts"]) || array_key_exists("recent_contacts", $context) ? $context["recent_contacts"] : (function () { throw new RuntimeError('Variable "recent_contacts" does not exist.', 193, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["contact"]) {
                // line 194
                yield "                            <div class=\"list-group-item border-0 px-0 py-3\">
                                <div class=\"d-flex align-items-center\">
                                    <div class=\"rounded-circle bg-danger text-white d-flex align-items-center justify-content-center me-3\"
                                         style=\"width: 40px; height: 40px;\">
                                        <i class=\"fas fa-envelope fs-6\"></i>
                                    </div>
                                    <div class=\"flex-grow-1\">
                                        <h6 class=\"mb-1 fw-bold\">";
                // line 201
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "fullName", [], "any", false, false, false, 201), "html", null, true);
                yield "</h6>
                                        <small class=\"text-muted\">";
                // line 202
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "email", [], "any", false, false, false, 202), "html", null, true);
                yield "</small>
                                    </div>
                                    <div class=\"text-end\">
                                        ";
                // line 205
                if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "isProcessed", [], "any", false, false, false, 205)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 206
                    yield "                                            <span class=\"badge bg-warning text-dark\">Pending</span>
                                        ";
                } else {
                    // line 208
                    yield "                                            <span class=\"badge bg-success\">Processed</span>
                                        ";
                }
                // line 210
                yield "                                        <br><small class=\"text-muted\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "createdAt", [], "any", false, false, false, 210), "M d"), "html", null, true);
                yield "</small>
                                    </div>
                                </div>
                            </div>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['contact'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 215
            yield "                    </div>
                ";
        } else {
            // line 217
            yield "                    <div class=\"text-center py-4\">
                        <i class=\"fas fa-envelope text-muted fs-1 mb-3\"></i>
                        <p class=\"text-muted\">No recent contacts</p>
                    </div>
                ";
        }
        // line 222
        yield "            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class=\"col-lg-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-0 pb-0\">
                <h5 class=\"fw-bold text-dark mb-0\">
                    <i class=\"fas fa-credit-card text-success me-2\"></i>Recent Payments
                </h5>
            </div>
            <div class=\"card-body pt-3\">
                ";
        // line 235
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["recent_payments"]) || array_key_exists("recent_payments", $context) ? $context["recent_payments"] : (function () { throw new RuntimeError('Variable "recent_payments" does not exist.', 235, $this->source); })())) > 0)) {
            // line 236
            yield "                    <div class=\"list-group list-group-flush\">
                        ";
            // line 237
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["recent_payments"]) || array_key_exists("recent_payments", $context) ? $context["recent_payments"] : (function () { throw new RuntimeError('Variable "recent_payments" does not exist.', 237, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["payment"]) {
                // line 238
                yield "                            <div class=\"list-group-item border-0 px-0\">
                                <div class=\"d-flex align-items-center\">
                                    <div class=\"payment-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3\" style=\"width: 40px; height: 40px;\">
                                        <i class=\"fas fa-dollar-sign\"></i>
                                    </div>
                                    <div class=\"flex-grow-1\">
                                        <h6 class=\"mb-1 fw-bold\">";
                // line 244
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["payment"], "user", [], "any", false, false, false, 244), "firstName", [], "any", false, false, false, 244), "html", null, true);
                yield " ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["payment"], "user", [], "any", false, false, false, 244), "lastName", [], "any", false, false, false, 244), "html", null, true);
                yield "</h6>
                                        <small class=\"text-muted\">";
                // line 245
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["payment"], "course", [], "any", false, false, false, 245), "title", [], "any", false, false, false, 245), "html", null, true);
                yield " (";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["payment"], "course", [], "any", false, false, false, 245), "code", [], "any", false, false, false, 245), "html", null, true);
                yield ")</small>
                                    </div>
                                    <div class=\"text-end\">
                                        <span class=\"badge bg-success\">\$";
                // line 248
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, $context["payment"], "amount", [], "any", false, false, false, 248), 2), "html", null, true);
                yield "</span>
                                        <br><small class=\"text-muted\">";
                // line 249
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["payment"], "createdAt", [], "any", false, false, false, 249), "M d"), "html", null, true);
                yield "</small>
                                    </div>
                                </div>
                            </div>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['payment'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 254
            yield "                    </div>
                ";
        } else {
            // line 256
            yield "                    <div class=\"text-center py-4\">
                        <i class=\"fas fa-credit-card text-muted fs-1 mb-3\"></i>
                        <p class=\"text-muted\">No recent payments</p>
                    </div>
                ";
        }
        // line 261
        yield "            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 267
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 268
        yield "<!-- Chart.js -->
<script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>

<script>
\$(document).ready(function() {
    // User Growth Chart
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'New Users',
                data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
                borderColor: '#1e3c72',
                backgroundColor: 'rgba(30, 60, 114, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#1e3c72',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });

    // Course Distribution Chart
    const courseDistributionCtx = document.getElementById('courseDistributionChart').getContext('2d');
    new Chart(courseDistributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active Courses', 'Inactive Courses', 'Draft Courses'],
            datasets: [{
                data: [";
        // line 335
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 335, $this->source); })()), "active_courses", [], "any", false, false, false, 335), "html", null, true);
        yield ", ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 335, $this->source); })()), "total_courses", [], "any", false, false, false, 335) - CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 335, $this->source); })()), "active_courses", [], "any", false, false, false, 335)), "html", null, true);
        yield ", 3],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107'
                ],
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        color: '#6c757d'
                    }
                }
            },
            cutout: '60%'
        }
    });

    // Interactive Map Simulation
    \$('#worldMap').on('click', function() {
        showAdminNotification('Interactive map feature coming soon!', 'info');
    });

    // Add hover effects to statistics cards
    \$('.card').hover(
        function() {
            \$(this).css('transform', 'translateY(-5px)');
        },
        function() {
            \$(this).css('transform', 'translateY(0)');
        }
    );

    // Real-time clock
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString();

        if (\$('#real-time-clock').length === 0) {
            \$('.breadcrumb').append(`
                <li class=\"breadcrumb-item active\" id=\"real-time-clock\">
                    <i class=\"fas fa-clock me-1\"></i>
                    \${timeString} - \${dateString}
                </li>
            `);
        } else {
            \$('#real-time-clock').html(`
                <i class=\"fas fa-clock me-1\"></i>
                \${timeString} - \${dateString}
            `);
        }
    }

    // Update clock every second
    updateClock();
    setInterval(updateClock, 1000);

    console.log('Capitol Academy Professional Dashboard Loaded');
});
</script>

<style>
/* Enhanced Dashboard Styles */
.card {
    transition: all 0.3s ease;
    border: none !important;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

.btn {
    transition: all 0.3s ease;
    border: none;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.badge {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Animation for statistics cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* Professional scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/dashboard.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  618 => 335,  549 => 268,  536 => 267,  521 => 261,  514 => 256,  510 => 254,  499 => 249,  495 => 248,  487 => 245,  481 => 244,  473 => 238,  469 => 237,  466 => 236,  464 => 235,  449 => 222,  442 => 217,  438 => 215,  426 => 210,  422 => 208,  418 => 206,  416 => 205,  410 => 202,  406 => 201,  397 => 194,  393 => 193,  390 => 192,  388 => 191,  374 => 179,  367 => 174,  363 => 172,  353 => 168,  348 => 166,  342 => 165,  335 => 162,  329 => 158,  325 => 157,  322 => 156,  320 => 155,  264 => 102,  246 => 87,  240 => 84,  222 => 69,  216 => 66,  198 => 51,  192 => 48,  168 => 27,  151 => 12,  138 => 11,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Dashboard - Capitol Academy Admin{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item active\">Dashboard</li>
{% endblock %}

{% block content %}
<!-- Professional Header Section -->
<div class=\"row mb-4\">
    <div class=\"col-12\">
        <div class=\"card shadow-sm border-0\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
            <div class=\"card-body py-4\">
                <div class=\"row align-items-center\">
                    <div class=\"col-lg-8\">
                        <h1 class=\"h3 mb-2 fw-bold\">
                            <i class=\"fas fa-tachometer-alt me-3\"></i>Capitol Academy Dashboard
                        </h1>
                        <p class=\"mb-0 opacity-75\">Welcome to your administrative control center</p>
                    </div>
                    <div class=\"col-lg-4 text-lg-end\">
                        <div class=\"d-flex flex-wrap gap-2 justify-content-lg-end\">
                            <span class=\"badge bg-light text-dark px-3 py-2 fs-6\">
                                <i class=\"fas fa-calendar me-1\"></i>{{ \"now\"|date(\"M d, Y\") }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class=\"row mb-4\">
    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\">
                        <i class=\"fas fa-users text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-primary\">{{ stats.total_users }}</h3>
                <p class=\"text-muted mb-2\">Total Users</p>
                <small class=\"text-success\">
                    <i class=\"fas fa-arrow-up me-1\"></i>{{ stats.new_users_this_month }} this month
                </small>
            </div>
        </div>
    </div>

    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\">
                        <i class=\"fas fa-graduation-cap text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-success\">{{ stats.active_courses }}</h3>
                <p class=\"text-muted mb-2\">Active Courses</p>
                <small class=\"text-info\">
                    <i class=\"fas fa-book me-1\"></i>{{ stats.total_courses }} total
                </small>
            </div>
        </div>
    </div>

    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);\">
                        <i class=\"fas fa-envelope text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-danger\">{{ stats.unprocessed_contacts }}</h3>
                <p class=\"text-muted mb-2\">Pending Contacts</p>
                <small class=\"text-warning\">
                    <i class=\"fas fa-clock me-1\"></i>{{ stats.total_contacts }} total
                </small>
            </div>
        </div>
    </div>

    <div class=\"col-lg-3 col-md-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-body text-center\">
                <div class=\"d-flex align-items-center justify-content-center mb-3\">
                    <div class=\"rounded-circle d-flex align-items-center justify-content-center\"
                         style=\"width: 60px; height: 60px; background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);\">
                        <i class=\"fas fa-chart-line text-white fs-4\"></i>
                    </div>
                </div>
                <h3 class=\"fw-bold text-warning\">{{ ((stats.new_users_this_month / stats.total_users) * 100)|round(1) }}%</h3>
                <p class=\"text-muted mb-2\">Growth Rate</p>
                <small class=\"text-primary\">
                    <i class=\"fas fa-trending-up me-1\"></i>This month
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class=\"row mb-4\">
    <div class=\"col-lg-8 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-chart-line me-2 text-primary\"></i>User Growth Trend
                </h5>
            </div>
            <div class=\"card-body\">
                <div style=\"height: 300px;\">
                    <canvas id=\"userGrowthChart\"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class=\"col-lg-4 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-chart-pie me-2 text-success\"></i>Course Distribution
                </h5>
            </div>
            <div class=\"card-body\">
                <div style=\"height: 300px;\">
                    <canvas id=\"courseDistributionChart\"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Section -->
<div class=\"row\">
    <div class=\"col-lg-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-user-plus me-2 text-primary\"></i>Recent Users
                </h5>
            </div>
            <div class=\"card-body\">
                {% if recent_users|length > 0 %}
                    <div class=\"list-group list-group-flush\">
                        {% for user in recent_users %}
                            <div class=\"list-group-item border-0 px-0 py-3\">
                                <div class=\"d-flex align-items-center\">
                                    <div class=\"rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3\"
                                         style=\"width: 40px; height: 40px; font-size: 14px; font-weight: bold;\">
                                        {{ user.firstName|first|upper }}{{ user.lastName|first|upper }}
                                    </div>
                                    <div class=\"flex-grow-1\">
                                        <h6 class=\"mb-1 fw-bold\">{{ user.firstName }} {{ user.lastName }}</h6>
                                        <small class=\"text-muted\">{{ user.email }}</small>
                                    </div>
                                    <small class=\"text-muted\">{{ user.createdAt|date('M d') }}</small>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class=\"text-center py-4\">
                        <i class=\"fas fa-users text-muted fs-1 mb-3\"></i>
                        <p class=\"text-muted\">No recent users</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class=\"col-lg-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-envelope me-2 text-danger\"></i>Recent Contacts
                </h5>
            </div>
            <div class=\"card-body\">
                {% if recent_contacts|length > 0 %}
                    <div class=\"list-group list-group-flush\">
                        {% for contact in recent_contacts %}
                            <div class=\"list-group-item border-0 px-0 py-3\">
                                <div class=\"d-flex align-items-center\">
                                    <div class=\"rounded-circle bg-danger text-white d-flex align-items-center justify-content-center me-3\"
                                         style=\"width: 40px; height: 40px;\">
                                        <i class=\"fas fa-envelope fs-6\"></i>
                                    </div>
                                    <div class=\"flex-grow-1\">
                                        <h6 class=\"mb-1 fw-bold\">{{ contact.fullName }}</h6>
                                        <small class=\"text-muted\">{{ contact.email }}</small>
                                    </div>
                                    <div class=\"text-end\">
                                        {% if not contact.isProcessed %}
                                            <span class=\"badge bg-warning text-dark\">Pending</span>
                                        {% else %}
                                            <span class=\"badge bg-success\">Processed</span>
                                        {% endif %}
                                        <br><small class=\"text-muted\">{{ contact.createdAt|date('M d') }}</small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class=\"text-center py-4\">
                        <i class=\"fas fa-envelope text-muted fs-1 mb-3\"></i>
                        <p class=\"text-muted\">No recent contacts</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class=\"col-lg-6 mb-4\">
        <div class=\"card border-0 shadow-sm h-100\">
            <div class=\"card-header bg-white border-0 pb-0\">
                <h5 class=\"fw-bold text-dark mb-0\">
                    <i class=\"fas fa-credit-card text-success me-2\"></i>Recent Payments
                </h5>
            </div>
            <div class=\"card-body pt-3\">
                {% if recent_payments|length > 0 %}
                    <div class=\"list-group list-group-flush\">
                        {% for payment in recent_payments %}
                            <div class=\"list-group-item border-0 px-0\">
                                <div class=\"d-flex align-items-center\">
                                    <div class=\"payment-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3\" style=\"width: 40px; height: 40px;\">
                                        <i class=\"fas fa-dollar-sign\"></i>
                                    </div>
                                    <div class=\"flex-grow-1\">
                                        <h6 class=\"mb-1 fw-bold\">{{ payment.user.firstName }} {{ payment.user.lastName }}</h6>
                                        <small class=\"text-muted\">{{ payment.course.title }} ({{ payment.course.code }})</small>
                                    </div>
                                    <div class=\"text-end\">
                                        <span class=\"badge bg-success\">\${{ payment.amount|number_format(2) }}</span>
                                        <br><small class=\"text-muted\">{{ payment.createdAt|date('M d') }}</small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class=\"text-center py-4\">
                        <i class=\"fas fa-credit-card text-muted fs-1 mb-3\"></i>
                        <p class=\"text-muted\">No recent payments</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<!-- Chart.js -->
<script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>

<script>
\$(document).ready(function() {
    // User Growth Chart
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'New Users',
                data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
                borderColor: '#1e3c72',
                backgroundColor: 'rgba(30, 60, 114, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#1e3c72',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });

    // Course Distribution Chart
    const courseDistributionCtx = document.getElementById('courseDistributionChart').getContext('2d');
    new Chart(courseDistributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active Courses', 'Inactive Courses', 'Draft Courses'],
            datasets: [{
                data: [{{ stats.active_courses }}, {{ stats.total_courses - stats.active_courses }}, 3],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107'
                ],
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        color: '#6c757d'
                    }
                }
            },
            cutout: '60%'
        }
    });

    // Interactive Map Simulation
    \$('#worldMap').on('click', function() {
        showAdminNotification('Interactive map feature coming soon!', 'info');
    });

    // Add hover effects to statistics cards
    \$('.card').hover(
        function() {
            \$(this).css('transform', 'translateY(-5px)');
        },
        function() {
            \$(this).css('transform', 'translateY(0)');
        }
    );

    // Real-time clock
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString();

        if (\$('#real-time-clock').length === 0) {
            \$('.breadcrumb').append(`
                <li class=\"breadcrumb-item active\" id=\"real-time-clock\">
                    <i class=\"fas fa-clock me-1\"></i>
                    \${timeString} - \${dateString}
                </li>
            `);
        } else {
            \$('#real-time-clock').html(`
                <i class=\"fas fa-clock me-1\"></i>
                \${timeString} - \${dateString}
            `);
        }
    }

    // Update clock every second
    updateClock();
    setInterval(updateClock, 1000);

    console.log('Capitol Academy Professional Dashboard Loaded');
});
</script>

<style>
/* Enhanced Dashboard Styles */
.card {
    transition: all 0.3s ease;
    border: none !important;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

.btn {
    transition: all 0.3s ease;
    border: none;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.badge {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Animation for statistics cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* Professional scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
}
</style>
{% endblock %}
", "admin/dashboard.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\dashboard.html.twig");
    }
}
