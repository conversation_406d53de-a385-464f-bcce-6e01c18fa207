<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/admins/edit.html.twig */
class __TwigTemplate_02ac5e951563083c7f7ca8d939a0bff6 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Administrator - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Administrator";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admins");
        yield "\">Administrators</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admin_view", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 9, $this->source); })()), "id", [], "any", false, false, false, 9)]), "html", null, true);
        yield "\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 9, $this->source); })()), "fullName", [], "any", false, false, false, 9), "html", null, true);
        yield "</a></li>
<li class=\"breadcrumb-item active\">Edit</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<!-- Professional Header Section -->
<div class=\"row mb-4\">
    <div class=\"col-12\">
        <div class=\"card shadow-sm border-0\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
            <div class=\"card-body py-4\">
                <div class=\"row align-items-center\">
                    <div class=\"col-lg-8\">
                        <div class=\"d-flex align-items-center\">
                            ";
        // line 22
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 22, $this->source); })()), "profileImage", [], "any", false, false, false, 22)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 23
            yield "                                <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 23, $this->source); })()), "profileImage", [], "any", false, false, false, 23))), "html", null, true);
            yield "\" 
                                     alt=\"";
            // line 24
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 24, $this->source); })()), "fullName", [], "any", false, false, false, 24), "html", null, true);
            yield "\" 
                                     class=\"rounded-circle me-4\"
                                     style=\"width: 80px; height: 80px; object-fit: cover; border: 3px solid rgba(255,255,255,0.3);\">
                            ";
        } else {
            // line 28
            yield "                                <div class=\"rounded-circle me-4 d-flex align-items-center justify-content-center\"
                                     style=\"width: 80px; height: 80px; background: rgba(255,255,255,0.2); border: 3px solid rgba(255,255,255,0.3);\">
                                    <span class=\"fs-2 fw-bold\">";
            // line 30
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 30, $this->source); })()), "fullName", [], "any", false, false, false, 30), 0, 1)), "html", null, true);
            yield "</span>
                                </div>
                            ";
        }
        // line 33
        yield "                            <div>
                                <h1 class=\"h3 mb-2 fw-bold\">
                                    <i class=\"fas fa-edit me-3\"></i>Edit Administrator
                                </h1>
                                <p class=\"mb-0 opacity-90\">
                                    Modify settings and permissions for ";
        // line 38
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 38, $this->source); })()), "fullName", [], "any", false, false, false, 38), "html", null, true);
        yield "
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class=\"col-lg-4 text-lg-end\">
                        <div class=\"d-flex flex-wrap gap-2 justify-content-lg-end\">
                            <a href=\"";
        // line 45
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admin_view", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 45, $this->source); })()), "id", [], "any", false, false, false, 45)]), "html", null, true);
        yield "\" class=\"btn btn-outline-light btn-lg\">
                                <i class=\"fas fa-eye me-2\"></i>View Details
                            </a>
                            <a href=\"";
        // line 48
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admins");
        yield "\" class=\"btn btn-light btn-lg\">
                                <i class=\"fas fa-arrow-left me-2\"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Form -->
<div class=\"row\">
    <div class=\"col-12\">
        <div class=\"card border-0 shadow-sm\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-cog me-2 text-primary\"></i>Administrator Settings
                </h5>
            </div>
            <div class=\"card-body\">
                ";
        // line 69
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 69, $this->source); })()), 'form_start', ["attr" => ["class" => "needs-validation", "novalidate" => true]]);
        yield "
                
                <div class=\"row\">
                    <!-- Basic Information -->
                    <div class=\"col-lg-6\">
                        <div class=\"card border-0 bg-light mb-4\">
                            <div class=\"card-header bg-primary text-white\">
                                <h6 class=\"mb-0 fw-bold\">
                                    <i class=\"fas fa-user me-2\"></i>Basic Information
                                </h6>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"row\">
                                    <div class=\"col-md-6 mb-3\">
                                        ";
        // line 83
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 83, $this->source); })()), "firstName", [], "any", false, false, false, 83), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
        yield "
                                        ";
        // line 84
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 84, $this->source); })()), "firstName", [], "any", false, false, false, 84), 'widget', ["attr" => ["class" => "form-control"]]);
        yield "
                                        ";
        // line 85
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 85, $this->source); })()), "firstName", [], "any", false, false, false, 85), 'errors');
        yield "
                                    </div>
                                    <div class=\"col-md-6 mb-3\">
                                        ";
        // line 88
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 88, $this->source); })()), "lastName", [], "any", false, false, false, 88), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
        yield "
                                        ";
        // line 89
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 89, $this->source); })()), "lastName", [], "any", false, false, false, 89), 'widget', ["attr" => ["class" => "form-control"]]);
        yield "
                                        ";
        // line 90
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 90, $this->source); })()), "lastName", [], "any", false, false, false, 90), 'errors');
        yield "
                                    </div>
                                </div>
                                
                                <div class=\"mb-3\">
                                    ";
        // line 95
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 95, $this->source); })()), "username", [], "any", false, false, false, 95), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
        yield "
                                    ";
        // line 96
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 96, $this->source); })()), "username", [], "any", false, false, false, 96), 'widget');
        yield "
                                    ";
        // line 97
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 97, $this->source); })()), "username", [], "any", false, false, false, 97), 'errors');
        yield "
                                </div>
                                
                                <div class=\"mb-3\">
                                    ";
        // line 101
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 101, $this->source); })()), "email", [], "any", false, false, false, 101), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
        yield "
                                    ";
        // line 102
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 102, $this->source); })()), "email", [], "any", false, false, false, 102), 'widget');
        yield "
                                    ";
        // line 103
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 103, $this->source); })()), "email", [], "any", false, false, false, 103), 'errors');
        yield "
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Settings -->
                    <div class=\"col-lg-6\">
                        <div class=\"card border-0 bg-light mb-4\">
                            <div class=\"card-header bg-success text-white\">
                                <h6 class=\"mb-0 fw-bold\">
                                    <i class=\"fas fa-cog me-2\"></i>Account Settings
                                </h6>
                            </div>
                            <div class=\"card-body\">
                                ";
        // line 118
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["form"] ?? null), "profileImageFile", [], "any", true, true, false, 118)) {
            // line 119
            yield "                                    <div class=\"mb-3\">
                                        ";
            // line 120
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 120, $this->source); })()), "profileImageFile", [], "any", false, false, false, 120), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
            yield "
                                        ";
            // line 121
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 121, $this->source); })()), "profileImageFile", [], "any", false, false, false, 121), 'widget');
            yield "
                                        ";
            // line 122
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 122, $this->source); })()), "profileImageFile", [], "any", false, false, false, 122), 'errors');
            yield "
                                        ";
            // line 123
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 123, $this->source); })()), "profileImage", [], "any", false, false, false, 123)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 124
                yield "                                            <div class=\"mt-2\">
                                                <small class=\"text-muted\">Current image:</small>
                                                <img src=\"";
                // line 126
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 126, $this->source); })()), "profileImage", [], "any", false, false, false, 126))), "html", null, true);
                yield "\" 
                                                     alt=\"Current profile\" 
                                                     class=\"img-thumbnail ms-2\" 
                                                     style=\"max-height: 50px;\">
                                            </div>
                                        ";
            }
            // line 132
            yield "                                    </div>
                                ";
        }
        // line 134
        yield "                                
                                ";
        // line 135
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["form"] ?? null), "isActive", [], "any", true, true, false, 135)) {
            // line 136
            yield "                                    <div class=\"mb-3\">
                                        <div class=\"form-check form-switch\">
                                            ";
            // line 138
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 138, $this->source); })()), "isActive", [], "any", false, false, false, 138), 'widget');
            yield "
                                            ";
            // line 139
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 139, $this->source); })()), "isActive", [], "any", false, false, false, 139), 'label', ["label_attr" => ["class" => "form-check-label fw-bold"]]);
            yield "
                                        </div>
                                        ";
            // line 141
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 141, $this->source); })()), "isActive", [], "any", false, false, false, 141), 'errors');
            yield "
                                    </div>
                                ";
        }
        // line 144
        yield "                                
                                ";
        // line 145
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["form"] ?? null), "plainPassword", [], "any", true, true, false, 145)) {
            // line 146
            yield "                                    <div class=\"mb-3\">
                                        ";
            // line 147
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 147, $this->source); })()), "plainPassword", [], "any", false, false, false, 147), 'label', ["label_attr" => ["class" => "form-label fw-bold"], "label" => "New Password (leave blank to keep current)"]);
            yield "
                                        ";
            // line 148
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 148, $this->source); })()), "plainPassword", [], "any", false, false, false, 148), 'widget');
            yield "
                                        ";
            // line 149
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 149, $this->source); })()), "plainPassword", [], "any", false, false, false, 149), 'errors');
            yield "
                                        <div class=\"form-text\">Leave blank to keep the current password</div>
                                    </div>
                                ";
        }
        // line 153
        yield "                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Permissions -->
                ";
        // line 159
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["form"] ?? null), "permissions", [], "any", true, true, false, 159)) {
            // line 160
            yield "                    <div class=\"mb-4\">
                        <div class=\"card border-0 bg-light\">
                            <div class=\"card-header bg-warning text-dark\">
                                <h6 class=\"mb-0 fw-bold\">
                                    <i class=\"fas fa-shield-alt me-2\"></i>Permissions
                                </h6>
                            </div>
                            <div class=\"card-body\">
                                ";
            // line 168
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 168, $this->source); })()), "permissions", [], "any", false, false, false, 168), 'label', ["label_attr" => ["class" => "form-label fw-bold"]]);
            yield "
                                ";
            // line 169
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 169, $this->source); })()), "permissions", [], "any", false, false, false, 169), 'widget');
            yield "
                                ";
            // line 170
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 170, $this->source); })()), "permissions", [], "any", false, false, false, 170), 'errors');
            yield "
                                <div class=\"form-text\">Select the permissions this administrator should have</div>
                            </div>
                        </div>
                    </div>
                ";
        }
        // line 176
        yield "                
                <!-- Action Buttons -->
                <div class=\"d-flex justify-content-between\">
                    <a href=\"";
        // line 179
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admin_view", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 179, $this->source); })()), "id", [], "any", false, false, false, 179)]), "html", null, true);
        yield "\" class=\"btn btn-outline-secondary btn-lg\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <div>
                        <button type=\"submit\" class=\"btn btn-primary btn-lg me-2\">
                            <i class=\"fas fa-save me-2\"></i>Update Administrator
                        </button>
                        <button type=\"submit\" name=\"save_and_continue\" value=\"1\" class=\"btn btn-success btn-lg\">
                            <i class=\"fas fa-save me-2\"></i>Save & Continue Editing
                        </button>
                    </div>
                </div>
                
                ";
        // line 192
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 192, $this->source); })()), 'form_end');
        yield "
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 199
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 200
        yield "<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/admins/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  500 => 200,  487 => 199,  470 => 192,  454 => 179,  449 => 176,  440 => 170,  436 => 169,  432 => 168,  422 => 160,  420 => 159,  412 => 153,  405 => 149,  401 => 148,  397 => 147,  394 => 146,  392 => 145,  389 => 144,  383 => 141,  378 => 139,  374 => 138,  370 => 136,  368 => 135,  365 => 134,  361 => 132,  352 => 126,  348 => 124,  346 => 123,  342 => 122,  338 => 121,  334 => 120,  331 => 119,  329 => 118,  311 => 103,  307 => 102,  303 => 101,  296 => 97,  292 => 96,  288 => 95,  280 => 90,  276 => 89,  272 => 88,  266 => 85,  262 => 84,  258 => 83,  241 => 69,  217 => 48,  211 => 45,  201 => 38,  194 => 33,  188 => 30,  184 => 28,  177 => 24,  172 => 23,  170 => 22,  160 => 14,  147 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Administrator - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Administrator{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_admins') }}\">Administrators</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_admin_view', {'id': admin.id}) }}\">{{ admin.fullName }}</a></li>
<li class=\"breadcrumb-item active\">Edit</li>
{% endblock %}

{% block content %}
<!-- Professional Header Section -->
<div class=\"row mb-4\">
    <div class=\"col-12\">
        <div class=\"card shadow-sm border-0\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
            <div class=\"card-body py-4\">
                <div class=\"row align-items-center\">
                    <div class=\"col-lg-8\">
                        <div class=\"d-flex align-items-center\">
                            {% if admin.profileImage %}
                                <img src=\"{{ asset('uploads/profiles/' ~ admin.profileImage) }}\" 
                                     alt=\"{{ admin.fullName }}\" 
                                     class=\"rounded-circle me-4\"
                                     style=\"width: 80px; height: 80px; object-fit: cover; border: 3px solid rgba(255,255,255,0.3);\">
                            {% else %}
                                <div class=\"rounded-circle me-4 d-flex align-items-center justify-content-center\"
                                     style=\"width: 80px; height: 80px; background: rgba(255,255,255,0.2); border: 3px solid rgba(255,255,255,0.3);\">
                                    <span class=\"fs-2 fw-bold\">{{ admin.fullName|slice(0,1)|upper }}</span>
                                </div>
                            {% endif %}
                            <div>
                                <h1 class=\"h3 mb-2 fw-bold\">
                                    <i class=\"fas fa-edit me-3\"></i>Edit Administrator
                                </h1>
                                <p class=\"mb-0 opacity-90\">
                                    Modify settings and permissions for {{ admin.fullName }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class=\"col-lg-4 text-lg-end\">
                        <div class=\"d-flex flex-wrap gap-2 justify-content-lg-end\">
                            <a href=\"{{ path('admin_admin_view', {'id': admin.id}) }}\" class=\"btn btn-outline-light btn-lg\">
                                <i class=\"fas fa-eye me-2\"></i>View Details
                            </a>
                            <a href=\"{{ path('admin_admins') }}\" class=\"btn btn-light btn-lg\">
                                <i class=\"fas fa-arrow-left me-2\"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Form -->
<div class=\"row\">
    <div class=\"col-12\">
        <div class=\"card border-0 shadow-sm\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-cog me-2 text-primary\"></i>Administrator Settings
                </h5>
            </div>
            <div class=\"card-body\">
                {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                
                <div class=\"row\">
                    <!-- Basic Information -->
                    <div class=\"col-lg-6\">
                        <div class=\"card border-0 bg-light mb-4\">
                            <div class=\"card-header bg-primary text-white\">
                                <h6 class=\"mb-0 fw-bold\">
                                    <i class=\"fas fa-user me-2\"></i>Basic Information
                                </h6>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"row\">
                                    <div class=\"col-md-6 mb-3\">
                                        {{ form_label(form.firstName, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.firstName, {'attr': {'class': 'form-control'}}) }}
                                        {{ form_errors(form.firstName) }}
                                    </div>
                                    <div class=\"col-md-6 mb-3\">
                                        {{ form_label(form.lastName, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.lastName, {'attr': {'class': 'form-control'}}) }}
                                        {{ form_errors(form.lastName) }}
                                    </div>
                                </div>
                                
                                <div class=\"mb-3\">
                                    {{ form_label(form.username, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                    {{ form_widget(form.username) }}
                                    {{ form_errors(form.username) }}
                                </div>
                                
                                <div class=\"mb-3\">
                                    {{ form_label(form.email, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                    {{ form_widget(form.email) }}
                                    {{ form_errors(form.email) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Settings -->
                    <div class=\"col-lg-6\">
                        <div class=\"card border-0 bg-light mb-4\">
                            <div class=\"card-header bg-success text-white\">
                                <h6 class=\"mb-0 fw-bold\">
                                    <i class=\"fas fa-cog me-2\"></i>Account Settings
                                </h6>
                            </div>
                            <div class=\"card-body\">
                                {% if form.profileImageFile is defined %}
                                    <div class=\"mb-3\">
                                        {{ form_label(form.profileImageFile, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.profileImageFile) }}
                                        {{ form_errors(form.profileImageFile) }}
                                        {% if admin.profileImage %}
                                            <div class=\"mt-2\">
                                                <small class=\"text-muted\">Current image:</small>
                                                <img src=\"{{ asset('uploads/profiles/' ~ admin.profileImage) }}\" 
                                                     alt=\"Current profile\" 
                                                     class=\"img-thumbnail ms-2\" 
                                                     style=\"max-height: 50px;\">
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %}
                                
                                {% if form.isActive is defined %}
                                    <div class=\"mb-3\">
                                        <div class=\"form-check form-switch\">
                                            {{ form_widget(form.isActive) }}
                                            {{ form_label(form.isActive, null, {'label_attr': {'class': 'form-check-label fw-bold'}}) }}
                                        </div>
                                        {{ form_errors(form.isActive) }}
                                    </div>
                                {% endif %}
                                
                                {% if form.plainPassword is defined %}
                                    <div class=\"mb-3\">
                                        {{ form_label(form.plainPassword, 'New Password (leave blank to keep current)', {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.plainPassword) }}
                                        {{ form_errors(form.plainPassword) }}
                                        <div class=\"form-text\">Leave blank to keep the current password</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Permissions -->
                {% if form.permissions is defined %}
                    <div class=\"mb-4\">
                        <div class=\"card border-0 bg-light\">
                            <div class=\"card-header bg-warning text-dark\">
                                <h6 class=\"mb-0 fw-bold\">
                                    <i class=\"fas fa-shield-alt me-2\"></i>Permissions
                                </h6>
                            </div>
                            <div class=\"card-body\">
                                {{ form_label(form.permissions, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                {{ form_widget(form.permissions) }}
                                {{ form_errors(form.permissions) }}
                                <div class=\"form-text\">Select the permissions this administrator should have</div>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                <!-- Action Buttons -->
                <div class=\"d-flex justify-content-between\">
                    <a href=\"{{ path('admin_admin_view', {'id': admin.id}) }}\" class=\"btn btn-outline-secondary btn-lg\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <div>
                        <button type=\"submit\" class=\"btn btn-primary btn-lg me-2\">
                            <i class=\"fas fa-save me-2\"></i>Update Administrator
                        </button>
                        <button type=\"submit\" name=\"save_and_continue\" value=\"1\" class=\"btn btn-success btn-lg\">
                            <i class=\"fas fa-save me-2\"></i>Save & Continue Editing
                        </button>
                    </div>
                </div>
                
                {{ form_end(form) }}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }
});
</script>
{% endblock %}
", "admin/admins/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\admins\\edit.html.twig");
    }
}
