<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* public/courses/index.html.twig */
class __TwigTemplate_98ca2f40bba874257cd436421eedf8c3 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "public/courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "public/courses/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Our Courses - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid px-0\">
    <!-- Hero Section -->
    <section class=\"hero-section position-relative\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); min-height: 60vh;\">
        <div class=\"container h-100\">
            <div class=\"row h-100 align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold text-white mb-4\">Our Courses</h1>
                    <p class=\"lead text-white-50 mb-4\">
                        Discover our comprehensive range of trading and financial education courses designed to help you succeed in the markets.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class=\"py-5\">
        <div class=\"container\">
            ";
        // line 24
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 24, $this->source); })())) > 0)) {
            // line 25
            yield "                <div class=\"row\">
                    ";
            // line 26
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 26, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
                // line 27
                yield "                        <div class=\"col-lg-4 col-md-6 mb-4\">
                            <div class=\"card h-100 border-0 shadow-sm course-card\">
                                ";
                // line 29
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailImage", [], "any", false, false, false, 29)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 30
                    yield "                                    <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailUrl", [], "any", false, false, false, 30), "html", null, true);
                    yield "\" class=\"card-img-top\" alt=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 30), "html", null, true);
                    yield "\" style=\"height: 200px; object-fit: cover;\">
                                ";
                } else {
                    // line 32
                    yield "                                    <div class=\"card-img-top bg-light d-flex align-items-center justify-content-center\" style=\"height: 200px;\">
                                        <i class=\"fas fa-graduation-cap text-muted fa-3x\"></i>
                                    </div>
                                ";
                }
                // line 36
                yield "                                
                                <div class=\"card-body d-flex flex-column\">
                                    <div class=\"d-flex justify-content-between align-items-start mb-2\">
                                        <span class=\"badge bg-primary\">";
                // line 39
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 39), "html", null, true);
                yield "</span>
                                        <span class=\"badge bg-secondary\">";
                // line 40
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", true, true, false, 40)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 40), "Beginner")) : ("Beginner")), "html", null, true);
                yield "</span>
                                    </div>
                                    
                                    <h5 class=\"card-title fw-bold text-dark\">";
                // line 43
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 43), "html", null, true);
                yield "</h5>
                                    <p class=\"card-text text-muted flex-grow-1\">
                                        ";
                // line 45
                yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 45)) > 150)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 45), 0, 150) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 45), "html", null, true)));
                yield "
                                    </p>
                                    
                                    <div class=\"mb-3\">
                                        <small class=\"text-muted\">
                                            <i class=\"fas fa-tag me-1\"></i>";
                // line 50
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 50), "html", null, true);
                yield "
                                        </small>
                                    </div>
                                    
                                    ";
                // line 54
                if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "learningOutcomes", [], "any", false, false, false, 54)) > 0)) {
                    // line 55
                    yield "                                        <div class=\"mb-3\">
                                            <h6 class=\"fw-bold text-dark mb-2\">What You'll Learn:</h6>
                                            <ul class=\"list-unstyled\">
                                                ";
                    // line 58
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "learningOutcomes", [], "any", false, false, false, 58), 0, 3));
                    foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                        // line 59
                        yield "                                                    <li class=\"mb-1\">
                                                        <i class=\"fas fa-check text-success me-2\"></i>
                                                        <small>";
                        // line 61
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                        yield "</small>
                                                    </li>
                                                ";
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 64
                    yield "                                                ";
                    if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "learningOutcomes", [], "any", false, false, false, 64)) > 3)) {
                        // line 65
                        yield "                                                    <li class=\"mb-1\">
                                                        <small class=\"text-muted\">+ ";
                        // line 66
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "learningOutcomes", [], "any", false, false, false, 66)) - 3), "html", null, true);
                        yield " more...</small>
                                                    </li>
                                                ";
                    }
                    // line 69
                    yield "                                            </ul>
                                        </div>
                                    ";
                }
                // line 72
                yield "                                    
                                    <div class=\"mt-auto\">
                                        <a href=\"";
                // line 74
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("public_course_detail", ["code" => CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 74)]), "html", null, true);
                yield "\" class=\"btn btn-primary w-100\">
                                            <i class=\"fas fa-eye me-2\"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 82
            yield "                </div>
            ";
        } else {
            // line 84
            yield "                <div class=\"text-center py-5\">
                    <i class=\"fas fa-graduation-cap text-muted fa-5x mb-4\"></i>
                    <h3 class=\"text-muted\">No Courses Available</h3>
                    <p class=\"text-muted\">Please check back later for new courses.</p>
                </div>
            ";
        }
        // line 90
        yield "        </div>
    </section>
</div>

<style>
.course-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.hero-section > .container {
    position: relative;
    z-index: 2;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "public/courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  255 => 90,  247 => 84,  243 => 82,  229 => 74,  225 => 72,  220 => 69,  214 => 66,  211 => 65,  208 => 64,  199 => 61,  195 => 59,  191 => 58,  186 => 55,  184 => 54,  177 => 50,  169 => 45,  164 => 43,  158 => 40,  154 => 39,  149 => 36,  143 => 32,  135 => 30,  133 => 29,  129 => 27,  125 => 26,  122 => 25,  120 => 24,  100 => 6,  87 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Our Courses - Capitol Academy{% endblock %}

{% block body %}
<div class=\"container-fluid px-0\">
    <!-- Hero Section -->
    <section class=\"hero-section position-relative\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); min-height: 60vh;\">
        <div class=\"container h-100\">
            <div class=\"row h-100 align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold text-white mb-4\">Our Courses</h1>
                    <p class=\"lead text-white-50 mb-4\">
                        Discover our comprehensive range of trading and financial education courses designed to help you succeed in the markets.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class=\"py-5\">
        <div class=\"container\">
            {% if courses|length > 0 %}
                <div class=\"row\">
                    {% for course in courses %}
                        <div class=\"col-lg-4 col-md-6 mb-4\">
                            <div class=\"card h-100 border-0 shadow-sm course-card\">
                                {% if course.thumbnailImage %}
                                    <img src=\"{{ course.thumbnailUrl }}\" class=\"card-img-top\" alt=\"{{ course.title }}\" style=\"height: 200px; object-fit: cover;\">
                                {% else %}
                                    <div class=\"card-img-top bg-light d-flex align-items-center justify-content-center\" style=\"height: 200px;\">
                                        <i class=\"fas fa-graduation-cap text-muted fa-3x\"></i>
                                    </div>
                                {% endif %}
                                
                                <div class=\"card-body d-flex flex-column\">
                                    <div class=\"d-flex justify-content-between align-items-start mb-2\">
                                        <span class=\"badge bg-primary\">{{ course.code }}</span>
                                        <span class=\"badge bg-secondary\">{{ course.level|default('Beginner') }}</span>
                                    </div>
                                    
                                    <h5 class=\"card-title fw-bold text-dark\">{{ course.title }}</h5>
                                    <p class=\"card-text text-muted flex-grow-1\">
                                        {{ course.description|length > 150 ? course.description|slice(0, 150) ~ '...' : course.description }}
                                    </p>
                                    
                                    <div class=\"mb-3\">
                                        <small class=\"text-muted\">
                                            <i class=\"fas fa-tag me-1\"></i>{{ course.category }}
                                        </small>
                                    </div>
                                    
                                    {% if course.learningOutcomes|length > 0 %}
                                        <div class=\"mb-3\">
                                            <h6 class=\"fw-bold text-dark mb-2\">What You'll Learn:</h6>
                                            <ul class=\"list-unstyled\">
                                                {% for outcome in course.learningOutcomes|slice(0, 3) %}
                                                    <li class=\"mb-1\">
                                                        <i class=\"fas fa-check text-success me-2\"></i>
                                                        <small>{{ outcome }}</small>
                                                    </li>
                                                {% endfor %}
                                                {% if course.learningOutcomes|length > 3 %}
                                                    <li class=\"mb-1\">
                                                        <small class=\"text-muted\">+ {{ course.learningOutcomes|length - 3 }} more...</small>
                                                    </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    {% endif %}
                                    
                                    <div class=\"mt-auto\">
                                        <a href=\"{{ path('public_course_detail', {code: course.code}) }}\" class=\"btn btn-primary w-100\">
                                            <i class=\"fas fa-eye me-2\"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class=\"text-center py-5\">
                    <i class=\"fas fa-graduation-cap text-muted fa-5x mb-4\"></i>
                    <h3 class=\"text-muted\">No Courses Available</h3>
                    <p class=\"text-muted\">Please check back later for new courses.</p>
                </div>
            {% endif %}
        </div>
    </section>
</div>

<style>
.course-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.hero-section > .container {
    position: relative;
    z-index: 2;
}
</style>
{% endblock %}
", "public/courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\public\\courses\\index.html.twig");
    }
}
