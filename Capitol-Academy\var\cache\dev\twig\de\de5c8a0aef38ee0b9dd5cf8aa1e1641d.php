<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/order/show.html.twig */
class __TwigTemplate_237cb4908b602f0506d97d2e07d73f0c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/show.html.twig"));

        // line 1
        yield from $this->load("admin/order/show.html.twig", 1, "2142033747")->unwrap()->yield(CoreExtension::merge($context, ["entity_name" => "Order", "entity_title" => CoreExtension::getAttribute($this->env, $this->source,         // line 3
(isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 3, $this->source); })()), "orderNumber", [], "any", false, false, false, 3), "entity_code" => CoreExtension::getAttribute($this->env, $this->source,         // line 4
(isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 4, $this->source); })()), "orderNumber", [], "any", false, false, false, 4), "entity_icon" => "fas fa-shopping-cart", "breadcrumb_items" => [["path" => "admin_dashboard", "title" => "Home"], ["path" => "admin_order_index", "title" => "Orders"], ["title" => CoreExtension::getAttribute($this->env, $this->source,         // line 9
(isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 9, $this->source); })()), "orderNumber", [], "any", false, false, false, 9), "active" => true]], "edit_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_order_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source,         // line 11
(isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 11, $this->source); })()), "id", [], "any", false, false, false, 11)]), "back_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_order_index"), "print_function" => "printOrderDetails"]));
        // line 311
        yield "
<script>
function printOrderDetails() {
    window.print();
}
</script>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/order/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  54 => 311,  52 => 11,  51 => 9,  50 => 4,  49 => 3,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Order',
    'entity_title': order.orderNumber,
    'entity_code': order.orderNumber,
    'entity_icon': 'fas fa-shopping-cart',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_order_index', 'title': 'Orders'},
        {'title': order.orderNumber, 'active': true}
    ],
    'edit_path': path('admin_order_edit', {'id': order.id}),
    'back_path': path('admin_order_index'),
    'print_function': 'printOrderDetails'
} %}

{% block preview_content %}

                        <!-- Order Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Order Number -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                        Order Number
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 1.1rem;\">{{ order.orderNumber }}</code>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Price -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                        Total Amount
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1.2rem;\">
                                            \${{ order.totalPrice }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Customer Name -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-user text-primary mr-1\"></i>
                                        Customer Name
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {{ order.user.fullName }}
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Email -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                        Customer Email
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <a href=\"mailto:{{ order.user.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ order.user.email }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Payment Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-credit-card text-primary mr-1\"></i>
                                        Payment Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {% set status_colors = {
                                            'completed': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                                            'pending': 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',
                                            'failed': 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
                                            'refunded': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)',
                                            'cancelled': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
                                        } %}
                                        {% set status_icons = {
                                            'completed': 'fas fa-check-circle',
                                            'pending': 'fas fa-clock',
                                            'failed': 'fas fa-times-circle',
                                            'refunded': 'fas fa-undo',
                                            'cancelled': 'fas fa-ban'
                                        } %}
                                        <span class=\"badge px-3 py-2\" style=\"background: {{ status_colors[order.paymentStatus] }}; color: white;\">
                                            <i class=\"{{ status_icons[order.paymentStatus] }} mr-1\"></i>{{ order.paymentStatus|title }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Gateway -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-credit-card text-primary mr-1\"></i>
                                        Payment Gateway
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {% if order.paymentGateway %}
                                            <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
                                                {% if order.paymentGateway == 'paypal' %}
                                                    <i class=\"fab fa-paypal mr-1\"></i>PayPal
                                                {% elseif order.paymentGateway == 'stripe' %}
                                                    <i class=\"fab fa-stripe mr-1\"></i>Stripe
                                                {% else %}
                                                    <i class=\"fas fa-hand-holding-usd mr-1\"></i>{{ order.paymentGateway|title }}
                                                {% endif %}
                                            </span>
                                        {% else %}
                                            <span class=\"text-muted\">Not specified</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-shopping-bag text-primary mr-1\"></i>
                                Order Items ({{ order.items|length }})
                            </label>
                            <div class=\"items-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;\">
                                {% if order.items|length > 0 %}
                                    {% for item in order.items %}
                                    <div class=\"item-card\" style=\"background: white; border: 2px solid #011a2d; border-radius: 8px; padding: 1.25rem; margin-bottom: {{ loop.last ? '0' : '1rem' }}; box-shadow: 0 2px 8px rgba(1, 26, 45, 0.1);\">
                                        <div class=\"row align-items-center\">
                                            <div class=\"col-md-6\">
                                                <h6 class=\"mb-1\" style=\"color: #011a2d; font-weight: 600;\">{{ item.name }}</h6>
                                                <small class=\"text-muted\">{{ item.type|title }}</small>
                                            </div>
                                            <div class=\"col-md-3 text-center\">
                                                <span class=\"badge px-2 py-1\" style=\"background: #e9ecef; color: #495057;\">
                                                    Qty: {{ item.quantity }}
                                                </span>
                                            </div>
                                            <div class=\"col-md-3 text-end\">
                                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1rem;\">
                                                    \${{ item.subtotal }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class=\"no-items\" style=\"text-align: center; color: #666; font-style: italic;\">No items in this order</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Transaction IDs -->
                        {% if order.paypalTransactionId or order.stripePaymentIntentId %}
                        <div class=\"row print-two-column clearfix\">
                            {% if order.paypalTransactionId %}
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fab fa-paypal text-primary mr-1\"></i>
                                        PayPal Transaction ID
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #0070ba; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;\">{{ order.paypalTransactionId }}</code>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            {% if order.stripePaymentIntentId %}
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fab fa-stripe text-primary mr-1\"></i>
                                        Stripe Payment Intent ID
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #635bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;\">{{ order.stripePaymentIntentId }}</code>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Notes -->
                        {% if order.notes %}
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-sticky-note text-primary mr-1\"></i>
                                Order Notes
                            </label>
                            <div class=\"notes-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;\">
                                <div class=\"notes-text\" style=\"background: white; border: 2px solid #ffc107; border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 8px rgba(255, 193, 7, 0.1); white-space: pre-wrap; line-height: 1.6;\">{{ order.notes }}</div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Timestamps Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Order Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Order Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {{ order.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Completion Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-check text-primary mr-1\"></i>
                                        Completion Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {% if order.completedAt %}
                                            {{ order.completedAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                        {% else %}
                                            <span class=\"text-muted\">Not completed</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
{% endblock %}

{% block print_body %}
    <!-- Order Information Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Order Number:</div>
                <div class=\"print-info-value\">{{ order.orderNumber }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Customer Name:</div>
                <div class=\"print-info-value\">{{ order.user.fullName }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Customer Email:</div>
                <div class=\"print-info-value\">{{ order.user.email }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Total Amount:</div>
                <div class=\"print-info-value\">\${{ order.totalPrice }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Payment Status:</div>
                <div class=\"print-info-value\">{{ order.paymentStatus|title }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Payment Gateway:</div>
                <div class=\"print-info-value\">{{ order.paymentGateway ? order.paymentGateway|title : 'Not specified' }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Order Date:</div>
                <div class=\"print-info-value\">{{ order.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Completion Date:</div>
                <div class=\"print-info-value\">{{ order.completedAt ? order.completedAt|date('F j, Y \\\\a\\\\t g:i A') : 'Not completed' }}</div>
            </div>
        </div>
    </div>

    <!-- Order Items Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Items</div>
        <div class=\"print-info-grid\">
            {% for item in order.items %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">{{ item.name }}:</div>
                <div class=\"print-info-value\">Qty: {{ item.quantity }} - \${{ item.subtotal }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    {% if order.notes %}
    <!-- Notes Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Notes</div>
        <div class=\"print-message-content\" style=\"padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;\">{{ order.notes }}</div>
    </div>
    {% endif %}
{% endblock %}
{% endembed %}

<script>
function printOrderDetails() {
    window.print();
}
</script>
", "admin/order/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\order\\show.html.twig");
    }
}


/* admin/order/show.html.twig */
class __TwigTemplate_237cb4908b602f0506d97d2e07d73f0c___2142033747 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'preview_content' => [$this, 'block_preview_content'],
            'print_body' => [$this, 'block_print_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "components/admin_preview_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/show.html.twig"));

        $this->parent = $this->load("components/admin_preview_layout.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 16
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_preview_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        // line 17
        yield "
                        <!-- Order Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Order Number -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                        Order Number
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 1.1rem;\">";
        // line 28
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 28, $this->source); })()), "orderNumber", [], "any", false, false, false, 28), "html", null, true);
        yield "</code>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Price -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                        Total Amount
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1.2rem;\">
                                            \$";
        // line 42
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 42, $this->source); })()), "totalPrice", [], "any", false, false, false, 42), "html", null, true);
        yield "
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Customer Name -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-user text-primary mr-1\"></i>
                                        Customer Name
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        ";
        // line 59
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 59, $this->source); })()), "user", [], "any", false, false, false, 59), "fullName", [], "any", false, false, false, 59), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Email -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                        Customer Email
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <a href=\"mailto:";
        // line 72
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 72, $this->source); })()), "user", [], "any", false, false, false, 72), "email", [], "any", false, false, false, 72), "html", null, true);
        yield "\" class=\"text-decoration-none\" style=\"color: #011a2d;\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 72, $this->source); })()), "user", [], "any", false, false, false, 72), "email", [], "any", false, false, false, 72), "html", null, true);
        yield "</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Payment Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-credit-card text-primary mr-1\"></i>
                                        Payment Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        ";
        // line 88
        $context["status_colors"] = ["completed" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)", "pending" => "linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)", "failed" => "linear-gradient(135deg, #dc3545 0%, #c82333 100%)", "refunded" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)", "cancelled" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"];
        // line 95
        yield "                                        ";
        $context["status_icons"] = ["completed" => "fas fa-check-circle", "pending" => "fas fa-clock", "failed" => "fas fa-times-circle", "refunded" => "fas fa-undo", "cancelled" => "fas fa-ban"];
        // line 102
        yield "                                        <span class=\"badge px-3 py-2\" style=\"background: ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["status_colors"]) || array_key_exists("status_colors", $context) ? $context["status_colors"] : (function () { throw new RuntimeError('Variable "status_colors" does not exist.', 102, $this->source); })()), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 102, $this->source); })()), "paymentStatus", [], "any", false, false, false, 102), [], "array", false, false, false, 102), "html", null, true);
        yield "; color: white;\">
                                            <i class=\"";
        // line 103
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["status_icons"]) || array_key_exists("status_icons", $context) ? $context["status_icons"] : (function () { throw new RuntimeError('Variable "status_icons" does not exist.', 103, $this->source); })()), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 103, $this->source); })()), "paymentStatus", [], "any", false, false, false, 103), [], "array", false, false, false, 103), "html", null, true);
        yield " mr-1\"></i>";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 103, $this->source); })()), "paymentStatus", [], "any", false, false, false, 103)), "html", null, true);
        yield "
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Gateway -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-credit-card text-primary mr-1\"></i>
                                        Payment Gateway
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        ";
        // line 117
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 117, $this->source); })()), "paymentGateway", [], "any", false, false, false, 117)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 118
            yield "                                            <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
                                                ";
            // line 119
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 119, $this->source); })()), "paymentGateway", [], "any", false, false, false, 119) == "paypal")) {
                // line 120
                yield "                                                    <i class=\"fab fa-paypal mr-1\"></i>PayPal
                                                ";
            } elseif ((CoreExtension::getAttribute($this->env, $this->source,             // line 121
(isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 121, $this->source); })()), "paymentGateway", [], "any", false, false, false, 121) == "stripe")) {
                // line 122
                yield "                                                    <i class=\"fab fa-stripe mr-1\"></i>Stripe
                                                ";
            } else {
                // line 124
                yield "                                                    <i class=\"fas fa-hand-holding-usd mr-1\"></i>";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 124, $this->source); })()), "paymentGateway", [], "any", false, false, false, 124)), "html", null, true);
                yield "
                                                ";
            }
            // line 126
            yield "                                            </span>
                                        ";
        } else {
            // line 128
            yield "                                            <span class=\"text-muted\">Not specified</span>
                                        ";
        }
        // line 130
        yield "                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-shopping-bag text-primary mr-1\"></i>
                                Order Items (";
        // line 139
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 139, $this->source); })()), "items", [], "any", false, false, false, 139)), "html", null, true);
        yield ")
                            </label>
                            <div class=\"items-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;\">
                                ";
        // line 142
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 142, $this->source); })()), "items", [], "any", false, false, false, 142)) > 0)) {
            // line 143
            yield "                                    ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 143, $this->source); })()), "items", [], "any", false, false, false, 143));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
                // line 144
                yield "                                    <div class=\"item-card\" style=\"background: white; border: 2px solid #011a2d; border-radius: 8px; padding: 1.25rem; margin-bottom: ";
                yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "last", [], "any", false, false, false, 144)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("0") : ("1rem"));
                yield "; box-shadow: 0 2px 8px rgba(1, 26, 45, 0.1);\">
                                        <div class=\"row align-items-center\">
                                            <div class=\"col-md-6\">
                                                <h6 class=\"mb-1\" style=\"color: #011a2d; font-weight: 600;\">";
                // line 147
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "name", [], "any", false, false, false, 147), "html", null, true);
                yield "</h6>
                                                <small class=\"text-muted\">";
                // line 148
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 148)), "html", null, true);
                yield "</small>
                                            </div>
                                            <div class=\"col-md-3 text-center\">
                                                <span class=\"badge px-2 py-1\" style=\"background: #e9ecef; color: #495057;\">
                                                    Qty: ";
                // line 152
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 152), "html", null, true);
                yield "
                                                </span>
                                            </div>
                                            <div class=\"col-md-3 text-end\">
                                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1rem;\">
                                                    \$";
                // line 157
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "subtotal", [], "any", false, false, false, 157), "html", null, true);
                yield "
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['item'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 163
            yield "                                ";
        } else {
            // line 164
            yield "                                    <div class=\"no-items\" style=\"text-align: center; color: #666; font-style: italic;\">No items in this order</div>
                                ";
        }
        // line 166
        yield "                            </div>
                        </div>

                        <!-- Transaction IDs -->
                        ";
        // line 170
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 170, $this->source); })()), "paypalTransactionId", [], "any", false, false, false, 170) || CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 170, $this->source); })()), "stripePaymentIntentId", [], "any", false, false, false, 170))) {
            // line 171
            yield "                        <div class=\"row print-two-column clearfix\">
                            ";
            // line 172
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 172, $this->source); })()), "paypalTransactionId", [], "any", false, false, false, 172)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 173
                yield "                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fab fa-paypal text-primary mr-1\"></i>
                                        PayPal Transaction ID
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #0070ba; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;\">";
                // line 180
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 180, $this->source); })()), "paypalTransactionId", [], "any", false, false, false, 180), "html", null, true);
                yield "</code>
                                    </div>
                                </div>
                            </div>
                            ";
            }
            // line 185
            yield "
                            ";
            // line 186
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 186, $this->source); })()), "stripePaymentIntentId", [], "any", false, false, false, 186)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 187
                yield "                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fab fa-stripe text-primary mr-1\"></i>
                                        Stripe Payment Intent ID
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #635bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;\">";
                // line 194
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 194, $this->source); })()), "stripePaymentIntentId", [], "any", false, false, false, 194), "html", null, true);
                yield "</code>
                                    </div>
                                </div>
                            </div>
                            ";
            }
            // line 199
            yield "                        </div>
                        ";
        }
        // line 201
        yield "
                        <!-- Notes -->
                        ";
        // line 203
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 203, $this->source); })()), "notes", [], "any", false, false, false, 203)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 204
            yield "                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-sticky-note text-primary mr-1\"></i>
                                Order Notes
                            </label>
                            <div class=\"notes-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;\">
                                <div class=\"notes-text\" style=\"background: white; border: 2px solid #ffc107; border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 8px rgba(255, 193, 7, 0.1); white-space: pre-wrap; line-height: 1.6;\">";
            // line 210
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 210, $this->source); })()), "notes", [], "any", false, false, false, 210), "html", null, true);
            yield "</div>
                            </div>
                        </div>
                        ";
        }
        // line 214
        yield "
                        <!-- Timestamps Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Order Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Order Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        ";
        // line 225
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 225, $this->source); })()), "createdAt", [], "any", false, false, false, 225), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Completion Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-check text-primary mr-1\"></i>
                                        Completion Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        ";
        // line 238
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 238, $this->source); })()), "completedAt", [], "any", false, false, false, 238)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 239
            yield "                                            ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 239, $this->source); })()), "completedAt", [], "any", false, false, false, 239), "F j, Y \\a\\t g:i A"), "html", null, true);
            yield "
                                        ";
        } else {
            // line 241
            yield "                                            <span class=\"text-muted\">Not completed</span>
                                        ";
        }
        // line 243
        yield "                                    </div>
                                </div>
                            </div>
                        </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 249
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_print_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        // line 250
        yield "    <!-- Order Information Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Order Number:</div>
                <div class=\"print-info-value\">";
        // line 256
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 256, $this->source); })()), "orderNumber", [], "any", false, false, false, 256), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Customer Name:</div>
                <div class=\"print-info-value\">";
        // line 260
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 260, $this->source); })()), "user", [], "any", false, false, false, 260), "fullName", [], "any", false, false, false, 260), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Customer Email:</div>
                <div class=\"print-info-value\">";
        // line 264
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 264, $this->source); })()), "user", [], "any", false, false, false, 264), "email", [], "any", false, false, false, 264), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Total Amount:</div>
                <div class=\"print-info-value\">\$";
        // line 268
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 268, $this->source); })()), "totalPrice", [], "any", false, false, false, 268), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Payment Status:</div>
                <div class=\"print-info-value\">";
        // line 272
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 272, $this->source); })()), "paymentStatus", [], "any", false, false, false, 272)), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Payment Gateway:</div>
                <div class=\"print-info-value\">";
        // line 276
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 276, $this->source); })()), "paymentGateway", [], "any", false, false, false, 276)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 276, $this->source); })()), "paymentGateway", [], "any", false, false, false, 276)), "html", null, true)) : ("Not specified"));
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Order Date:</div>
                <div class=\"print-info-value\">";
        // line 280
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 280, $this->source); })()), "createdAt", [], "any", false, false, false, 280), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Completion Date:</div>
                <div class=\"print-info-value\">";
        // line 284
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 284, $this->source); })()), "completedAt", [], "any", false, false, false, 284)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 284, $this->source); })()), "completedAt", [], "any", false, false, false, 284), "F j, Y \\a\\t g:i A"), "html", null, true)) : ("Not completed"));
        yield "</div>
            </div>
        </div>
    </div>

    <!-- Order Items Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Items</div>
        <div class=\"print-info-grid\">
            ";
        // line 293
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 293, $this->source); })()), "items", [], "any", false, false, false, 293));
        foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
            // line 294
            yield "            <div class=\"print-info-row\">
                <div class=\"print-info-label\">";
            // line 295
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "name", [], "any", false, false, false, 295), "html", null, true);
            yield ":</div>
                <div class=\"print-info-value\">Qty: ";
            // line 296
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 296), "html", null, true);
            yield " - \$";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "subtotal", [], "any", false, false, false, 296), "html", null, true);
            yield "</div>
            </div>
            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['item'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 299
        yield "        </div>
    </div>

    ";
        // line 302
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 302, $this->source); })()), "notes", [], "any", false, false, false, 302)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 303
            yield "    <!-- Notes Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Notes</div>
        <div class=\"print-message-content\" style=\"padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;\">";
            // line 306
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 306, $this->source); })()), "notes", [], "any", false, false, false, 306), "html", null, true);
            yield "</div>
    </div>
    ";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/order/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  962 => 306,  957 => 303,  955 => 302,  950 => 299,  939 => 296,  935 => 295,  932 => 294,  928 => 293,  916 => 284,  909 => 280,  902 => 276,  895 => 272,  888 => 268,  881 => 264,  874 => 260,  867 => 256,  859 => 250,  846 => 249,  831 => 243,  827 => 241,  821 => 239,  819 => 238,  803 => 225,  790 => 214,  783 => 210,  775 => 204,  773 => 203,  769 => 201,  765 => 199,  757 => 194,  748 => 187,  746 => 186,  743 => 185,  735 => 180,  726 => 173,  724 => 172,  721 => 171,  719 => 170,  713 => 166,  709 => 164,  706 => 163,  686 => 157,  678 => 152,  671 => 148,  667 => 147,  660 => 144,  642 => 143,  640 => 142,  634 => 139,  623 => 130,  619 => 128,  615 => 126,  609 => 124,  605 => 122,  603 => 121,  600 => 120,  598 => 119,  595 => 118,  593 => 117,  574 => 103,  569 => 102,  566 => 95,  564 => 88,  543 => 72,  527 => 59,  507 => 42,  490 => 28,  477 => 17,  464 => 16,  441 => 1,  54 => 311,  52 => 11,  51 => 9,  50 => 4,  49 => 3,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Order',
    'entity_title': order.orderNumber,
    'entity_code': order.orderNumber,
    'entity_icon': 'fas fa-shopping-cart',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_order_index', 'title': 'Orders'},
        {'title': order.orderNumber, 'active': true}
    ],
    'edit_path': path('admin_order_edit', {'id': order.id}),
    'back_path': path('admin_order_index'),
    'print_function': 'printOrderDetails'
} %}

{% block preview_content %}

                        <!-- Order Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Order Number -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                        Order Number
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 1.1rem;\">{{ order.orderNumber }}</code>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Price -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                        Total Amount
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1.2rem;\">
                                            \${{ order.totalPrice }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Customer Name -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-user text-primary mr-1\"></i>
                                        Customer Name
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {{ order.user.fullName }}
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Email -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                        Customer Email
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <a href=\"mailto:{{ order.user.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ order.user.email }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Payment Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-credit-card text-primary mr-1\"></i>
                                        Payment Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {% set status_colors = {
                                            'completed': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                                            'pending': 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',
                                            'failed': 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
                                            'refunded': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)',
                                            'cancelled': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
                                        } %}
                                        {% set status_icons = {
                                            'completed': 'fas fa-check-circle',
                                            'pending': 'fas fa-clock',
                                            'failed': 'fas fa-times-circle',
                                            'refunded': 'fas fa-undo',
                                            'cancelled': 'fas fa-ban'
                                        } %}
                                        <span class=\"badge px-3 py-2\" style=\"background: {{ status_colors[order.paymentStatus] }}; color: white;\">
                                            <i class=\"{{ status_icons[order.paymentStatus] }} mr-1\"></i>{{ order.paymentStatus|title }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Gateway -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-credit-card text-primary mr-1\"></i>
                                        Payment Gateway
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {% if order.paymentGateway %}
                                            <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
                                                {% if order.paymentGateway == 'paypal' %}
                                                    <i class=\"fab fa-paypal mr-1\"></i>PayPal
                                                {% elseif order.paymentGateway == 'stripe' %}
                                                    <i class=\"fab fa-stripe mr-1\"></i>Stripe
                                                {% else %}
                                                    <i class=\"fas fa-hand-holding-usd mr-1\"></i>{{ order.paymentGateway|title }}
                                                {% endif %}
                                            </span>
                                        {% else %}
                                            <span class=\"text-muted\">Not specified</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-shopping-bag text-primary mr-1\"></i>
                                Order Items ({{ order.items|length }})
                            </label>
                            <div class=\"items-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;\">
                                {% if order.items|length > 0 %}
                                    {% for item in order.items %}
                                    <div class=\"item-card\" style=\"background: white; border: 2px solid #011a2d; border-radius: 8px; padding: 1.25rem; margin-bottom: {{ loop.last ? '0' : '1rem' }}; box-shadow: 0 2px 8px rgba(1, 26, 45, 0.1);\">
                                        <div class=\"row align-items-center\">
                                            <div class=\"col-md-6\">
                                                <h6 class=\"mb-1\" style=\"color: #011a2d; font-weight: 600;\">{{ item.name }}</h6>
                                                <small class=\"text-muted\">{{ item.type|title }}</small>
                                            </div>
                                            <div class=\"col-md-3 text-center\">
                                                <span class=\"badge px-2 py-1\" style=\"background: #e9ecef; color: #495057;\">
                                                    Qty: {{ item.quantity }}
                                                </span>
                                            </div>
                                            <div class=\"col-md-3 text-end\">
                                                <span class=\"badge px-3 py-2\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1rem;\">
                                                    \${{ item.subtotal }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class=\"no-items\" style=\"text-align: center; color: #666; font-style: italic;\">No items in this order</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Transaction IDs -->
                        {% if order.paypalTransactionId or order.stripePaymentIntentId %}
                        <div class=\"row print-two-column clearfix\">
                            {% if order.paypalTransactionId %}
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fab fa-paypal text-primary mr-1\"></i>
                                        PayPal Transaction ID
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #0070ba; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;\">{{ order.paypalTransactionId }}</code>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            {% if order.stripePaymentIntentId %}
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fab fa-stripe text-primary mr-1\"></i>
                                        Stripe Payment Intent ID
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        <code style=\"background: #635bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;\">{{ order.stripePaymentIntentId }}</code>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Notes -->
                        {% if order.notes %}
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-sticky-note text-primary mr-1\"></i>
                                Order Notes
                            </label>
                            <div class=\"notes-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;\">
                                <div class=\"notes-text\" style=\"background: white; border: 2px solid #ffc107; border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 8px rgba(255, 193, 7, 0.1); white-space: pre-wrap; line-height: 1.6;\">{{ order.notes }}</div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Timestamps Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Order Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Order Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {{ order.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Completion Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-check text-primary mr-1\"></i>
                                        Completion Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\">
                                        {% if order.completedAt %}
                                            {{ order.completedAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                        {% else %}
                                            <span class=\"text-muted\">Not completed</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
{% endblock %}

{% block print_body %}
    <!-- Order Information Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Order Number:</div>
                <div class=\"print-info-value\">{{ order.orderNumber }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Customer Name:</div>
                <div class=\"print-info-value\">{{ order.user.fullName }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Customer Email:</div>
                <div class=\"print-info-value\">{{ order.user.email }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Total Amount:</div>
                <div class=\"print-info-value\">\${{ order.totalPrice }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Payment Status:</div>
                <div class=\"print-info-value\">{{ order.paymentStatus|title }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Payment Gateway:</div>
                <div class=\"print-info-value\">{{ order.paymentGateway ? order.paymentGateway|title : 'Not specified' }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Order Date:</div>
                <div class=\"print-info-value\">{{ order.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Completion Date:</div>
                <div class=\"print-info-value\">{{ order.completedAt ? order.completedAt|date('F j, Y \\\\a\\\\t g:i A') : 'Not completed' }}</div>
            </div>
        </div>
    </div>

    <!-- Order Items Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Items</div>
        <div class=\"print-info-grid\">
            {% for item in order.items %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">{{ item.name }}:</div>
                <div class=\"print-info-value\">Qty: {{ item.quantity }} - \${{ item.subtotal }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    {% if order.notes %}
    <!-- Notes Section -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Order Notes</div>
        <div class=\"print-message-content\" style=\"padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;\">{{ order.notes }}</div>
    </div>
    {% endif %}
{% endblock %}
{% endembed %}

<script>
function printOrderDetails() {
    window.print();
}
</script>
", "admin/order/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\order\\show.html.twig");
    }
}
