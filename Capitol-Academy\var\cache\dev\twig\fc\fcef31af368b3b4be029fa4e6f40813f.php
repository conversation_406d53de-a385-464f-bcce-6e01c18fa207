<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/market_analysis/show.html.twig */
class __TwigTemplate_f25b6d3acde803cda53883bfa2e3dd08 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/show.html.twig"));

        // line 1
        yield from $this->load("admin/market_analysis/show.html.twig", 1, "795602092")->unwrap()->yield(CoreExtension::merge($context, ["entity_name" => "Market Analysis", "entity_title" => CoreExtension::getAttribute($this->env, $this->source,         // line 3
(isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "entity_code" => CoreExtension::getAttribute($this->env, $this->source,         // line 4
(isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 4, $this->source); })()), "assetTypeLabel", [], "any", false, false, false, 4), "entity_icon" => "fas fa-chart-line", "breadcrumb_items" => [["path" => "admin_dashboard", "title" => "Home"], ["path" => "admin_market_analysis_index", "title" => "Market Analysis"], ["title" => CoreExtension::getAttribute($this->env, $this->source,         // line 9
(isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 9, $this->source); })()), "title", [], "any", false, false, false, 9), "active" => true]], "edit_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_edit_readable", ["slug" => CoreExtension::getAttribute($this->env, $this->source,         // line 11
(isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 11, $this->source); })()), "slug", [], "any", false, false, false, 11)]), "back_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_index"), "print_function" => "printMarketAnalysisDetails"]));
        // line 229
        yield "
";
        // line 230
        yield from $this->unwrap()->yieldBlock('stylesheets', $context, $blocks);
        // line 243
        yield "
";
        // line 244
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    // line 230
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 231
        yield "<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 244
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 245
        yield "<script>
// Print function for the preview layout
function printMarketAnalysisDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/market_analysis/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  123 => 245,  110 => 244,  88 => 231,  75 => 230,  64 => 244,  61 => 243,  59 => 230,  56 => 229,  54 => 11,  53 => 9,  52 => 4,  51 => 3,  50 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Market Analysis',
    'entity_title': article.title,
    'entity_code': article.assetTypeLabel,
    'entity_icon': 'fas fa-chart-line',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_market_analysis_index', 'title': 'Market Analysis'},
        {'title': article.title, 'active': true}
    ],
    'edit_path': path('admin_market_analysis_edit_readable', {'slug': article.slug}),
    'back_path': path('admin_market_analysis_index'),
    'print_function': 'printMarketAnalysisDetails'
} %}

{% block preview_content %}

    <!-- Article Information -->
    <div class=\"row\">
        <!-- Title and Asset Type -->
        <div class=\"col-md-8\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-heading text-primary mr-1\"></i>
                    Title
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.title }}
                </div>
            </div>
        </div>

        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-chart-bar text-primary mr-1\"></i>
                    Asset Type
                </label>
                <div class=\"enhanced-display-field\">
                    <span class=\"badge\" style=\"background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 20px;\">
                        {{ article.assetTypeLabel }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Author and Publish Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user-edit text-primary mr-1\"></i>
                    Author
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.author ?? 'Capitol Academy Analyst' }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Publish Date
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.publishDate|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Excerpt (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-quote-left text-primary mr-1\"></i>
            Excerpt
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 80px;\">
            {{ article.excerpt|nl2br }}
        </div>
    </div>

    <!-- Content -->
    {% if article.content %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Content
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 200px; max-height: 400px; overflow-y: auto;\">
            {{ article.content|raw }}
        </div>
    </div>
    {% endif %}

    <!-- Images Row -->
    <div class=\"row\">
        <!-- Thumbnail Image -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-image text-primary mr-1\"></i>
                    Thumbnail Image
                </label>
                <div class=\"enhanced-display-field text-center\" style=\"min-height: 150px; display: flex; align-items: center; justify-content: center;\">
                    {% if article.thumbnailImage %}
                        <img src=\"{{ asset('uploads/market_analysis/' ~ article.thumbnailImage) }}\" 
                             alt=\"Thumbnail\" 
                             class=\"img-fluid rounded\" 
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                    {% else %}
                        <div class=\"text-muted\">
                            <i class=\"fas fa-image fa-3x mb-2\"></i>
                            <br>No thumbnail image
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Featured Image -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-star text-primary mr-1\"></i>
                    Featured Image
                </label>
                <div class=\"enhanced-display-field text-center\" style=\"min-height: 150px; display: flex; align-items: center; justify-content: center;\">
                    {% if article.featuredImage %}
                        <img src=\"{{ asset('uploads/market_analysis/' ~ article.featuredImage) }}\" 
                             alt=\"Featured\" 
                             class=\"img-fluid rounded\" 
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                    {% else %}
                        <div class=\"text-muted\">
                            <i class=\"fas fa-star fa-3x mb-2\"></i>
                            <br>No featured image
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Date and Views Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                    Publish Date
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.publishDate|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-eye text-primary mr-1\"></i>
                    Views
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.views|number_format }}
                </div>
            </div>
        </div>
    </div>

    <!-- Status and Created Date Row (Last Line) -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if article.isActive %}
                        <span class=\"badge bg-success\">Active</span>
                    {% else %}
                        <span class=\"badge bg-secondary\">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    {% if article.createdAt %}
                        {{ article.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Updated Date (if needed) -->
    {% if article.updatedAt and article.updatedAt != article.createdAt %}
    <div class=\"row\">
        <div class=\"col-md-12\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar-edit text-primary mr-1\"></i>
                    Last Updated
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.updatedAt|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printMarketAnalysisDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/market_analysis/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\market_analysis\\show.html.twig");
    }
}


/* admin/market_analysis/show.html.twig */
class __TwigTemplate_f25b6d3acde803cda53883bfa2e3dd08___795602092 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'preview_content' => [$this, 'block_preview_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "components/admin_preview_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/show.html.twig"));

        $this->parent = $this->load("components/admin_preview_layout.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 16
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_preview_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        // line 17
        yield "
    <!-- Article Information -->
    <div class=\"row\">
        <!-- Title and Asset Type -->
        <div class=\"col-md-8\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-heading text-primary mr-1\"></i>
                    Title
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 28
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 28, $this->source); })()), "title", [], "any", false, false, false, 28), "html", null, true);
        yield "
                </div>
            </div>
        </div>

        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-chart-bar text-primary mr-1\"></i>
                    Asset Type
                </label>
                <div class=\"enhanced-display-field\">
                    <span class=\"badge\" style=\"background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 20px;\">
                        ";
        // line 41
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 41, $this->source); })()), "assetTypeLabel", [], "any", false, false, false, 41), "html", null, true);
        yield "
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Author and Publish Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user-edit text-primary mr-1\"></i>
                    Author
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 57
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["article"] ?? null), "author", [], "any", true, true, false, 57) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 57, $this->source); })()), "author", [], "any", false, false, false, 57)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 57, $this->source); })()), "author", [], "any", false, false, false, 57), "html", null, true)) : ("Capitol Academy Analyst"));
        yield "
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Publish Date
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 69
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 69, $this->source); })()), "publishDate", [], "any", false, false, false, 69), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                </div>
            </div>
        </div>
    </div>

    <!-- Excerpt (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-quote-left text-primary mr-1\"></i>
            Excerpt
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 80px;\">
            ";
        // line 82
        yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 82, $this->source); })()), "excerpt", [], "any", false, false, false, 82), "html", null, true));
        yield "
        </div>
    </div>

    <!-- Content -->
    ";
        // line 87
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 87, $this->source); })()), "content", [], "any", false, false, false, 87)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 88
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Content
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 200px; max-height: 400px; overflow-y: auto;\">
            ";
            // line 94
            yield CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 94, $this->source); })()), "content", [], "any", false, false, false, 94);
            yield "
        </div>
    </div>
    ";
        }
        // line 98
        yield "
    <!-- Images Row -->
    <div class=\"row\">
        <!-- Thumbnail Image -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-image text-primary mr-1\"></i>
                    Thumbnail Image
                </label>
                <div class=\"enhanced-display-field text-center\" style=\"min-height: 150px; display: flex; align-items: center; justify-content: center;\">
                    ";
        // line 109
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 109, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 109)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 110
            yield "                        <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/market_analysis/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 110, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 110))), "html", null, true);
            yield "\" 
                             alt=\"Thumbnail\" 
                             class=\"img-fluid rounded\" 
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                    ";
        } else {
            // line 115
            yield "                        <div class=\"text-muted\">
                            <i class=\"fas fa-image fa-3x mb-2\"></i>
                            <br>No thumbnail image
                        </div>
                    ";
        }
        // line 120
        yield "                </div>
            </div>
        </div>

        <!-- Featured Image -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-star text-primary mr-1\"></i>
                    Featured Image
                </label>
                <div class=\"enhanced-display-field text-center\" style=\"min-height: 150px; display: flex; align-items: center; justify-content: center;\">
                    ";
        // line 132
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 132, $this->source); })()), "featuredImage", [], "any", false, false, false, 132)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 133
            yield "                        <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/market_analysis/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 133, $this->source); })()), "featuredImage", [], "any", false, false, false, 133))), "html", null, true);
            yield "\" 
                             alt=\"Featured\" 
                             class=\"img-fluid rounded\" 
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                    ";
        } else {
            // line 138
            yield "                        <div class=\"text-muted\">
                            <i class=\"fas fa-star fa-3x mb-2\"></i>
                            <br>No featured image
                        </div>
                    ";
        }
        // line 143
        yield "                </div>
            </div>
        </div>
    </div>

    <!-- Date and Views Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                    Publish Date
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 157
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 157, $this->source); })()), "publishDate", [], "any", false, false, false, 157), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-eye text-primary mr-1\"></i>
                    Views
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 169
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 169, $this->source); })()), "views", [], "any", false, false, false, 169)), "html", null, true);
        yield "
                </div>
            </div>
        </div>
    </div>

    <!-- Status and Created Date Row (Last Line) -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 184
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 184, $this->source); })()), "isActive", [], "any", false, false, false, 184)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 185
            yield "                        <span class=\"badge bg-success\">Active</span>
                    ";
        } else {
            // line 187
            yield "                        <span class=\"badge bg-secondary\">Inactive</span>
                    ";
        }
        // line 189
        yield "                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 200
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 200, $this->source); })()), "createdAt", [], "any", false, false, false, 200)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 201
            yield "                        ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 201, $this->source); })()), "createdAt", [], "any", false, false, false, 201), "F j, Y \\a\\t g:i A"), "html", null, true);
            yield "
                    ";
        } else {
            // line 203
            yield "                        Not available
                    ";
        }
        // line 205
        yield "                </div>
            </div>
        </div>
    </div>

    <!-- Updated Date (if needed) -->
    ";
        // line 211
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 211, $this->source); })()), "updatedAt", [], "any", false, false, false, 211) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 211, $this->source); })()), "updatedAt", [], "any", false, false, false, 211) != CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 211, $this->source); })()), "createdAt", [], "any", false, false, false, 211)))) {
            // line 212
            yield "    <div class=\"row\">
        <div class=\"col-md-12\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar-edit text-primary mr-1\"></i>
                    Last Updated
                </label>
                <div class=\"enhanced-display-field\">
                    ";
            // line 220
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 220, $this->source); })()), "updatedAt", [], "any", false, false, false, 220), "F j, Y \\a\\t g:i A"), "html", null, true);
            yield "
                </div>
            </div>
        </div>
    </div>
    ";
        }
        // line 226
        yield "
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/market_analysis/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  760 => 226,  751 => 220,  741 => 212,  739 => 211,  731 => 205,  727 => 203,  721 => 201,  719 => 200,  706 => 189,  702 => 187,  698 => 185,  696 => 184,  678 => 169,  663 => 157,  647 => 143,  640 => 138,  631 => 133,  629 => 132,  615 => 120,  608 => 115,  599 => 110,  597 => 109,  584 => 98,  577 => 94,  569 => 88,  567 => 87,  559 => 82,  543 => 69,  528 => 57,  509 => 41,  493 => 28,  480 => 17,  467 => 16,  444 => 1,  123 => 245,  110 => 244,  88 => 231,  75 => 230,  64 => 244,  61 => 243,  59 => 230,  56 => 229,  54 => 11,  53 => 9,  52 => 4,  51 => 3,  50 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Market Analysis',
    'entity_title': article.title,
    'entity_code': article.assetTypeLabel,
    'entity_icon': 'fas fa-chart-line',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_market_analysis_index', 'title': 'Market Analysis'},
        {'title': article.title, 'active': true}
    ],
    'edit_path': path('admin_market_analysis_edit_readable', {'slug': article.slug}),
    'back_path': path('admin_market_analysis_index'),
    'print_function': 'printMarketAnalysisDetails'
} %}

{% block preview_content %}

    <!-- Article Information -->
    <div class=\"row\">
        <!-- Title and Asset Type -->
        <div class=\"col-md-8\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-heading text-primary mr-1\"></i>
                    Title
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.title }}
                </div>
            </div>
        </div>

        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-chart-bar text-primary mr-1\"></i>
                    Asset Type
                </label>
                <div class=\"enhanced-display-field\">
                    <span class=\"badge\" style=\"background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 20px;\">
                        {{ article.assetTypeLabel }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Author and Publish Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user-edit text-primary mr-1\"></i>
                    Author
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.author ?? 'Capitol Academy Analyst' }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Publish Date
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.publishDate|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Excerpt (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-quote-left text-primary mr-1\"></i>
            Excerpt
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 80px;\">
            {{ article.excerpt|nl2br }}
        </div>
    </div>

    <!-- Content -->
    {% if article.content %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Content
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 200px; max-height: 400px; overflow-y: auto;\">
            {{ article.content|raw }}
        </div>
    </div>
    {% endif %}

    <!-- Images Row -->
    <div class=\"row\">
        <!-- Thumbnail Image -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-image text-primary mr-1\"></i>
                    Thumbnail Image
                </label>
                <div class=\"enhanced-display-field text-center\" style=\"min-height: 150px; display: flex; align-items: center; justify-content: center;\">
                    {% if article.thumbnailImage %}
                        <img src=\"{{ asset('uploads/market_analysis/' ~ article.thumbnailImage) }}\" 
                             alt=\"Thumbnail\" 
                             class=\"img-fluid rounded\" 
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                    {% else %}
                        <div class=\"text-muted\">
                            <i class=\"fas fa-image fa-3x mb-2\"></i>
                            <br>No thumbnail image
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Featured Image -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-star text-primary mr-1\"></i>
                    Featured Image
                </label>
                <div class=\"enhanced-display-field text-center\" style=\"min-height: 150px; display: flex; align-items: center; justify-content: center;\">
                    {% if article.featuredImage %}
                        <img src=\"{{ asset('uploads/market_analysis/' ~ article.featuredImage) }}\" 
                             alt=\"Featured\" 
                             class=\"img-fluid rounded\" 
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                    {% else %}
                        <div class=\"text-muted\">
                            <i class=\"fas fa-star fa-3x mb-2\"></i>
                            <br>No featured image
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Date and Views Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                    Publish Date
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.publishDate|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-eye text-primary mr-1\"></i>
                    Views
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.views|number_format }}
                </div>
            </div>
        </div>
    </div>

    <!-- Status and Created Date Row (Last Line) -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if article.isActive %}
                        <span class=\"badge bg-success\">Active</span>
                    {% else %}
                        <span class=\"badge bg-secondary\">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    {% if article.createdAt %}
                        {{ article.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Updated Date (if needed) -->
    {% if article.updatedAt and article.updatedAt != article.createdAt %}
    <div class=\"row\">
        <div class=\"col-md-12\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar-edit text-primary mr-1\"></i>
                    Last Updated
                </label>
                <div class=\"enhanced-display-field\">
                    {{ article.updatedAt|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printMarketAnalysisDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/market_analysis/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\market_analysis\\show.html.twig");
    }
}
