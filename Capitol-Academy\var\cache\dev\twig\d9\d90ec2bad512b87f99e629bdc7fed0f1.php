<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* contact/webinar.html.twig */
class __TwigTemplate_7dd104d3acdf8a543ff3476bdc4712f2 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "contact/webinar.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "contact/webinar.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Live Trading Webinars - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "    <!-- Section: Live Trading Webinar Contact Form -->
    <section class=\"py-5 position-relative\" style=\"background: url('";
        // line 7
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Background Any Question Contact Us.png"), "html", null, true);
        yield "') center/cover; height: calc(100vh - 80px);\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-6\">
                    <!-- Left Column: Title and Text -->
                    <div class=\"text-white pe-lg-5\" style=\"margin-right: 3rem;\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: white; font-family: 'Montserrat', sans-serif;\">Join Our Live Trading Webinars</h2>
                        <p class=\"lead mb-4\" style=\"font-size: 1.2rem; line-height: 1.8; color: white; font-family: 'Calibri', Arial, sans-serif;\">
                            Experience real-time trading sessions with our expert instructors. Learn advanced trading strategies, market analysis, and risk management techniques through interactive live webinars.
                        </p>
                        <div class=\"webinar-features mb-4\">
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-video text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Interactive live trading sessions</span>
                            </div>
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-chart-line text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Real-time market analysis</span>
                            </div>
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-users text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Q&A sessions with experts</span>
                            </div>
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-certificate text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Participation certificates</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=\"col-lg-5 offset-lg-1\">
                    <!-- Right Column: Contact Form (Narrower) -->
                    <div class=\"card border-0 shadow-lg\" style=\"background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.15);\">
                        <div class=\"card-body p-4\">
                            <h5 class=\"text-white mb-4 text-center\" style=\"font-family: 'Montserrat', sans-serif;\">Register Your Interest</h5>
                            
                            <!-- Flash Messages -->
                            ";
        // line 44
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 44, $this->source); })()), "flashes", ["success"], "method", false, false, false, 44));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 45
            yield "                                <div class=\"alert alert-success alert-dismissible fade show mb-3\" role=\"alert\" style=\"background: rgba(40, 167, 69, 0.9); border: none; color: white;\">
                                    <i class=\"fas fa-check-circle me-2\"></i>";
            // line 46
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
                                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"alert\"></button>
                                </div>
                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 50
        yield "
                            ";
        // line 51
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 51, $this->source); })()), "flashes", ["error"], "method", false, false, false, 51));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 52
            yield "                                <div class=\"alert alert-danger alert-dismissible fade show mb-3\" role=\"alert\" style=\"background: rgba(220, 53, 69, 0.9); border: none; color: white;\">
                                    <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 53
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
                                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"alert\"></button>
                                </div>
                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 57
        yield "
                            ";
        // line 58
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 58, $this->source); })()), 'form_start', ["attr" => ["class" => "needs-validation", "novalidate" => true]]);
        yield "
                                <div class=\"mb-3\">
                                    ";
        // line 60
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 60, $this->source); })()), "fullName", [], "any", false, false, false, 60), 'widget', ["attr" => ["class" => "form-control glassmorphism-input", "placeholder" => "Full Name *", "style" => "border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;"]]);
        // line 66
        yield "
                                    <div class=\"invalid-feedback\">Please provide your full name.</div>
                                </div>

                                <div class=\"mb-3\">
                                    ";
        // line 71
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 71, $this->source); })()), "email", [], "any", false, false, false, 71), 'widget', ["attr" => ["class" => "form-control glassmorphism-input", "placeholder" => "Email Address *", "style" => "border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;"]]);
        // line 77
        yield "
                                    <div class=\"invalid-feedback\">Please provide a valid email address.</div>
                                </div>

                                <div class=\"mb-3\">
                                    ";
        // line 82
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 82, $this->source); })()), "message", [], "any", false, false, false, 82), 'widget', ["attr" => ["class" => "form-control glassmorphism-input", "rows" => 4, "placeholder" => "Tell us about your trading experience and what you hope to learn from our webinars...", "style" => "border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px; resize: vertical;"]]);
        // line 89
        yield "
                                    <div class=\"invalid-feedback\">Please tell us about your interest in our webinars.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"privacyConsent\" name=\"privacy_consent\" checked required>
                                        <label class=\"form-check-label\" for=\"privacyConsent\" style=\"font-size: 0.85rem; line-height: 1.4; color: rgba(255, 255, 255, 0.8);\">
                                            I agree to receive information about Capitol Academy's live trading webinars and educational content. I understand I can unsubscribe at any time.
                                        </label>
                                        <div class=\"invalid-feedback\">Please accept our privacy terms.</div>
                                    </div>
                                </div>

                                <div class=\"d-grid\">
                                    <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 8px; padding: 12px 20px; font-weight: 600; transition: all 0.3s ease; font-family: 'Montserrat', sans-serif;\">
                                        <i class=\"fas fa-paper-plane me-2\"></i>Register Interest
                                    </button>
                                </div>
                            ";
        // line 108
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 108, $this->source); })()), 'form_end');
        yield "

                            <div class=\"text-center mt-3\">
                                <small style=\"color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;\">
                                    We'll contact you with upcoming webinar schedules and registration details.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
    /* Glassmorphism Input Styling */
    .glassmorphism-input::placeholder {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    .glassmorphism-input:focus {
        background: rgba(255, 255, 255, 0.15) !important;
        border-color: rgba(255, 255, 255, 0.4) !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
        color: white !important;
    }

    .glassmorphism-input:valid {
        color: white !important;
    }

    /* Button Hover Effects */
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
    }

    /* Feature Items Animation */
    .feature-item {
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        transform: translateX(10px);
    }

    /* Form Validation Styling */
    .was-validated .form-control:valid {
        border-color: rgba(40, 167, 69, 0.5);
        background-image: none;
    }

    .was-validated .form-control:invalid {
        border-color: rgba(220, 53, 69, 0.5);
        background-image: none;
    }

    .invalid-feedback {
        color: #ff6b6b;
        font-weight: 500;
    }

    /* Alert Auto-dismiss Animation */
    .alert {
        animation: slideInDown 0.5s ease-out;
    }

    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    </style>

    <script>
    // Form validation and enhancement
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const forms = document.querySelectorAll('.needs-validation');

        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // Auto-dismiss alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        // Add loading state to form submission
        const webinarForm = document.querySelector('form');
        if (webinarForm) {
            webinarForm.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type=\"submit\"]');
                if (submitBtn && this.checkValidity()) {
                    submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Registering...';
                    submitBtn.disabled = true;
                }
            });
        }
    });
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "contact/webinar.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  229 => 108,  208 => 89,  206 => 82,  199 => 77,  197 => 71,  190 => 66,  188 => 60,  183 => 58,  180 => 57,  170 => 53,  167 => 52,  163 => 51,  160 => 50,  150 => 46,  147 => 45,  143 => 44,  103 => 7,  100 => 6,  87 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Live Trading Webinars - Capitol Academy{% endblock %}

{% block body %}
    <!-- Section: Live Trading Webinar Contact Form -->
    <section class=\"py-5 position-relative\" style=\"background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: calc(100vh - 80px);\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-6\">
                    <!-- Left Column: Title and Text -->
                    <div class=\"text-white pe-lg-5\" style=\"margin-right: 3rem;\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: white; font-family: 'Montserrat', sans-serif;\">Join Our Live Trading Webinars</h2>
                        <p class=\"lead mb-4\" style=\"font-size: 1.2rem; line-height: 1.8; color: white; font-family: 'Calibri', Arial, sans-serif;\">
                            Experience real-time trading sessions with our expert instructors. Learn advanced trading strategies, market analysis, and risk management techniques through interactive live webinars.
                        </p>
                        <div class=\"webinar-features mb-4\">
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-video text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Interactive live trading sessions</span>
                            </div>
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-chart-line text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Real-time market analysis</span>
                            </div>
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-users text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Q&A sessions with experts</span>
                            </div>
                            <div class=\"feature-item mb-3 d-flex align-items-center\">
                                <i class=\"fas fa-certificate text-white me-3\" style=\"font-size: 1.2rem;\"></i>
                                <span style=\"color: white; font-family: 'Calibri', Arial, sans-serif;\">Participation certificates</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=\"col-lg-5 offset-lg-1\">
                    <!-- Right Column: Contact Form (Narrower) -->
                    <div class=\"card border-0 shadow-lg\" style=\"background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.15);\">
                        <div class=\"card-body p-4\">
                            <h5 class=\"text-white mb-4 text-center\" style=\"font-family: 'Montserrat', sans-serif;\">Register Your Interest</h5>
                            
                            <!-- Flash Messages -->
                            {% for message in app.flashes('success') %}
                                <div class=\"alert alert-success alert-dismissible fade show mb-3\" role=\"alert\" style=\"background: rgba(40, 167, 69, 0.9); border: none; color: white;\">
                                    <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
                                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"alert\"></button>
                                </div>
                            {% endfor %}

                            {% for message in app.flashes('error') %}
                                <div class=\"alert alert-danger alert-dismissible fade show mb-3\" role=\"alert\" style=\"background: rgba(220, 53, 69, 0.9); border: none; color: white;\">
                                    <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
                                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"alert\"></button>
                                </div>
                            {% endfor %}

                            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                                <div class=\"mb-3\">
                                    {{ form_widget(form.fullName, {
                                        'attr': {
                                            'class': 'form-control glassmorphism-input',
                                            'placeholder': 'Full Name *',
                                            'style': 'border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;'
                                        }
                                    }) }}
                                    <div class=\"invalid-feedback\">Please provide your full name.</div>
                                </div>

                                <div class=\"mb-3\">
                                    {{ form_widget(form.email, {
                                        'attr': {
                                            'class': 'form-control glassmorphism-input',
                                            'placeholder': 'Email Address *',
                                            'style': 'border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;'
                                        }
                                    }) }}
                                    <div class=\"invalid-feedback\">Please provide a valid email address.</div>
                                </div>

                                <div class=\"mb-3\">
                                    {{ form_widget(form.message, {
                                        'attr': {
                                            'class': 'form-control glassmorphism-input',
                                            'rows': 4,
                                            'placeholder': 'Tell us about your trading experience and what you hope to learn from our webinars...',
                                            'style': 'border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px; resize: vertical;'
                                        }
                                    }) }}
                                    <div class=\"invalid-feedback\">Please tell us about your interest in our webinars.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"privacyConsent\" name=\"privacy_consent\" checked required>
                                        <label class=\"form-check-label\" for=\"privacyConsent\" style=\"font-size: 0.85rem; line-height: 1.4; color: rgba(255, 255, 255, 0.8);\">
                                            I agree to receive information about Capitol Academy's live trading webinars and educational content. I understand I can unsubscribe at any time.
                                        </label>
                                        <div class=\"invalid-feedback\">Please accept our privacy terms.</div>
                                    </div>
                                </div>

                                <div class=\"d-grid\">
                                    <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 8px; padding: 12px 20px; font-weight: 600; transition: all 0.3s ease; font-family: 'Montserrat', sans-serif;\">
                                        <i class=\"fas fa-paper-plane me-2\"></i>Register Interest
                                    </button>
                                </div>
                            {{ form_end(form) }}

                            <div class=\"text-center mt-3\">
                                <small style=\"color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;\">
                                    We'll contact you with upcoming webinar schedules and registration details.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
    /* Glassmorphism Input Styling */
    .glassmorphism-input::placeholder {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    .glassmorphism-input:focus {
        background: rgba(255, 255, 255, 0.15) !important;
        border-color: rgba(255, 255, 255, 0.4) !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
        color: white !important;
    }

    .glassmorphism-input:valid {
        color: white !important;
    }

    /* Button Hover Effects */
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
    }

    /* Feature Items Animation */
    .feature-item {
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        transform: translateX(10px);
    }

    /* Form Validation Styling */
    .was-validated .form-control:valid {
        border-color: rgba(40, 167, 69, 0.5);
        background-image: none;
    }

    .was-validated .form-control:invalid {
        border-color: rgba(220, 53, 69, 0.5);
        background-image: none;
    }

    .invalid-feedback {
        color: #ff6b6b;
        font-weight: 500;
    }

    /* Alert Auto-dismiss Animation */
    .alert {
        animation: slideInDown 0.5s ease-out;
    }

    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    </style>

    <script>
    // Form validation and enhancement
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const forms = document.querySelectorAll('.needs-validation');

        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // Auto-dismiss alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        // Add loading state to form submission
        const webinarForm = document.querySelector('form');
        if (webinarForm) {
            webinarForm.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type=\"submit\"]');
                if (submitBtn && this.checkValidity()) {
                    submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Registering...';
                    submitBtn.disabled = true;
                }
            });
        }
    });
    </script>
{% endblock %}
", "contact/webinar.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\contact\\webinar.html.twig");
    }
}
