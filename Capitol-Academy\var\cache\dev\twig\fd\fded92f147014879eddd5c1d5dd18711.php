<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* user/orders/index.html.twig */
class __TwigTemplate_841516ca79c857900a3f0633db518c9e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "user/orders/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "user/orders/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "My Orders - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 6
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        .orders-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .page-header {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
        }

        .stats-cards {
            margin-bottom: 40px;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 1.5rem;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .orders-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .orders-header {
            background: #f8f9fa;
            padding: 25px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .orders-content {
            padding: 30px;
        }

        .order-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .order-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #011a2d;
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .order-number {
            font-size: 1.1rem;
            font-weight: 600;
            color: #011a2d;
        }

        .order-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .order-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: #a90418;
        }

        .order-items {
            margin-bottom: 15px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 3px;
            font-size: 0.95rem;
        }

        .item-type {
            background: #e9ecef;
            color: #6c757d;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            text-transform: uppercase;
        }

        .item-price {
            font-weight: 600;
            color: #495057;
        }

        .order-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 6px 15px;
            font-size: 0.85rem;
        }

        .empty-orders {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-orders i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #dee2e6;
        }

        @media (max-width: 768px) {
            .orders-section {
                padding: 40px 0;
            }
            
            .page-header {
                padding: 30px 0;
            }
            
            .orders-content {
                padding: 20px;
            }
            
            .order-card {
                padding: 20px;
            }
            
            .order-header,
            .order-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .stats-cards .col-md-3 {
                margin-bottom: 20px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 241
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 242
        yield "    <!-- Page Header -->
    <section class=\"page-header\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"mb-3\">
                        <i class=\"fas fa-shopping-bag me-3\"></i>My Orders
                    </h1>
                    <p class=\"lead mb-0\">Track your purchases and access your content</p>
                </div>
                <div class=\"col-lg-4 text-end\">
                    <a href=\"";
        // line 253
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos_list");
        yield "\" class=\"btn btn-light btn-lg\">
                        <i class=\"fas fa-plus me-2\"></i>Browse More Videos
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class=\"orders-section\">
        <div class=\"container\">
            <!-- Statistics Cards -->
            <div class=\"stats-cards\">
                <div class=\"row\">
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-shopping-cart\"></i>
                            </div>
                            <div class=\"stats-number\">";
        // line 271
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 271, $this->source); })())), "html", null, true);
        yield "</div>
                            <div class=\"stats-label\">Total Orders</div>
                        </div>
                    </div>
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-video\"></i>
                            </div>
                            <div class=\"stats-number\">";
        // line 280
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["access_stats"]) || array_key_exists("access_stats", $context) ? $context["access_stats"] : (function () { throw new RuntimeError('Variable "access_stats" does not exist.', 280, $this->source); })()), "total_videos_accessed", [], "any", false, false, false, 280), "html", null, true);
        yield "</div>
                            <div class=\"stats-label\">Videos Owned</div>
                        </div>
                    </div>
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-play-circle\"></i>
                            </div>
                            <div class=\"stats-number\">";
        // line 289
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["access_stats"]) || array_key_exists("access_stats", $context) ? $context["access_stats"] : (function () { throw new RuntimeError('Variable "access_stats" does not exist.', 289, $this->source); })()), "total_video_views", [], "any", false, false, false, 289), "html", null, true);
        yield "</div>
                            <div class=\"stats-label\">Total Views</div>
                        </div>
                    </div>
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-graduation-cap\"></i>
                            </div>
                            <div class=\"stats-number\">";
        // line 298
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["access_stats"]) || array_key_exists("access_stats", $context) ? $context["access_stats"] : (function () { throw new RuntimeError('Variable "access_stats" does not exist.', 298, $this->source); })()), "purchased_courses", [], "any", false, false, false, 298), "html", null, true);
        yield "</div>
                            <div class=\"stats-label\">Courses Enrolled</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders List -->
            <div class=\"orders-container\">
                <div class=\"orders-header\">
                    <h4 class=\"mb-0\">
                        <i class=\"fas fa-list me-2\"></i>Order History
                    </h4>
                </div>

                <div class=\"orders-content\">
                    ";
        // line 314
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 314, $this->source); })())) > 0)) {
            // line 315
            yield "                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 315, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["order"]) {
                // line 316
                yield "                            <div class=\"order-card\">
                                <div class=\"order-header\">
                                    <div class=\"order-number\">
                                        Order #";
                // line 319
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "orderNumber", [], "any", false, false, false, 319), "html", null, true);
                yield "
                                    </div>
                                    <div class=\"order-status status-";
                // line 321
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "paymentStatus", [], "any", false, false, false, 321), "html", null, true);
                yield "\">
                                        ";
                // line 322
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "statusLabel", [], "any", false, false, false, 322), "html", null, true);
                yield "
                                    </div>
                                </div>

                                <div class=\"order-meta\">
                                    <div class=\"order-date\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        ";
                // line 329
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "createdAt", [], "any", false, false, false, 329), "M d, Y \\a\\t g:i A"), "html", null, true);
                yield "
                                    </div>
                                    <div class=\"order-total\">
                                        ";
                // line 332
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "formattedTotalPrice", [], "any", false, false, false, 332), "html", null, true);
                yield "
                                    </div>
                                </div>

                                <div class=\"order-items\">
                                    ";
                // line 337
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "items", [], "any", false, false, false, 337));
                foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
                    // line 338
                    yield "                                        <div class=\"order-item\">
                                            <div class=\"item-info\">
                                                <h6>";
                    // line 340
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 340), "html", null, true);
                    yield "</h6>
                                                <span class=\"item-type\">";
                    // line 341
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), Twig\Extension\CoreExtension::replace(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 341), ["_" => " "])), "html", null, true);
                    yield "</span>
                                                ";
                    // line 342
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 342) > 1)) {
                        // line 343
                        yield "                                                    <small class=\"text-muted ms-2\">Qty: ";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 343), "html", null, true);
                        yield "</small>
                                                ";
                    }
                    // line 345
                    yield "                                            </div>
                                            <div class=\"item-price\">
                                                \$";
                    // line 347
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "subtotal", [], "any", false, false, false, 347), 2), "html", null, true);
                    yield "
                                            </div>
                                        </div>
                                    ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['item'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 351
                yield "                                </div>

                                <div class=\"order-actions\">
                                    <a href=\"";
                // line 354
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_order_show", ["orderNumber" => CoreExtension::getAttribute($this->env, $this->source, $context["order"], "orderNumber", [], "any", false, false, false, 354)]), "html", null, true);
                yield "\" 
                                       class=\"btn btn-outline-primary btn-sm\">
                                        <i class=\"fas fa-eye me-1\"></i>View Details
                                    </a>
                                    
                                    ";
                // line 359
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["order"], "isCompleted", [], "any", false, false, false, 359)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 360
                    yield "                                        <a href=\"";
                    yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos_list");
                    yield "\"
                                           class=\"btn btn-primary btn-sm\">
                                            <i class=\"fas fa-play me-1\"></i>Access Content
                                        </a>
                                    ";
                }
                // line 365
                yield "                                    
                                    ";
                // line 366
                if (((CoreExtension::getAttribute($this->env, $this->source, $context["order"], "paymentGateway", [], "any", false, false, false, 366) == "paypal") && CoreExtension::getAttribute($this->env, $this->source, $context["order"], "paypalTransactionId", [], "any", false, false, false, 366))) {
                    // line 367
                    yield "                                        <small class=\"text-muted align-self-center\">
                                            PayPal ID: ";
                    // line 368
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["order"], "paypalTransactionId", [], "any", false, false, false, 368), "html", null, true);
                    yield "
                                        </small>
                                    ";
                }
                // line 371
                yield "                                </div>
                            </div>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['order'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 374
            yield "                    ";
        } else {
            // line 375
            yield "                        <div class=\"empty-orders\">
                            <i class=\"fas fa-shopping-bag\"></i>
                            <h3>No Orders Yet</h3>
                            <p class=\"mb-4\">You haven't made any purchases yet. Start exploring our premium content!</p>
                            <a href=\"";
            // line 379
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos_list");
            yield "\" class=\"btn btn-primary btn-lg\">
                                <i class=\"fas fa-video me-2\"></i>Browse Premium Videos
                            </a>
                        </div>
                    ";
        }
        // line 384
        yield "                </div>
            </div>
        </div>
    </section>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 390
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 391
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        // Add hover effects to order cards
        document.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "user/orders/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  624 => 391,  611 => 390,  596 => 384,  588 => 379,  582 => 375,  579 => 374,  571 => 371,  565 => 368,  562 => 367,  560 => 366,  557 => 365,  548 => 360,  546 => 359,  538 => 354,  533 => 351,  523 => 347,  519 => 345,  513 => 343,  511 => 342,  507 => 341,  503 => 340,  499 => 338,  495 => 337,  487 => 332,  481 => 329,  471 => 322,  467 => 321,  462 => 319,  457 => 316,  452 => 315,  450 => 314,  431 => 298,  419 => 289,  407 => 280,  395 => 271,  374 => 253,  361 => 242,  348 => 241,  102 => 6,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}My Orders - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .orders-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .page-header {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
        }

        .stats-cards {
            margin-bottom: 40px;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 1.5rem;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .orders-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .orders-header {
            background: #f8f9fa;
            padding: 25px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .orders-content {
            padding: 30px;
        }

        .order-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .order-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #011a2d;
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .order-number {
            font-size: 1.1rem;
            font-weight: 600;
            color: #011a2d;
        }

        .order-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .order-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: #a90418;
        }

        .order-items {
            margin-bottom: 15px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 3px;
            font-size: 0.95rem;
        }

        .item-type {
            background: #e9ecef;
            color: #6c757d;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            text-transform: uppercase;
        }

        .item-price {
            font-weight: 600;
            color: #495057;
        }

        .order-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 6px 15px;
            font-size: 0.85rem;
        }

        .empty-orders {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-orders i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #dee2e6;
        }

        @media (max-width: 768px) {
            .orders-section {
                padding: 40px 0;
            }
            
            .page-header {
                padding: 30px 0;
            }
            
            .orders-content {
                padding: 20px;
            }
            
            .order-card {
                padding: 20px;
            }
            
            .order-header,
            .order-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .stats-cards .col-md-3 {
                margin-bottom: 20px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <!-- Page Header -->
    <section class=\"page-header\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"mb-3\">
                        <i class=\"fas fa-shopping-bag me-3\"></i>My Orders
                    </h1>
                    <p class=\"lead mb-0\">Track your purchases and access your content</p>
                </div>
                <div class=\"col-lg-4 text-end\">
                    <a href=\"{{ path('app_videos_list') }}\" class=\"btn btn-light btn-lg\">
                        <i class=\"fas fa-plus me-2\"></i>Browse More Videos
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class=\"orders-section\">
        <div class=\"container\">
            <!-- Statistics Cards -->
            <div class=\"stats-cards\">
                <div class=\"row\">
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-shopping-cart\"></i>
                            </div>
                            <div class=\"stats-number\">{{ orders|length }}</div>
                            <div class=\"stats-label\">Total Orders</div>
                        </div>
                    </div>
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-video\"></i>
                            </div>
                            <div class=\"stats-number\">{{ access_stats.total_videos_accessed }}</div>
                            <div class=\"stats-label\">Videos Owned</div>
                        </div>
                    </div>
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-play-circle\"></i>
                            </div>
                            <div class=\"stats-number\">{{ access_stats.total_video_views }}</div>
                            <div class=\"stats-label\">Total Views</div>
                        </div>
                    </div>
                    <div class=\"col-md-3\">
                        <div class=\"stats-card\">
                            <div class=\"stats-icon\">
                                <i class=\"fas fa-graduation-cap\"></i>
                            </div>
                            <div class=\"stats-number\">{{ access_stats.purchased_courses }}</div>
                            <div class=\"stats-label\">Courses Enrolled</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders List -->
            <div class=\"orders-container\">
                <div class=\"orders-header\">
                    <h4 class=\"mb-0\">
                        <i class=\"fas fa-list me-2\"></i>Order History
                    </h4>
                </div>

                <div class=\"orders-content\">
                    {% if orders|length > 0 %}
                        {% for order in orders %}
                            <div class=\"order-card\">
                                <div class=\"order-header\">
                                    <div class=\"order-number\">
                                        Order #{{ order.orderNumber }}
                                    </div>
                                    <div class=\"order-status status-{{ order.paymentStatus }}\">
                                        {{ order.statusLabel }}
                                    </div>
                                </div>

                                <div class=\"order-meta\">
                                    <div class=\"order-date\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        {{ order.createdAt|date('M d, Y \\\\a\\\\t g:i A') }}
                                    </div>
                                    <div class=\"order-total\">
                                        {{ order.formattedTotalPrice }}
                                    </div>
                                </div>

                                <div class=\"order-items\">
                                    {% for item in order.items %}
                                        <div class=\"order-item\">
                                            <div class=\"item-info\">
                                                <h6>{{ item.title }}</h6>
                                                <span class=\"item-type\">{{ item.type|replace({'_': ' '})|title }}</span>
                                                {% if item.quantity > 1 %}
                                                    <small class=\"text-muted ms-2\">Qty: {{ item.quantity }}</small>
                                                {% endif %}
                                            </div>
                                            <div class=\"item-price\">
                                                \${{ item.subtotal|number_format(2) }}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                <div class=\"order-actions\">
                                    <a href=\"{{ path('app_user_order_show', {orderNumber: order.orderNumber}) }}\" 
                                       class=\"btn btn-outline-primary btn-sm\">
                                        <i class=\"fas fa-eye me-1\"></i>View Details
                                    </a>
                                    
                                    {% if order.isCompleted %}
                                        <a href=\"{{ path('app_videos_list') }}\"
                                           class=\"btn btn-primary btn-sm\">
                                            <i class=\"fas fa-play me-1\"></i>Access Content
                                        </a>
                                    {% endif %}
                                    
                                    {% if order.paymentGateway == 'paypal' and order.paypalTransactionId %}
                                        <small class=\"text-muted align-self-center\">
                                            PayPal ID: {{ order.paypalTransactionId }}
                                        </small>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class=\"empty-orders\">
                            <i class=\"fas fa-shopping-bag\"></i>
                            <h3>No Orders Yet</h3>
                            <p class=\"mb-4\">You haven't made any purchases yet. Start exploring our premium content!</p>
                            <a href=\"{{ path('app_videos_list') }}\" class=\"btn btn-primary btn-lg\">
                                <i class=\"fas fa-video me-2\"></i>Browse Premium Videos
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Add hover effects to order cards
        document.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
{% endblock %}
", "user/orders/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\user\\orders\\index.html.twig");
    }
}
