<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* onsite_course/show.html.twig */
class __TwigTemplate_267900af648667fb4c7db5b8534618c4 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "onsite_course/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "onsite_course/show.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "html", null, true);
        yield " - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid\">
    <!-- Course Header -->
    <section class=\"course-header py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <div class=\"mb-3\">
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2 me-2\">";
        // line 13
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 13, $this->source); })()), "code", [], "any", false, false, false, 13), "html", null, true);
        yield "</span>
                        ";
        // line 14
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 14, $this->source); })()), "level", [], "any", false, false, false, 14)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 15
            yield "                            <span class=\"badge bg-warning text-dark fs-6 px-3 py-2 me-2\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 15, $this->source); })()), "level", [], "any", false, false, false, 15), "html", null, true);
            yield "</span>
                        ";
        }
        // line 17
        yield "                        <span class=\"badge bg-success fs-6 px-3 py-2\">";
        yield ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 17, $this->source); })()), "category", [], "any", false, false, false, 17)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 17, $this->source); })()), "category", [], "any", false, false, false, 17), "html", null, true)) : ("General"));
        yield "</span>
                    </div>
                    <h1 class=\"display-5 fw-bold mb-3\">";
        // line 19
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 19, $this->source); })()), "title", [], "any", false, false, false, 19), "html", null, true);
        yield "</h1>
                    ";
        // line 20
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 20, $this->source); })()), "description", [], "any", false, false, false, 20)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 21
            yield "                        <p class=\"lead mb-4\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 21, $this->source); })()), "description", [], "any", false, false, false, 21), "html", null, true);
            yield "</p>
                    ";
        }
        // line 23
        yield "                    
                    <!-- Course Stats -->
                    <div class=\"row text-center\">
                        ";
        // line 26
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 26, $this->source); })()), "duration", [], "any", false, false, false, 26)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 27
            yield "                            <div class=\"col-md-3 col-6 mb-3\">
                                <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                    <i class=\"fas fa-clock fs-3 mb-2\"></i>
                                    <div class=\"fw-bold\">";
            // line 30
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 30, $this->source); })()), "duration", [], "any", false, false, false, 30), "html", null, true);
            yield " min</div>
                                    <small>Duration</small>
                                </div>
                            </div>
                        ";
        }
        // line 35
        yield "                        <div class=\"col-md-3 col-6 mb-3\">
                            <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                <i class=\"fas fa-dollar-sign fs-3 mb-2\"></i>
                                <div class=\"fw-bold\">\$";
        // line 38
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 38, $this->source); })()), "price", [], "any", false, false, false, 38), "html", null, true);
        yield "</div>
                                <small>Price</small>
                            </div>
                        </div>
                        <div class=\"col-md-3 col-6 mb-3\">
                            <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                <i class=\"fas fa-users fs-3 mb-2\"></i>
                                <div class=\"fw-bold\">";
        // line 45
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 45, $this->source); })()), "enrolledCount", [], "any", false, false, false, 45), "html", null, true);
        yield "</div>
                                <small>Students</small>
                            </div>
                        </div>
                        <div class=\"col-md-3 col-6 mb-3\">
                            <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                <i class=\"fas fa-eye fs-3 mb-2\"></i>
                                <div class=\"fw-bold\">";
        // line 52
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 52, $this->source); })()), "viewCount", [], "any", false, false, false, 52), "html", null, true);
        yield "</div>
                                <small>Views</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    ";
        // line 59
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 59, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 59)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 60
            yield "                        <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 60, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 60), "html", null, true);
            yield "\" alt=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 60, $this->source); })()), "title", [], "any", false, false, false, 60), "html", null, true);
            yield "\" class=\"img-fluid rounded shadow\" style=\"max-width: 300px;\">
                    ";
        } else {
            // line 62
            yield "                        <div class=\"bg-white bg-opacity-10 rounded p-5\">
                            <i class=\"fas fa-graduation-cap\" style=\"font-size: 6rem; opacity: 0.7;\"></i>
                        </div>
                    ";
        }
        // line 66
        yield "                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- Main Content -->
                <div class=\"col-lg-8\">
                    <!-- Learning Outcomes -->
                    ";
        // line 78
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 78, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 78) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 78, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 78)) > 0))) {
            // line 79
            yield "                        <div class=\"card mb-4 border-0 shadow-sm\">
                            <div class=\"card-header bg-primary text-white\">
                                <h4 class=\"mb-0\">
                                    <i class=\"fas fa-bullseye me-2\"></i>What You'll Learn
                                </h4>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"row\">
                                    ";
            // line 87
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 87, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 87));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 88
                yield "                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-check-circle text-success me-3 mt-1\"></i>
                                                <span>";
                // line 91
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "</span>
                                            </div>
                                        </div>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 95
            yield "                                </div>
                            </div>
                        </div>
                    ";
        }
        // line 99
        yield "
                    <!-- Course Features -->
                    ";
        // line 101
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 101, $this->source); })()), "features", [], "any", false, false, false, 101) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 101, $this->source); })()), "features", [], "any", false, false, false, 101)) > 0))) {
            // line 102
            yield "                        <div class=\"card mb-4 border-0 shadow-sm\">
                            <div class=\"card-header bg-success text-white\">
                                <h4 class=\"mb-0\">
                                    <i class=\"fas fa-star me-2\"></i>Course Features
                                </h4>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"row\">
                                    ";
            // line 110
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 110, $this->source); })()), "features", [], "any", false, false, false, 110));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 111
                yield "                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-star text-warning me-3 mt-1\"></i>
                                                <span>";
                // line 114
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "</span>
                                            </div>
                                        </div>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 118
            yield "                                </div>
                            </div>
                        </div>
                    ";
        }
        // line 122
        yield "
                    <!-- Course Modules -->
                    ";
        // line 124
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 124, $this->source); })()), "hasModules", [], "any", false, false, false, 124) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["modules"]) || array_key_exists("modules", $context) ? $context["modules"] : (function () { throw new RuntimeError('Variable "modules" does not exist.', 124, $this->source); })())) > 0))) {
            // line 125
            yield "                        <div class=\"card mb-4 border-0 shadow-sm\">
                            <div class=\"card-header bg-info text-white\">
                                <h4 class=\"mb-0\">
                                    <i class=\"fas fa-list me-2\"></i>Course Modules
                                </h4>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"accordion\" id=\"moduleAccordion\">
                                    ";
            // line 133
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["modules"]) || array_key_exists("modules", $context) ? $context["modules"] : (function () { throw new RuntimeError('Variable "modules" does not exist.', 133, $this->source); })()));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                // line 134
                yield "                                        <div class=\"accordion-item\">
                                            <h2 class=\"accordion-header\" id=\"heading";
                // line 135
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 135), "html", null, true);
                yield "\">
                                                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#collapse";
                // line 136
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 136), "html", null, true);
                yield "\" aria-expanded=\"false\" aria-controls=\"collapse";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 136), "html", null, true);
                yield "\">
                                                    <div class=\"d-flex justify-content-between w-100 me-3\">
                                                        <span class=\"fw-bold\">";
                // line 138
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 138), "html", null, true);
                yield "</span>
                                                        <div>
                                                            ";
                // line 140
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["module"], "duration", [], "any", false, false, false, 140)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 141
                    yield "                                                                <span class=\"badge bg-primary me-2\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "duration", [], "any", false, false, false, 141), "html", null, true);
                    yield " min</span>
                                                            ";
                }
                // line 143
                yield "                                                            ";
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "price", [], "any", false, false, false, 143) && (CoreExtension::getAttribute($this->env, $this->source, $context["module"], "price", [], "any", false, false, false, 143) > 0))) {
                    // line 144
                    yield "                                                                <span class=\"badge bg-success\">\$";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "price", [], "any", false, false, false, 144), "html", null, true);
                    yield "</span>
                                                            ";
                }
                // line 146
                yield "                                                        </div>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id=\"collapse";
                // line 150
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 150), "html", null, true);
                yield "\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 150), "html", null, true);
                yield "\" data-bs-parent=\"#moduleAccordion\">
                                                <div class=\"accordion-body\">
                                                    ";
                // line 152
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 152)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 153
                    yield "                                                        <p class=\"mb-3\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 153), "html", null, true);
                    yield "</p>
                                                    ";
                }
                // line 155
                yield "                                                    
                                                    ";
                // line 156
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 156) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 156)) > 0))) {
                    // line 157
                    yield "                                                        <h6 class=\"fw-bold mb-2\">Learning Outcomes:</h6>
                                                        <ul class=\"list-unstyled mb-3\">
                                                            ";
                    // line 159
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 159));
                    foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                        // line 160
                        yield "                                                                <li class=\"mb-1\">
                                                                    <i class=\"fas fa-check text-success me-2\"></i>";
                        // line 161
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                        yield "
                                                                </li>
                                                            ";
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 164
                    yield "                                                        </ul>
                                                    ";
                }
                // line 166
                yield "                                                </div>
                                            </div>
                                        </div>
                                    ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['module'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 170
            yield "                                </div>
                            </div>
                        </div>
                    ";
        }
        // line 174
        yield "                </div>

                <!-- Sidebar -->
                <div class=\"col-lg-4\">
                    <!-- Enrollment Card -->
                    <div class=\"card mb-4 border-0 shadow-sm sticky-top\" style=\"top: 2rem;\">
                        <div class=\"card-body text-center\">
                            <div class=\"mb-3\">
                                <div class=\"display-6 fw-bold text-success\">\$";
        // line 182
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 182, $this->source); })()), "price", [], "any", false, false, false, 182), "html", null, true);
        yield "</div>
                                <small class=\"text-muted\">One-time payment</small>
                            </div>
                            
                            ";
        // line 186
        if ((($tmp = (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 186, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 187
            yield "                                ";
            if ((($tmp = (isset($context["is_enrolled"]) || array_key_exists("is_enrolled", $context) ? $context["is_enrolled"] : (function () { throw new RuntimeError('Variable "is_enrolled" does not exist.', 187, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 188
                yield "                                    <button class=\"btn btn-success btn-lg w-100 mb-3\" disabled>
                                        <i class=\"fas fa-check me-2\"></i>Enrolled
                                    </button>
                                ";
            } else {
                // line 192
                yield "                                    <button class=\"btn btn-primary btn-lg w-100 mb-3\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: none;\">
                                        <i class=\"fas fa-shopping-cart me-2\"></i>Enroll Now
                                    </button>
                                ";
            }
            // line 196
            yield "                            ";
        } else {
            // line 197
            yield "                                <a href=\"";
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
            yield "\" class=\"btn btn-primary btn-lg w-100 mb-3\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: none;\">
                                    <i class=\"fas fa-sign-in-alt me-2\"></i>Login to Enroll
                                </a>
                            ";
        }
        // line 201
        yield "
                            <div class=\"text-center\">
                                <small class=\"text-muted\">
                                    <i class=\"fas fa-shield-alt me-1\"></i>30-day money-back guarantee
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Course Info -->
                    <div class=\"card mb-4 border-0 shadow-sm\">
                        <div class=\"card-header bg-light\">
                            <h5 class=\"mb-0\">
                                <i class=\"fas fa-info-circle me-2\"></i>Course Information
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"row mb-2\">
                                <div class=\"col-6\"><strong>Course Code:</strong></div>
                                <div class=\"col-6\">";
        // line 220
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 220, $this->source); })()), "code", [], "any", false, false, false, 220), "html", null, true);
        yield "</div>
                            </div>
                            <div class=\"row mb-2\">
                                <div class=\"col-6\"><strong>Category:</strong></div>
                                <div class=\"col-6\">";
        // line 224
        yield ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 224, $this->source); })()), "category", [], "any", false, false, false, 224)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 224, $this->source); })()), "category", [], "any", false, false, false, 224), "html", null, true)) : ("General"));
        yield "</div>
                            </div>
                            ";
        // line 226
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 226, $this->source); })()), "level", [], "any", false, false, false, 226)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 227
            yield "                                <div class=\"row mb-2\">
                                    <div class=\"col-6\"><strong>Level:</strong></div>
                                    <div class=\"col-6\">";
            // line 229
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 229, $this->source); })()), "level", [], "any", false, false, false, 229), "html", null, true);
            yield "</div>
                                </div>
                            ";
        }
        // line 232
        yield "                            ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 232, $this->source); })()), "duration", [], "any", false, false, false, 232)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 233
            yield "                                <div class=\"row mb-2\">
                                    <div class=\"col-6\"><strong>Duration:</strong></div>
                                    <div class=\"col-6\">";
            // line 235
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 235, $this->source); })()), "duration", [], "any", false, false, false, 235), "html", null, true);
            yield " minutes</div>
                                </div>
                            ";
        }
        // line 238
        yield "                            <div class=\"row mb-2\">
                                <div class=\"col-6\"><strong>Students:</strong></div>
                                <div class=\"col-6\">";
        // line 240
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 240, $this->source); })()), "enrolledCount", [], "any", false, false, false, 240), "html", null, true);
        yield "</div>
                            </div>
                            <div class=\"row\">
                                <div class=\"col-6\"><strong>Last Updated:</strong></div>
                                <div class=\"col-6\">";
        // line 244
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 244, $this->source); })()), "updatedAt", [], "any", false, false, false, 244), "M Y"), "html", null, true);
        yield "</div>
                            </div>
                        </div>
                    </div>

                    <!-- Share Course -->
                    <div class=\"card border-0 shadow-sm\">
                        <div class=\"card-header bg-light\">
                            <h5 class=\"mb-0\">
                                <i class=\"fas fa-share-alt me-2\"></i>Share This Course
                            </h5>
                        </div>
                        <div class=\"card-body text-center\">
                            <div class=\"d-flex justify-content-center gap-2\">
                                <a href=\"#\" class=\"btn btn-outline-primary btn-sm\">
                                    <i class=\"fab fa-facebook-f\"></i>
                                </a>
                                <a href=\"#\" class=\"btn btn-outline-info btn-sm\">
                                    <i class=\"fab fa-twitter\"></i>
                                </a>
                                <a href=\"#\" class=\"btn btn-outline-primary btn-sm\">
                                    <i class=\"fab fa-linkedin-in\"></i>
                                </a>
                                <a href=\"#\" class=\"btn btn-outline-success btn-sm\">
                                    <i class=\"fab fa-whatsapp\"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Courses -->
    <section class=\"py-5 bg-light\">
        <div class=\"container\">
            <h2 class=\"text-center mb-5\">Other Onsite Courses</h2>
            <div class=\"row\">
                <div class=\"col-md-4 mb-4\">
                    <div class=\"card h-100 border-0 shadow-sm\">
                        <div class=\"card-body text-center\">
                            <i class=\"fas fa-chart-line text-primary mb-3\" style=\"font-size: 3rem;\"></i>
                            <h5>Technical Analysis</h5>
                            <p class=\"text-muted\">Master chart patterns and technical indicators</p>
                            <a href=\"#\" class=\"btn btn-outline-primary\">Learn More</a>
                        </div>
                    </div>
                </div>
                <div class=\"col-md-4 mb-4\">
                    <div class=\"card h-100 border-0 shadow-sm\">
                        <div class=\"card-body text-center\">
                            <i class=\"fas fa-coins text-warning mb-3\" style=\"font-size: 3rem;\"></i>
                            <h5>Risk Management</h5>
                            <p class=\"text-muted\">Learn to protect your capital and manage risk</p>
                            <a href=\"#\" class=\"btn btn-outline-primary\">Learn More</a>
                        </div>
                    </div>
                </div>
                <div class=\"col-md-4 mb-4\">
                    <div class=\"card h-100 border-0 shadow-sm\">
                        <div class=\"card-body text-center\">
                            <i class=\"fas fa-brain text-success mb-3\" style=\"font-size: 3rem;\"></i>
                            <h5>Trading Psychology</h5>
                            <p class=\"text-muted\">Develop the right mindset for successful trading</p>
                            <a href=\"#\" class=\"btn btn-outline-primary\">Learn More</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.course-header {
    position: relative;
    overflow: hidden;
}

.course-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-pattern.png') repeat;
    opacity: 0.1;
    z-index: 0;
}

.course-header .container {
    position: relative;
    z-index: 1;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.sticky-top {
    position: sticky !important;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "onsite_course/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  559 => 244,  552 => 240,  548 => 238,  542 => 235,  538 => 233,  535 => 232,  529 => 229,  525 => 227,  523 => 226,  518 => 224,  511 => 220,  490 => 201,  482 => 197,  479 => 196,  473 => 192,  467 => 188,  464 => 187,  462 => 186,  455 => 182,  445 => 174,  439 => 170,  422 => 166,  418 => 164,  409 => 161,  406 => 160,  402 => 159,  398 => 157,  396 => 156,  393 => 155,  387 => 153,  385 => 152,  378 => 150,  372 => 146,  366 => 144,  363 => 143,  357 => 141,  355 => 140,  350 => 138,  343 => 136,  339 => 135,  336 => 134,  319 => 133,  309 => 125,  307 => 124,  303 => 122,  297 => 118,  287 => 114,  282 => 111,  278 => 110,  268 => 102,  266 => 101,  262 => 99,  256 => 95,  246 => 91,  241 => 88,  237 => 87,  227 => 79,  225 => 78,  211 => 66,  205 => 62,  197 => 60,  195 => 59,  185 => 52,  175 => 45,  165 => 38,  160 => 35,  152 => 30,  147 => 27,  145 => 26,  140 => 23,  134 => 21,  132 => 20,  128 => 19,  122 => 17,  116 => 15,  114 => 14,  110 => 13,  101 => 6,  88 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}{{ course.title }} - Capitol Academy{% endblock %}

{% block body %}
<div class=\"container-fluid\">
    <!-- Course Header -->
    <section class=\"course-header py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <div class=\"mb-3\">
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2 me-2\">{{ course.code }}</span>
                        {% if course.level %}
                            <span class=\"badge bg-warning text-dark fs-6 px-3 py-2 me-2\">{{ course.level }}</span>
                        {% endif %}
                        <span class=\"badge bg-success fs-6 px-3 py-2\">{{ course.category ?: 'General' }}</span>
                    </div>
                    <h1 class=\"display-5 fw-bold mb-3\">{{ course.title }}</h1>
                    {% if course.description %}
                        <p class=\"lead mb-4\">{{ course.description }}</p>
                    {% endif %}
                    
                    <!-- Course Stats -->
                    <div class=\"row text-center\">
                        {% if course.duration %}
                            <div class=\"col-md-3 col-6 mb-3\">
                                <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                    <i class=\"fas fa-clock fs-3 mb-2\"></i>
                                    <div class=\"fw-bold\">{{ course.duration }} min</div>
                                    <small>Duration</small>
                                </div>
                            </div>
                        {% endif %}
                        <div class=\"col-md-3 col-6 mb-3\">
                            <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                <i class=\"fas fa-dollar-sign fs-3 mb-2\"></i>
                                <div class=\"fw-bold\">\${{ course.price }}</div>
                                <small>Price</small>
                            </div>
                        </div>
                        <div class=\"col-md-3 col-6 mb-3\">
                            <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                <i class=\"fas fa-users fs-3 mb-2\"></i>
                                <div class=\"fw-bold\">{{ course.enrolledCount }}</div>
                                <small>Students</small>
                            </div>
                        </div>
                        <div class=\"col-md-3 col-6 mb-3\">
                            <div class=\"bg-white bg-opacity-10 rounded p-3\">
                                <i class=\"fas fa-eye fs-3 mb-2\"></i>
                                <div class=\"fw-bold\">{{ course.viewCount }}</div>
                                <small>Views</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    {% if course.thumbnailImage %}
                        <img src=\"{{ course.thumbnailUrl }}\" alt=\"{{ course.title }}\" class=\"img-fluid rounded shadow\" style=\"max-width: 300px;\">
                    {% else %}
                        <div class=\"bg-white bg-opacity-10 rounded p-5\">
                            <i class=\"fas fa-graduation-cap\" style=\"font-size: 6rem; opacity: 0.7;\"></i>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- Main Content -->
                <div class=\"col-lg-8\">
                    <!-- Learning Outcomes -->
                    {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                        <div class=\"card mb-4 border-0 shadow-sm\">
                            <div class=\"card-header bg-primary text-white\">
                                <h4 class=\"mb-0\">
                                    <i class=\"fas fa-bullseye me-2\"></i>What You'll Learn
                                </h4>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"row\">
                                    {% for outcome in course.learningOutcomes %}
                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-check-circle text-success me-3 mt-1\"></i>
                                                <span>{{ outcome }}</span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Course Features -->
                    {% if course.features and course.features|length > 0 %}
                        <div class=\"card mb-4 border-0 shadow-sm\">
                            <div class=\"card-header bg-success text-white\">
                                <h4 class=\"mb-0\">
                                    <i class=\"fas fa-star me-2\"></i>Course Features
                                </h4>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"row\">
                                    {% for feature in course.features %}
                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-star text-warning me-3 mt-1\"></i>
                                                <span>{{ feature }}</span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Course Modules -->
                    {% if course.hasModules and modules|length > 0 %}
                        <div class=\"card mb-4 border-0 shadow-sm\">
                            <div class=\"card-header bg-info text-white\">
                                <h4 class=\"mb-0\">
                                    <i class=\"fas fa-list me-2\"></i>Course Modules
                                </h4>
                            </div>
                            <div class=\"card-body\">
                                <div class=\"accordion\" id=\"moduleAccordion\">
                                    {% for module in modules %}
                                        <div class=\"accordion-item\">
                                            <h2 class=\"accordion-header\" id=\"heading{{ loop.index }}\">
                                                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#collapse{{ loop.index }}\" aria-expanded=\"false\" aria-controls=\"collapse{{ loop.index }}\">
                                                    <div class=\"d-flex justify-content-between w-100 me-3\">
                                                        <span class=\"fw-bold\">{{ module.title }}</span>
                                                        <div>
                                                            {% if module.duration %}
                                                                <span class=\"badge bg-primary me-2\">{{ module.duration }} min</span>
                                                            {% endif %}
                                                            {% if module.price and module.price > 0 %}
                                                                <span class=\"badge bg-success\">\${{ module.price }}</span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </button>
                                            </h2>
                                            <div id=\"collapse{{ loop.index }}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading{{ loop.index }}\" data-bs-parent=\"#moduleAccordion\">
                                                <div class=\"accordion-body\">
                                                    {% if module.description %}
                                                        <p class=\"mb-3\">{{ module.description }}</p>
                                                    {% endif %}
                                                    
                                                    {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                                        <h6 class=\"fw-bold mb-2\">Learning Outcomes:</h6>
                                                        <ul class=\"list-unstyled mb-3\">
                                                            {% for outcome in module.learningOutcomes %}
                                                                <li class=\"mb-1\">
                                                                    <i class=\"fas fa-check text-success me-2\"></i>{{ outcome }}
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Sidebar -->
                <div class=\"col-lg-4\">
                    <!-- Enrollment Card -->
                    <div class=\"card mb-4 border-0 shadow-sm sticky-top\" style=\"top: 2rem;\">
                        <div class=\"card-body text-center\">
                            <div class=\"mb-3\">
                                <div class=\"display-6 fw-bold text-success\">\${{ course.price }}</div>
                                <small class=\"text-muted\">One-time payment</small>
                            </div>
                            
                            {% if user %}
                                {% if is_enrolled %}
                                    <button class=\"btn btn-success btn-lg w-100 mb-3\" disabled>
                                        <i class=\"fas fa-check me-2\"></i>Enrolled
                                    </button>
                                {% else %}
                                    <button class=\"btn btn-primary btn-lg w-100 mb-3\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: none;\">
                                        <i class=\"fas fa-shopping-cart me-2\"></i>Enroll Now
                                    </button>
                                {% endif %}
                            {% else %}
                                <a href=\"{{ path('app_login') }}\" class=\"btn btn-primary btn-lg w-100 mb-3\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: none;\">
                                    <i class=\"fas fa-sign-in-alt me-2\"></i>Login to Enroll
                                </a>
                            {% endif %}

                            <div class=\"text-center\">
                                <small class=\"text-muted\">
                                    <i class=\"fas fa-shield-alt me-1\"></i>30-day money-back guarantee
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Course Info -->
                    <div class=\"card mb-4 border-0 shadow-sm\">
                        <div class=\"card-header bg-light\">
                            <h5 class=\"mb-0\">
                                <i class=\"fas fa-info-circle me-2\"></i>Course Information
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"row mb-2\">
                                <div class=\"col-6\"><strong>Course Code:</strong></div>
                                <div class=\"col-6\">{{ course.code }}</div>
                            </div>
                            <div class=\"row mb-2\">
                                <div class=\"col-6\"><strong>Category:</strong></div>
                                <div class=\"col-6\">{{ course.category ?: 'General' }}</div>
                            </div>
                            {% if course.level %}
                                <div class=\"row mb-2\">
                                    <div class=\"col-6\"><strong>Level:</strong></div>
                                    <div class=\"col-6\">{{ course.level }}</div>
                                </div>
                            {% endif %}
                            {% if course.duration %}
                                <div class=\"row mb-2\">
                                    <div class=\"col-6\"><strong>Duration:</strong></div>
                                    <div class=\"col-6\">{{ course.duration }} minutes</div>
                                </div>
                            {% endif %}
                            <div class=\"row mb-2\">
                                <div class=\"col-6\"><strong>Students:</strong></div>
                                <div class=\"col-6\">{{ course.enrolledCount }}</div>
                            </div>
                            <div class=\"row\">
                                <div class=\"col-6\"><strong>Last Updated:</strong></div>
                                <div class=\"col-6\">{{ course.updatedAt|date('M Y') }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Share Course -->
                    <div class=\"card border-0 shadow-sm\">
                        <div class=\"card-header bg-light\">
                            <h5 class=\"mb-0\">
                                <i class=\"fas fa-share-alt me-2\"></i>Share This Course
                            </h5>
                        </div>
                        <div class=\"card-body text-center\">
                            <div class=\"d-flex justify-content-center gap-2\">
                                <a href=\"#\" class=\"btn btn-outline-primary btn-sm\">
                                    <i class=\"fab fa-facebook-f\"></i>
                                </a>
                                <a href=\"#\" class=\"btn btn-outline-info btn-sm\">
                                    <i class=\"fab fa-twitter\"></i>
                                </a>
                                <a href=\"#\" class=\"btn btn-outline-primary btn-sm\">
                                    <i class=\"fab fa-linkedin-in\"></i>
                                </a>
                                <a href=\"#\" class=\"btn btn-outline-success btn-sm\">
                                    <i class=\"fab fa-whatsapp\"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Courses -->
    <section class=\"py-5 bg-light\">
        <div class=\"container\">
            <h2 class=\"text-center mb-5\">Other Onsite Courses</h2>
            <div class=\"row\">
                <div class=\"col-md-4 mb-4\">
                    <div class=\"card h-100 border-0 shadow-sm\">
                        <div class=\"card-body text-center\">
                            <i class=\"fas fa-chart-line text-primary mb-3\" style=\"font-size: 3rem;\"></i>
                            <h5>Technical Analysis</h5>
                            <p class=\"text-muted\">Master chart patterns and technical indicators</p>
                            <a href=\"#\" class=\"btn btn-outline-primary\">Learn More</a>
                        </div>
                    </div>
                </div>
                <div class=\"col-md-4 mb-4\">
                    <div class=\"card h-100 border-0 shadow-sm\">
                        <div class=\"card-body text-center\">
                            <i class=\"fas fa-coins text-warning mb-3\" style=\"font-size: 3rem;\"></i>
                            <h5>Risk Management</h5>
                            <p class=\"text-muted\">Learn to protect your capital and manage risk</p>
                            <a href=\"#\" class=\"btn btn-outline-primary\">Learn More</a>
                        </div>
                    </div>
                </div>
                <div class=\"col-md-4 mb-4\">
                    <div class=\"card h-100 border-0 shadow-sm\">
                        <div class=\"card-body text-center\">
                            <i class=\"fas fa-brain text-success mb-3\" style=\"font-size: 3rem;\"></i>
                            <h5>Trading Psychology</h5>
                            <p class=\"text-muted\">Develop the right mindset for successful trading</p>
                            <a href=\"#\" class=\"btn btn-outline-primary\">Learn More</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.course-header {
    position: relative;
    overflow: hidden;
}

.course-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-pattern.png') repeat;
    opacity: 0.1;
    z-index: 0;
}

.course-header .container {
    position: relative;
    z-index: 1;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.sticky-top {
    position: sticky !important;
}
</style>
{% endblock %}
", "onsite_course/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\onsite_course\\show.html.twig");
    }
}
