<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* onsite_course/index.html.twig */
class __TwigTemplate_552d78aaea1d92cdad4bff5182e61e3b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "onsite_course/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "onsite_course/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Onsite Courses - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid\">
    <!-- Hero Section -->
    <section class=\"hero-section py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">Onsite Trading Courses</h1>
                    <p class=\"lead mb-4\">Master trading skills through our comprehensive onsite courses designed for hands-on learning and direct interaction with expert instructors.</p>
                    <div class=\"d-flex flex-wrap gap-3\">
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2\">
                            <i class=\"fas fa-users me-2\"></i>Small Class Sizes
                        </span>
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2\">
                            <i class=\"fas fa-chalkboard-teacher me-2\"></i>Expert Instructors
                        </span>
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2\">
                            <i class=\"fas fa-certificate me-2\"></i>Certification Included
                        </span>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <i class=\"fas fa-graduation-cap\" style=\"font-size: 8rem; opacity: 0.3;\"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Grid -->
    <section class=\"py-5\">
        <div class=\"container\">
            ";
        // line 36
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 36, $this->source); })())) > 0)) {
            // line 37
            yield "                <div class=\"row\">
                    ";
            // line 38
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 38, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
                // line 39
                yield "                        <div class=\"col-lg-4 col-md-6 mb-4\">
                            <div class=\"card h-100 shadow-sm border-0\" style=\"transition: transform 0.3s ease, box-shadow 0.3s ease;\">
                                <!-- Course Thumbnail -->
                                ";
                // line 42
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailImage", [], "any", false, false, false, 42)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 43
                    yield "                                    <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailUrl", [], "any", false, false, false, 43), "html", null, true);
                    yield "\" class=\"card-img-top\" alt=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 43), "html", null, true);
                    yield "\" style=\"height: 200px; object-fit: cover;\">
                                ";
                } else {
                    // line 45
                    yield "                                    <div class=\"card-img-top d-flex align-items-center justify-content-center bg-light\" style=\"height: 200px;\">
                                        <i class=\"fas fa-graduation-cap text-muted\" style=\"font-size: 3rem;\"></i>
                                    </div>
                                ";
                }
                // line 49
                yield "
                                <div class=\"card-body d-flex flex-column\">
                                    <!-- Course Code Badge -->
                                    <div class=\"mb-2\">
                                        <span class=\"badge bg-primary\">";
                // line 53
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 53), "html", null, true);
                yield "</span>
                                        ";
                // line 54
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 54)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 55
                    yield "                                            <span class=\"badge bg-secondary\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 55), "html", null, true);
                    yield "</span>
                                        ";
                }
                // line 57
                yield "                                    </div>

                                    <!-- Course Title -->
                                    <h5 class=\"card-title fw-bold\">";
                // line 60
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 60), "html", null, true);
                yield "</h5>

                                    <!-- Course Description -->
                                    ";
                // line 63
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 63)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 64
                    yield "                                        <p class=\"card-text text-muted\">
                                            ";
                    // line 65
                    yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 65)) > 120)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 65), 0, 120) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 65), "html", null, true)));
                    yield "
                                        </p>
                                    ";
                }
                // line 68
                yield "
                                    <!-- Course Details -->
                                    <div class=\"mt-auto\">
                                        <div class=\"row text-center mb-3\">
                                            ";
                // line 72
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "duration", [], "any", false, false, false, 72)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 73
                    yield "                                                <div class=\"col-4\">
                                                    <small class=\"text-muted d-block\">Duration</small>
                                                    <strong>";
                    // line 75
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "duration", [], "any", false, false, false, 75), "html", null, true);
                    yield " min</strong>
                                                </div>
                                            ";
                }
                // line 78
                yield "                                            <div class=\"col-4\">
                                                <small class=\"text-muted d-block\">Category</small>
                                                <strong>";
                // line 80
                yield ((CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 80)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 80), "html", null, true)) : ("General"));
                yield "</strong>
                                            </div>
                                            <div class=\"col-4\">
                                                <small class=\"text-muted d-block\">Price</small>
                                                <strong class=\"text-success\">\$";
                // line 84
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "price", [], "any", false, false, false, 84), "html", null, true);
                yield "</strong>
                                            </div>
                                        </div>

                                        <!-- Action Button -->
                                        <a href=\"";
                // line 89
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_onsite_course_show", ["code" => CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 89)]), "html", null, true);
                yield "\" 
                                           class=\"btn btn-primary w-100\"
                                           style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: none;\">
                                            <i class=\"fas fa-eye me-2\"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 99
            yield "                </div>
            ";
        } else {
            // line 101
            yield "                <!-- No Courses Available -->
                <div class=\"text-center py-5\">
                    <i class=\"fas fa-graduation-cap text-muted mb-4\" style=\"font-size: 4rem;\"></i>
                    <h3 class=\"text-muted mb-3\">No Onsite Courses Available</h3>
                    <p class=\"text-muted\">We're working on adding new onsite courses. Please check back soon!</p>
                </div>
            ";
        }
        // line 108
        yield "        </div>
    </section>

    <!-- Call to Action Section -->
    <section class=\"py-5 bg-light\">
        <div class=\"container text-center\">
            <div class=\"row justify-content-center\">
                <div class=\"col-lg-8\">
                    <h2 class=\"fw-bold mb-3\">Ready to Start Your Trading Journey?</h2>
                    <p class=\"lead text-muted mb-4\">Join thousands of successful traders who have learned with Capitol Academy's expert-led onsite courses.</p>
                    <div class=\"d-flex justify-content-center gap-3 flex-wrap\">
                        <a href=\"";
        // line 119
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" class=\"btn btn-outline-primary btn-lg\">
                            <i class=\"fas fa-phone me-2\"></i>Contact Us
                        </a>
                        <a href=\"";
        // line 122
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_register");
        yield "\" class=\"btn btn-primary btn-lg\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); border: none;\">
                            <i class=\"fas fa-user-plus me-2\"></i>Join for Free
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-pattern.png') repeat;
    opacity: 0.1;
    z-index: 0;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.badge {
    font-weight: 500;
}

.btn-primary {
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(1, 26, 45, 0.3);
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "onsite_course/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  287 => 122,  281 => 119,  268 => 108,  259 => 101,  255 => 99,  239 => 89,  231 => 84,  224 => 80,  220 => 78,  214 => 75,  210 => 73,  208 => 72,  202 => 68,  196 => 65,  193 => 64,  191 => 63,  185 => 60,  180 => 57,  174 => 55,  172 => 54,  168 => 53,  162 => 49,  156 => 45,  148 => 43,  146 => 42,  141 => 39,  137 => 38,  134 => 37,  132 => 36,  100 => 6,  87 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Onsite Courses - Capitol Academy{% endblock %}

{% block body %}
<div class=\"container-fluid\">
    <!-- Hero Section -->
    <section class=\"hero-section py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">Onsite Trading Courses</h1>
                    <p class=\"lead mb-4\">Master trading skills through our comprehensive onsite courses designed for hands-on learning and direct interaction with expert instructors.</p>
                    <div class=\"d-flex flex-wrap gap-3\">
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2\">
                            <i class=\"fas fa-users me-2\"></i>Small Class Sizes
                        </span>
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2\">
                            <i class=\"fas fa-chalkboard-teacher me-2\"></i>Expert Instructors
                        </span>
                        <span class=\"badge bg-light text-dark fs-6 px-3 py-2\">
                            <i class=\"fas fa-certificate me-2\"></i>Certification Included
                        </span>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <i class=\"fas fa-graduation-cap\" style=\"font-size: 8rem; opacity: 0.3;\"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Grid -->
    <section class=\"py-5\">
        <div class=\"container\">
            {% if courses|length > 0 %}
                <div class=\"row\">
                    {% for course in courses %}
                        <div class=\"col-lg-4 col-md-6 mb-4\">
                            <div class=\"card h-100 shadow-sm border-0\" style=\"transition: transform 0.3s ease, box-shadow 0.3s ease;\">
                                <!-- Course Thumbnail -->
                                {% if course.thumbnailImage %}
                                    <img src=\"{{ course.thumbnailUrl }}\" class=\"card-img-top\" alt=\"{{ course.title }}\" style=\"height: 200px; object-fit: cover;\">
                                {% else %}
                                    <div class=\"card-img-top d-flex align-items-center justify-content-center bg-light\" style=\"height: 200px;\">
                                        <i class=\"fas fa-graduation-cap text-muted\" style=\"font-size: 3rem;\"></i>
                                    </div>
                                {% endif %}

                                <div class=\"card-body d-flex flex-column\">
                                    <!-- Course Code Badge -->
                                    <div class=\"mb-2\">
                                        <span class=\"badge bg-primary\">{{ course.code }}</span>
                                        {% if course.level %}
                                            <span class=\"badge bg-secondary\">{{ course.level }}</span>
                                        {% endif %}
                                    </div>

                                    <!-- Course Title -->
                                    <h5 class=\"card-title fw-bold\">{{ course.title }}</h5>

                                    <!-- Course Description -->
                                    {% if course.description %}
                                        <p class=\"card-text text-muted\">
                                            {{ course.description|length > 120 ? course.description|slice(0, 120) ~ '...' : course.description }}
                                        </p>
                                    {% endif %}

                                    <!-- Course Details -->
                                    <div class=\"mt-auto\">
                                        <div class=\"row text-center mb-3\">
                                            {% if course.duration %}
                                                <div class=\"col-4\">
                                                    <small class=\"text-muted d-block\">Duration</small>
                                                    <strong>{{ course.duration }} min</strong>
                                                </div>
                                            {% endif %}
                                            <div class=\"col-4\">
                                                <small class=\"text-muted d-block\">Category</small>
                                                <strong>{{ course.category ?: 'General' }}</strong>
                                            </div>
                                            <div class=\"col-4\">
                                                <small class=\"text-muted d-block\">Price</small>
                                                <strong class=\"text-success\">\${{ course.price }}</strong>
                                            </div>
                                        </div>

                                        <!-- Action Button -->
                                        <a href=\"{{ path('app_onsite_course_show', {'code': course.code}) }}\" 
                                           class=\"btn btn-primary w-100\"
                                           style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: none;\">
                                            <i class=\"fas fa-eye me-2\"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- No Courses Available -->
                <div class=\"text-center py-5\">
                    <i class=\"fas fa-graduation-cap text-muted mb-4\" style=\"font-size: 4rem;\"></i>
                    <h3 class=\"text-muted mb-3\">No Onsite Courses Available</h3>
                    <p class=\"text-muted\">We're working on adding new onsite courses. Please check back soon!</p>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class=\"py-5 bg-light\">
        <div class=\"container text-center\">
            <div class=\"row justify-content-center\">
                <div class=\"col-lg-8\">
                    <h2 class=\"fw-bold mb-3\">Ready to Start Your Trading Journey?</h2>
                    <p class=\"lead text-muted mb-4\">Join thousands of successful traders who have learned with Capitol Academy's expert-led onsite courses.</p>
                    <div class=\"d-flex justify-content-center gap-3 flex-wrap\">
                        <a href=\"{{ path('app_contact') }}\" class=\"btn btn-outline-primary btn-lg\">
                            <i class=\"fas fa-phone me-2\"></i>Contact Us
                        </a>
                        <a href=\"{{ path('app_register') }}\" class=\"btn btn-primary btn-lg\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); border: none;\">
                            <i class=\"fas fa-user-plus me-2\"></i>Join for Free
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-pattern.png') repeat;
    opacity: 0.1;
    z-index: 0;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.badge {
    font-weight: 500;
}

.btn-primary {
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(1, 26, 45, 0.3);
}
</style>
{% endblock %}
", "onsite_course/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\onsite_course\\index.html.twig");
    }
}
