<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/test_email.html.twig */
class __TwigTemplate_d7e72aa42ef8fc7354e70665469035fe extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/test_email.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/test_email.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Email Test - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Email System Test";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Email Test</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        yield "<div class=\"container-fluid\">
    <!-- Email Test Card -->
    <div class=\"card border-0 shadow-lg\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-8\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-envelope-open mr-3\" style=\"font-size: 2rem;\"></i>
                        Email System Test
                    </h2>
                    <p class=\"mb-0 mt-2\" style=\"opacity: 0.9;\">Test the email functionality to ensure proper configuration</p>
                </div>
                <div class=\"col-md-4 text-right\">
                    <i class=\"fas fa-cogs\" style=\"font-size: 3rem; opacity: 0.3;\"></i>
                </div>
            </div>
        </div>

        <div class=\"card-body\" style=\"padding: 2rem;\">
            <!-- Current Configuration Info -->
            <div class=\"row mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"info-card\" style=\"background: #f6f7f9; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #011a2d;\">
                        <h5 style=\"color: #011a2d; margin-bottom: 1rem;\">
                            <i class=\"fas fa-server mr-2\"></i>Current Configuration
                        </h5>
                        <ul class=\"list-unstyled mb-0\">
                            <li><strong>From Email:</strong> <EMAIL></li>
                            <li><strong>Test Recipient:</strong> <EMAIL></li>
                            <li><strong>SMTP Server:</strong> Gmail SMTP</li>
                            <li><strong>Port:</strong> 587 (TLS)</li>
                            <li><strong>Authentication:</strong> App Password</li>
                        </ul>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"info-card\" style=\"background: #f6f7f9; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #a90418;\">
                        <h5 style=\"color: #a90418; margin-bottom: 1rem;\">
                            <i class=\"fas fa-info-circle mr-2\"></i>Test Information
                        </h5>
                        <ul class=\"list-unstyled mb-0\">
                            <li><strong>Test Type:</strong> HTML Email</li>
                            <li><strong>Template:</strong> emails/test_email.html.twig</li>
                            <li><strong>Subject:</strong> Capitol Academy - Email Test</li>
                            <li><strong>Delivery:</strong> Immediate</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Test Form -->
            <div class=\"row\">
                <div class=\"col-md-8 mx-auto\">
                    <div class=\"test-form-container\" style=\"background: white; padding: 2rem; border-radius: 8px; border: 1px solid #e9ecef;\">
                        <h4 style=\"color: #011a2d; margin-bottom: 1.5rem; text-align: center;\">
                            <i class=\"fas fa-paper-plane mr-2\"></i>Send Test Email
                        </h4>
                        
                        <form method=\"post\">
                            <div class=\"text-center mb-4\">
                                <p class=\"text-muted\">
                                    Click the button below to send a test email to <strong><EMAIL></strong>
                                    to verify that the email system is working correctly.
                                </p>
                            </div>

                            <div class=\"text-center\">
                                <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 12px 40px; border-radius: 8px; font-weight: 600; transition: all 0.3s ease;\">
                                    <i class=\"fas fa-envelope mr-2\"></i>Send Test Email
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class=\"row mt-4\">
                <div class=\"col-12\">
                    <div class=\"alert\" style=\"background: #e7f3ff; border: 1px solid #b3d9ff; color: #0066cc; border-radius: 8px;\">
                        <h6><i class=\"fas fa-lightbulb mr-2\"></i>Instructions:</h6>
                        <ol class=\"mb-0\">
                            <li>Click the \"Send Test Email\" button above</li>
                            <li>Check the flash message for success/error status</li>
                            <li>Verify that the test email <NAME_EMAIL></li>
                            <li>If successful, the email system is properly configured</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 108
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 109
        yield "<script>
\$(document).ready(function() {
    // Add loading state to form submission
    \$('form').on('submit', function() {
        const button = \$(this).find('button[type=\"submit\"]');
        const originalHtml = button.html();
        
        button.prop('disabled', true);
        button.html('<i class=\"fas fa-spinner fa-spin mr-2\"></i>Sending...');
        
        // Re-enable button after 5 seconds in case of issues
        setTimeout(function() {
            button.prop('disabled', false);
            button.html(originalHtml);
        }, 5000);
    });
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/test_email.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  271 => 109,  258 => 108,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Email Test - Capitol Academy Admin{% endblock %}

{% block page_title %}Email System Test{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Email Test</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Email Test Card -->
    <div class=\"card border-0 shadow-lg\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-8\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-envelope-open mr-3\" style=\"font-size: 2rem;\"></i>
                        Email System Test
                    </h2>
                    <p class=\"mb-0 mt-2\" style=\"opacity: 0.9;\">Test the email functionality to ensure proper configuration</p>
                </div>
                <div class=\"col-md-4 text-right\">
                    <i class=\"fas fa-cogs\" style=\"font-size: 3rem; opacity: 0.3;\"></i>
                </div>
            </div>
        </div>

        <div class=\"card-body\" style=\"padding: 2rem;\">
            <!-- Current Configuration Info -->
            <div class=\"row mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"info-card\" style=\"background: #f6f7f9; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #011a2d;\">
                        <h5 style=\"color: #011a2d; margin-bottom: 1rem;\">
                            <i class=\"fas fa-server mr-2\"></i>Current Configuration
                        </h5>
                        <ul class=\"list-unstyled mb-0\">
                            <li><strong>From Email:</strong> <EMAIL></li>
                            <li><strong>Test Recipient:</strong> <EMAIL></li>
                            <li><strong>SMTP Server:</strong> Gmail SMTP</li>
                            <li><strong>Port:</strong> 587 (TLS)</li>
                            <li><strong>Authentication:</strong> App Password</li>
                        </ul>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"info-card\" style=\"background: #f6f7f9; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #a90418;\">
                        <h5 style=\"color: #a90418; margin-bottom: 1rem;\">
                            <i class=\"fas fa-info-circle mr-2\"></i>Test Information
                        </h5>
                        <ul class=\"list-unstyled mb-0\">
                            <li><strong>Test Type:</strong> HTML Email</li>
                            <li><strong>Template:</strong> emails/test_email.html.twig</li>
                            <li><strong>Subject:</strong> Capitol Academy - Email Test</li>
                            <li><strong>Delivery:</strong> Immediate</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Test Form -->
            <div class=\"row\">
                <div class=\"col-md-8 mx-auto\">
                    <div class=\"test-form-container\" style=\"background: white; padding: 2rem; border-radius: 8px; border: 1px solid #e9ecef;\">
                        <h4 style=\"color: #011a2d; margin-bottom: 1.5rem; text-align: center;\">
                            <i class=\"fas fa-paper-plane mr-2\"></i>Send Test Email
                        </h4>
                        
                        <form method=\"post\">
                            <div class=\"text-center mb-4\">
                                <p class=\"text-muted\">
                                    Click the button below to send a test email to <strong><EMAIL></strong>
                                    to verify that the email system is working correctly.
                                </p>
                            </div>

                            <div class=\"text-center\">
                                <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 12px 40px; border-radius: 8px; font-weight: 600; transition: all 0.3s ease;\">
                                    <i class=\"fas fa-envelope mr-2\"></i>Send Test Email
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class=\"row mt-4\">
                <div class=\"col-12\">
                    <div class=\"alert\" style=\"background: #e7f3ff; border: 1px solid #b3d9ff; color: #0066cc; border-radius: 8px;\">
                        <h6><i class=\"fas fa-lightbulb mr-2\"></i>Instructions:</h6>
                        <ol class=\"mb-0\">
                            <li>Click the \"Send Test Email\" button above</li>
                            <li>Check the flash message for success/error status</li>
                            <li>Verify that the test email <NAME_EMAIL></li>
                            <li>If successful, the email system is properly configured</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Add loading state to form submission
    \$('form').on('submit', function() {
        const button = \$(this).find('button[type=\"submit\"]');
        const originalHtml = button.html();
        
        button.prop('disabled', true);
        button.html('<i class=\"fas fa-spinner fa-spin mr-2\"></i>Sending...');
        
        // Re-enable button after 5 seconds in case of issues
        setTimeout(function() {
            button.prop('disabled', false);
            button.html(originalHtml);
        }, 5000);
    });
});
</script>
{% endblock %}
", "admin/test_email.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\test_email.html.twig");
    }
}
