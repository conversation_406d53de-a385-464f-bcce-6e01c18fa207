<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/contacts/index.html.twig */
class __TwigTemplate_39424fe448857226c6ad4851ff27e21f extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Contacts Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Contacts Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Contacts</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Contacts Management", "page_icon" => "fas fa-envelope", "search_placeholder" => "Search contacts by name, email, or country...", "stats" => [["title" => "Total Contacts", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 20
(isset($context["contacts"]) || array_key_exists("contacts", $context) ? $context["contacts"] : (function () { throw new RuntimeError('Variable "contacts" does not exist.', 20, $this->source); })())), "icon" => "fas fa-envelope", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Recent (7 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 27
(isset($context["contacts"]) || array_key_exists("contacts", $context) ? $context["contacts"] : (function () { throw new RuntimeError('Variable "contacts" does not exist.', 27, $this->source); })()), function ($__contact__) use ($context, $macros) { $context["contact"] = $__contact__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 27, $this->source); })()), "createdAt", [], "any", false, false, false, 27) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 27, $this->source); })()), "createdAt", [], "any", false, false, false, 27) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-7 days"))); })), "icon" => "fas fa-envelope-open", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "This Month", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 34
(isset($context["contacts"]) || array_key_exists("contacts", $context) ? $context["contacts"] : (function () { throw new RuntimeError('Variable "contacts" does not exist.', 34, $this->source); })()), function ($__contact__) use ($context, $macros) { $context["contact"] = $__contact__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 34, $this->source); })()), "createdAt", [], "any", false, false, false, 34) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 34, $this->source); })()), "createdAt", [], "any", false, false, false, 34) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-calendar-alt", "color" => "#17a2b8", "gradient" => "linear-gradient(135deg, #17a2b8 0%, #138496 100%)"], ["title" => "Countries", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::reduce($this->env, Twig\Extension\CoreExtension::map($this->env,         // line 41
(isset($context["contacts"]) || array_key_exists("contacts", $context) ? $context["contacts"] : (function () { throw new RuntimeError('Variable "contacts" does not exist.', 41, $this->source); })()), function ($__c__) use ($context, $macros) { $context["c"] = $__c__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["c"]) || array_key_exists("c", $context) ? $context["c"] : (function () { throw new RuntimeError('Variable "c" does not exist.', 41, $this->source); })()), "country", [], "any", false, false, false, 41); }), function ($__carry__, $__country__) use ($context, $macros) { $context["carry"] = $__carry__; $context["country"] = $__country__; return ((CoreExtension::inFilter((isset($context["country"]) || array_key_exists("country", $context) ? $context["country"] : (function () { throw new RuntimeError('Variable "country" does not exist.', 41, $this->source); })()), (isset($context["carry"]) || array_key_exists("carry", $context) ? $context["carry"] : (function () { throw new RuntimeError('Variable "carry" does not exist.', 41, $this->source); })()))) ? ((isset($context["carry"]) || array_key_exists("carry", $context) ? $context["carry"] : (function () { throw new RuntimeError('Variable "carry" does not exist.', 41, $this->source); })())) : (Twig\Extension\CoreExtension::merge((isset($context["carry"]) || array_key_exists("carry", $context) ? $context["carry"] : (function () { throw new RuntimeError('Variable "carry" does not exist.', 41, $this->source); })()), [(isset($context["country"]) || array_key_exists("country", $context) ? $context["country"] : (function () { throw new RuntimeError('Variable "country" does not exist.', 41, $this->source); })())]))); }, [])), "icon" => "fas fa-globe", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 48
        yield "
";
        // line 49
        yield from $this->load("admin/contacts/index.html.twig", 49, "1795582363")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 49, $this->source); })())));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 108
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 109
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.contact-row',
        ['.contact-name', '.contact-email', '.contact-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Contact management functions
\$(document).on('click', '.toggle-processed-btn', function() {
    const button = \$(this);
    const contactSlug = button.data('contact-slug');
    const originalHtml = button.html();

    // Show loading state
    button.prop('disabled', true);
    button.html('<i class=\"fas fa-spinner fa-spin\"></i>');

    // Make AJAX request
    \$.ajax({
        url: `/admin/contacts/\${contactSlug}/toggle-processed`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while updating the contact status.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html(originalHtml);
        }
    });
});

\$(document).on('click', '.delete-contact-btn', function() {
    const button = \$(this);
    const contactSlug = button.data('contact-slug');
    const contactName = button.data('contact-name');

    // Use standardized delete modal
    showDeleteModal(contactName, function() {
        executeContactDelete(contactSlug, button);
    });
});

// Contact management functions using standardized modals
function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

function executeContactDelete(contactSlug, button) {
    // Show loading state
    button.prop('disabled', true);
    button.html('<i class=\"fas fa-spinner fa-spin\"></i>');

    // Make AJAX request
    \$.ajax({
        url: `/admin/contacts/\${contactSlug}/delete`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html('<i class=\"fas fa-trash\"></i>');
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while deleting the contact.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html('<i class=\"fas fa-trash\"></i>');
        }
    });
}

// Function to show Capitol Academy styled alert messages
function showCapitolAlert(type, message) {
    // Use Capitol Academy navy blue for success messages
    const alertStyle = type === 'success' ?
        'background: #011a2d; color: white; border: 1px solid #011a2d;' :
        'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';

    const alertHtml = `
        <div class=\"alert alert-dismissible fade show\" role=\"alert\" style=\"\${alertStyle} border-radius: 8px; margin-bottom: 1rem; position: relative; z-index: 1050;\">
            <i class=\"fas fa-\${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
            \${message}
            <button type=\"button\" class=\"btn-close \${type === 'success' ? 'btn-close-white' : ''}\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
        </div>
    `;

    // Remove existing alerts to prevent duplicates
    \$('.alert').remove();

    // Position alert below header cards but above table content
    // Look for the statistics cards section and place alert after it
    const statsSection = \$('.card-body.pb-0');
    if (statsSection.length > 0) {
        // If stats section exists, place alert after it
        statsSection.after(alertHtml);
    } else {
        // Fallback: place after the card header
        \$('.card-header').after('<div class=\"alert-container\" style=\"padding: 0 1.5rem;\">' + alertHtml + '</div>');
    }

    // Auto-dismiss after 2 seconds
    setTimeout(function() {
        \$('.alert').fadeOut();
    }, 2000);
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/contacts/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  187 => 109,  174 => 108,  163 => 49,  160 => 48,  158 => 41,  157 => 34,  156 => 27,  155 => 20,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Contacts Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Contacts Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Contacts</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Contacts Management',
    'page_icon': 'fas fa-envelope',
    'search_placeholder': 'Search contacts by name, email, or country...',
    'stats': [
        {
            'title': 'Total Contacts',
            'value': contacts|length,
            'icon': 'fas fa-envelope',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Recent (7 days)',
            'value': contacts|filter(contact => contact.createdAt and contact.createdAt > date('-7 days'))|length,
            'icon': 'fas fa-envelope-open',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'This Month',
            'value': contacts|filter(contact => contact.createdAt and contact.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-calendar-alt',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Countries',
            'value': contacts|map(c => c.country)|reduce((carry, country) => country in carry ? carry : carry|merge([country]), [])|length,
            'icon': 'fas fa-globe',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Name'},
            {'text': 'Subject'},
            {'text': 'Country'},
            {'text': 'Source'},
            {'text': 'Date'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for contact in contacts %}
            {% set row_cells = [
                {
                    'content': '<h6 class=\"contact-name mb-0 font-weight-bold text-dark\">' ~ contact.fullName ~ '</h6>'
                },
                {
                    'content': contact.subject ?
                        '<span class=\"contact-subject text-dark font-weight-medium\">' ~ (contact.subject|length > 40 ? contact.subject|slice(0, 40) ~ '...' : contact.subject) ~ '</span>' :
                        '<span class=\"text-muted\">No subject</span>'
                },
                {
                    'content': '<span class=\"contact-country text-dark\">' ~ (contact.country|default('N/A')) ~ '</span>'
                },
                {
                    'content': '<span class=\"badge bg-info\">' ~ (contact.sourcePage|default('Website')) ~ '</span>'
                },
                {
                    'content': '<small class=\"text-muted\">' ~ contact.createdAt|date('M d, Y H:i') ~ '</small>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_contact_show', {'slug': contact.urlSlug}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Contact\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"mailto:' ~ contact.email ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Send Email\"><i class=\"fas fa-envelope\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm toggle-processed-btn\" data-contact-slug=\"' ~ contact.urlSlug ~ '\" style=\"background: ' ~ (contact.processed ? '#ffc107' : '#28a745') ~ '; color: ' ~ (contact.processed ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (contact.processed ? 'Mark as Unprocessed' : 'Mark as Processed') ~ '\"><i class=\"fas ' ~ (contact.processed ? 'fa-undo' : 'fa-check') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm delete-contact-btn\" data-contact-slug=\"' ~ contact.urlSlug ~ '\" data-contact-name=\"' ~ contact.fullName ~ '\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Contact\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'contact-row',
            'empty_message': 'No contacts found',
            'empty_icon': 'fas fa-envelope',
            'empty_description': 'Contact submissions will appear here.',
            'search_config': {
                'fields': ['.contact-name', '.contact-email', '.contact-country']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.contact-row',
        ['.contact-name', '.contact-email', '.contact-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Contact management functions
\$(document).on('click', '.toggle-processed-btn', function() {
    const button = \$(this);
    const contactSlug = button.data('contact-slug');
    const originalHtml = button.html();

    // Show loading state
    button.prop('disabled', true);
    button.html('<i class=\"fas fa-spinner fa-spin\"></i>');

    // Make AJAX request
    \$.ajax({
        url: `/admin/contacts/\${contactSlug}/toggle-processed`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while updating the contact status.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html(originalHtml);
        }
    });
});

\$(document).on('click', '.delete-contact-btn', function() {
    const button = \$(this);
    const contactSlug = button.data('contact-slug');
    const contactName = button.data('contact-name');

    // Use standardized delete modal
    showDeleteModal(contactName, function() {
        executeContactDelete(contactSlug, button);
    });
});

// Contact management functions using standardized modals
function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

function executeContactDelete(contactSlug, button) {
    // Show loading state
    button.prop('disabled', true);
    button.html('<i class=\"fas fa-spinner fa-spin\"></i>');

    // Make AJAX request
    \$.ajax({
        url: `/admin/contacts/\${contactSlug}/delete`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html('<i class=\"fas fa-trash\"></i>');
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while deleting the contact.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html('<i class=\"fas fa-trash\"></i>');
        }
    });
}

// Function to show Capitol Academy styled alert messages
function showCapitolAlert(type, message) {
    // Use Capitol Academy navy blue for success messages
    const alertStyle = type === 'success' ?
        'background: #011a2d; color: white; border: 1px solid #011a2d;' :
        'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';

    const alertHtml = `
        <div class=\"alert alert-dismissible fade show\" role=\"alert\" style=\"\${alertStyle} border-radius: 8px; margin-bottom: 1rem; position: relative; z-index: 1050;\">
            <i class=\"fas fa-\${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
            \${message}
            <button type=\"button\" class=\"btn-close \${type === 'success' ? 'btn-close-white' : ''}\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
        </div>
    `;

    // Remove existing alerts to prevent duplicates
    \$('.alert').remove();

    // Position alert below header cards but above table content
    // Look for the statistics cards section and place alert after it
    const statsSection = \$('.card-body.pb-0');
    if (statsSection.length > 0) {
        // If stats section exists, place alert after it
        statsSection.after(alertHtml);
    } else {
        // Fallback: place after the card header
        \$('.card-header').after('<div class=\"alert-container\" style=\"padding: 0 1.5rem;\">' + alertHtml + '</div>');
    }

    // Auto-dismiss after 2 seconds
    setTimeout(function() {
        \$('.alert').fadeOut();
    }, 2000);
}
</script>
{% endblock %}
", "admin/contacts/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\contacts\\index.html.twig");
    }
}


/* admin/contacts/index.html.twig */
class __TwigTemplate_39424fe448857226c6ad4851ff27e21f___1795582363 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 49
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 49);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 50
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 51
        yield "        <!-- Standardized Table -->
        ";
        // line 52
        $context["table_headers"] = [["text" => "Name"], ["text" => "Subject"], ["text" => "Country"], ["text" => "Source"], ["text" => "Date"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 60
        yield "
        ";
        // line 61
        $context["table_rows"] = [];
        // line 62
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["contacts"]) || array_key_exists("contacts", $context) ? $context["contacts"] : (function () { throw new RuntimeError('Variable "contacts" does not exist.', 62, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["contact"]) {
            // line 63
            yield "            ";
            $context["row_cells"] = [["content" => (("<h6 class=\"contact-name mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 65
$context["contact"], "fullName", [], "any", false, false, false, 65)) . "</h6>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 68
$context["contact"], "subject", [], "any", false, false, false, 68)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<span class=\"contact-subject text-dark font-weight-medium\">" . (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 69
$context["contact"], "subject", [], "any", false, false, false, 69)) > 40)) ? ((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "subject", [], "any", false, false, false, 69), 0, 40) . "...")) : (CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "subject", [], "any", false, false, false, 69)))) . "</span>")) : ("<span class=\"text-muted\">No subject</span>"))], ["content" => (("<span class=\"contact-country text-dark\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["contact"], "country", [], "any", true, true, false, 73)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "country", [], "any", false, false, false, 73), "N/A")) : ("N/A"))) . "</span>")], ["content" => (("<span class=\"badge bg-info\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 76
$context["contact"], "sourcePage", [], "any", true, true, false, 76)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "sourcePage", [], "any", false, false, false, 76), "Website")) : ("Website"))) . "</span>")], ["content" => (("<small class=\"text-muted\">" . $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source,             // line 79
$context["contact"], "createdAt", [], "any", false, false, false, 79), "M d, Y H:i")) . "</small>")], ["content" => (((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_contact_show", ["slug" => CoreExtension::getAttribute($this->env, $this->source,             // line 83
$context["contact"], "urlSlug", [], "any", false, false, false, 83)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Contact\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"mailto:") . CoreExtension::getAttribute($this->env, $this->source,             // line 84
$context["contact"], "email", [], "any", false, false, false, 84)) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Send Email\"><i class=\"fas fa-envelope\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm toggle-processed-btn\" data-contact-slug=\"") . CoreExtension::getAttribute($this->env, $this->source,             // line 85
$context["contact"], "urlSlug", [], "any", false, false, false, 85)) . "\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "processed", [], "any", false, false, false, 85)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#ffc107") : ("#28a745"))) . "; color: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "processed", [], "any", false, false, false, 85)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#212529") : ("white"))) . "; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "processed", [], "any", false, false, false, 85)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Mark as Unprocessed") : ("Mark as Processed"))) . "\"><i class=\"fas ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "processed", [], "any", false, false, false, 85)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("fa-undo") : ("fa-check"))) . "\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm delete-contact-btn\" data-contact-slug=\"") . CoreExtension::getAttribute($this->env, $this->source,             // line 86
$context["contact"], "urlSlug", [], "any", false, false, false, 86)) . "\" data-contact-name=\"") . CoreExtension::getAttribute($this->env, $this->source, $context["contact"], "fullName", [], "any", false, false, false, 86)) . "\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Contact\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 90
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 90, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 90, $this->source); })())]]);
            // line 91
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['contact'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 92
        yield "
        ";
        // line 93
        yield from $this->load("components/admin_table.html.twig", 93)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 94
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 94, $this->source); })()), "rows" =>         // line 95
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 95, $this->source); })()), "row_class" => "contact-row", "empty_message" => "No contacts found", "empty_icon" => "fas fa-envelope", "empty_description" => "Contact submissions will appear here.", "search_config" => ["fields" => [".contact-name", ".contact-email", ".contact-country"]]]));
        // line 104
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/contacts/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  779 => 104,  777 => 95,  776 => 94,  775 => 93,  772 => 92,  766 => 91,  763 => 90,  760 => 86,  758 => 85,  756 => 84,  754 => 83,  752 => 79,  751 => 76,  750 => 73,  749 => 69,  748 => 68,  747 => 65,  745 => 63,  740 => 62,  738 => 61,  735 => 60,  733 => 52,  730 => 51,  717 => 50,  694 => 49,  187 => 109,  174 => 108,  163 => 49,  160 => 48,  158 => 41,  157 => 34,  156 => 27,  155 => 20,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Contacts Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Contacts Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Contacts</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Contacts Management',
    'page_icon': 'fas fa-envelope',
    'search_placeholder': 'Search contacts by name, email, or country...',
    'stats': [
        {
            'title': 'Total Contacts',
            'value': contacts|length,
            'icon': 'fas fa-envelope',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Recent (7 days)',
            'value': contacts|filter(contact => contact.createdAt and contact.createdAt > date('-7 days'))|length,
            'icon': 'fas fa-envelope-open',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'This Month',
            'value': contacts|filter(contact => contact.createdAt and contact.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-calendar-alt',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Countries',
            'value': contacts|map(c => c.country)|reduce((carry, country) => country in carry ? carry : carry|merge([country]), [])|length,
            'icon': 'fas fa-globe',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Name'},
            {'text': 'Subject'},
            {'text': 'Country'},
            {'text': 'Source'},
            {'text': 'Date'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for contact in contacts %}
            {% set row_cells = [
                {
                    'content': '<h6 class=\"contact-name mb-0 font-weight-bold text-dark\">' ~ contact.fullName ~ '</h6>'
                },
                {
                    'content': contact.subject ?
                        '<span class=\"contact-subject text-dark font-weight-medium\">' ~ (contact.subject|length > 40 ? contact.subject|slice(0, 40) ~ '...' : contact.subject) ~ '</span>' :
                        '<span class=\"text-muted\">No subject</span>'
                },
                {
                    'content': '<span class=\"contact-country text-dark\">' ~ (contact.country|default('N/A')) ~ '</span>'
                },
                {
                    'content': '<span class=\"badge bg-info\">' ~ (contact.sourcePage|default('Website')) ~ '</span>'
                },
                {
                    'content': '<small class=\"text-muted\">' ~ contact.createdAt|date('M d, Y H:i') ~ '</small>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_contact_show', {'slug': contact.urlSlug}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Contact\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"mailto:' ~ contact.email ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Send Email\"><i class=\"fas fa-envelope\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm toggle-processed-btn\" data-contact-slug=\"' ~ contact.urlSlug ~ '\" style=\"background: ' ~ (contact.processed ? '#ffc107' : '#28a745') ~ '; color: ' ~ (contact.processed ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (contact.processed ? 'Mark as Unprocessed' : 'Mark as Processed') ~ '\"><i class=\"fas ' ~ (contact.processed ? 'fa-undo' : 'fa-check') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm delete-contact-btn\" data-contact-slug=\"' ~ contact.urlSlug ~ '\" data-contact-name=\"' ~ contact.fullName ~ '\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Contact\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'contact-row',
            'empty_message': 'No contacts found',
            'empty_icon': 'fas fa-envelope',
            'empty_description': 'Contact submissions will appear here.',
            'search_config': {
                'fields': ['.contact-name', '.contact-email', '.contact-country']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.contact-row',
        ['.contact-name', '.contact-email', '.contact-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Contact management functions
\$(document).on('click', '.toggle-processed-btn', function() {
    const button = \$(this);
    const contactSlug = button.data('contact-slug');
    const originalHtml = button.html();

    // Show loading state
    button.prop('disabled', true);
    button.html('<i class=\"fas fa-spinner fa-spin\"></i>');

    // Make AJAX request
    \$.ajax({
        url: `/admin/contacts/\${contactSlug}/toggle-processed`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while updating the contact status.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html(originalHtml);
        }
    });
});

\$(document).on('click', '.delete-contact-btn', function() {
    const button = \$(this);
    const contactSlug = button.data('contact-slug');
    const contactName = button.data('contact-name');

    // Use standardized delete modal
    showDeleteModal(contactName, function() {
        executeContactDelete(contactSlug, button);
    });
});

// Contact management functions using standardized modals
function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

function executeContactDelete(contactSlug, button) {
    // Show loading state
    button.prop('disabled', true);
    button.html('<i class=\"fas fa-spinner fa-spin\"></i>');

    // Make AJAX request
    \$.ajax({
        url: `/admin/contacts/\${contactSlug}/delete`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html('<i class=\"fas fa-trash\"></i>');
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while deleting the contact.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html('<i class=\"fas fa-trash\"></i>');
        }
    });
}

// Function to show Capitol Academy styled alert messages
function showCapitolAlert(type, message) {
    // Use Capitol Academy navy blue for success messages
    const alertStyle = type === 'success' ?
        'background: #011a2d; color: white; border: 1px solid #011a2d;' :
        'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';

    const alertHtml = `
        <div class=\"alert alert-dismissible fade show\" role=\"alert\" style=\"\${alertStyle} border-radius: 8px; margin-bottom: 1rem; position: relative; z-index: 1050;\">
            <i class=\"fas fa-\${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
            \${message}
            <button type=\"button\" class=\"btn-close \${type === 'success' ? 'btn-close-white' : ''}\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
        </div>
    `;

    // Remove existing alerts to prevent duplicates
    \$('.alert').remove();

    // Position alert below header cards but above table content
    // Look for the statistics cards section and place alert after it
    const statsSection = \$('.card-body.pb-0');
    if (statsSection.length > 0) {
        // If stats section exists, place alert after it
        statsSection.after(alertHtml);
    } else {
        // Fallback: place after the card header
        \$('.card-header').after('<div class=\"alert-container\" style=\"padding: 0 1.5rem;\">' + alertHtml + '</div>');
    }

    // Auto-dismiss after 2 seconds
    setTimeout(function() {
        \$('.alert').fadeOut();
    }, 2000);
}
</script>
{% endblock %}
", "admin/contacts/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\contacts\\index.html.twig");
    }
}
