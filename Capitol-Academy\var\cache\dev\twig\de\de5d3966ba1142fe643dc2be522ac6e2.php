<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/instructor/print.html.twig */
class __TwigTemplate_03a6801e0eff9949f09cb276aca1667e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/print.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/print.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Instructor Details - ";
        // line 6
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 6, $this->source); })()), "name", [], "any", false, false, false, 6), "html", null, true);
        yield " - Capitol Academy</title>
    
    <!-- Bootstrap 5 for print styling -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12pt; }
            .page-break { page-break-before: always; }
        }
        
        :root {
            --primary-navy: #011a2d;
            --accent-red: #a90418;
            --light-gray: #f8f9fa;
            --text-dark: #343a40;
            --text-muted: #6c757d;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            color: var(--text-dark);
            background: white;
        }
        
        .print-header {
            border-bottom: 3px solid var(--primary-navy);
            margin-bottom: 2rem;
            padding-bottom: 1rem;
        }
        
        .print-logo {
            max-height: 60px;
            width: auto;
        }
        
        .instructor-photo {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            border: 2px solid var(--light-gray);
        }
        
        .section-title {
            color: var(--primary-navy);
            border-bottom: 2px solid var(--accent-red);
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--primary-navy);
        }
        
        .print-footer {
            border-top: 1px solid var(--light-gray);
            margin-top: 2rem;
            padding-top: 1rem;
            font-size: 0.9rem;
            color: var(--text-muted);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class=\"container-fluid\">
        <!-- Print Header -->
        <div class=\"print-header\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-8\">
                    <h1 class=\"h3 mb-1\" style=\"color: var(--primary-navy);\">Capitol Academy</h1>
                    <p class=\"mb-0 text-muted\">Instructor Details Report</p>
                </div>
                <div class=\"col-md-4 text-end\">
                    <img src=\"";
        // line 101
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-horizontal.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"print-logo\">
                </div>
            </div>
        </div>

        <!-- Instructor Information -->
        <div class=\"row\">
            <div class=\"col-md-8\">
                <h2 class=\"section-title\">Instructor Information</h2>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Full Name:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        ";
        // line 116
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 116, $this->source); })()), "name", [], "any", false, false, false, 116), "html", null, true);
        yield "
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Email:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        ";
        // line 125
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 125, $this->source); })()), "email", [], "any", false, false, false, 125), "html", null, true);
        yield "
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Phone:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        ";
        // line 134
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["instructor"] ?? null), "phone", [], "any", true, true, false, 134) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 134, $this->source); })()), "phone", [], "any", false, false, false, 134)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 134, $this->source); })()), "phone", [], "any", false, false, false, 134), "html", null, true)) : ("Not provided"));
        yield "
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Status:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        <span class=\"status-badge ";
        // line 143
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 143, $this->source); })()), "isActive", [], "any", false, false, false, 143)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("status-active") : ("status-inactive"));
        yield "\">
                            ";
        // line 144
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 144, $this->source); })()), "isActive", [], "any", false, false, false, 144)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Active") : ("Inactive"));
        yield "
                        </span>
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Joined Date:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        ";
        // line 154
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 154, $this->source); })()), "createdAt", [], "any", false, false, false, 154), "F d, Y"), "html", null, true);
        yield "
                    </div>
                </div>
            </div>
            
            <div class=\"col-md-4 text-center\">
                ";
        // line 160
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 160, $this->source); })()), "profilePicture", [], "any", false, false, false, 160)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 161
            yield "                    <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/instructors/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 161, $this->source); })()), "profilePicture", [], "any", false, false, false, 161))), "html", null, true);
            yield "\" 
                         alt=\"";
            // line 162
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 162, $this->source); })()), "name", [], "any", false, false, false, 162), "html", null, true);
            yield "\" class=\"instructor-photo\">
                ";
        } else {
            // line 164
            yield "                    <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/instructors/instructor-default-pp.png"), "html", null, true);
            yield "\" 
                         alt=\"Default Instructor\" class=\"instructor-photo\">
                ";
        }
        // line 167
        yield "            </div>
        </div>

        <!-- Biography Section -->
        ";
        // line 171
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 171, $this->source); })()), "biography", [], "any", false, false, false, 171)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 172
            yield "        <div class=\"row mt-4\">
            <div class=\"col-12\">
                <h3 class=\"section-title\">Biography</h3>
                <div class=\"p-3\" style=\"background-color: var(--light-gray); border-radius: 8px;\">
                    ";
            // line 176
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 176, $this->source); })()), "biography", [], "any", false, false, false, 176), "html", null, true));
            yield "
                </div>
            </div>
        </div>
        ";
        }
        // line 181
        yield "
        <!-- Specializations Section -->
        ";
        // line 183
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 183, $this->source); })()), "specializations", [], "any", false, false, false, 183)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 184
            yield "        <div class=\"row mt-4\">
            <div class=\"col-12\">
                <h3 class=\"section-title\">Specializations</h3>
                <div class=\"p-3\" style=\"background-color: var(--light-gray); border-radius: 8px;\">
                    ";
            // line 188
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 188, $this->source); })()), "specializations", [], "any", false, false, false, 188), "html", null, true));
            yield "
                </div>
            </div>
        </div>
        ";
        }
        // line 193
        yield "
        <!-- Print Footer -->
        <div class=\"print-footer\">
            <div class=\"row\">
                <div class=\"col-md-6\">
                    <strong>Capitol Academy</strong><br>
                    Professional Trading Education Platform
                </div>
                <div class=\"col-md-6 text-end\">
                    Generated on: ";
        // line 202
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "F d, Y \\a\\t g:i A"), "html", null, true);
        yield "<br>
                    Report ID: INS-";
        // line 203
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 203, $this->source); })()), "id", [], "any", false, false, false, 203), "html", null, true);
        yield "-";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "Ymd"), "html", null, true);
        yield "
                </div>
            </div>
        </div>
    </div>

    <!-- Print Button (hidden when printing) -->
    <div class=\"no-print position-fixed bottom-0 end-0 m-3\">
        <button onclick=\"window.print()\" class=\"btn btn-primary\">
            <i class=\"fas fa-print\"></i> Print Report
        </button>
        <button onclick=\"window.close()\" class=\"btn btn-secondary ms-2\">
            <i class=\"fas fa-times\"></i> Close
        </button>
    </div>

    <!-- Font Awesome for icons -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    
    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/print.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  309 => 203,  305 => 202,  294 => 193,  286 => 188,  280 => 184,  278 => 183,  274 => 181,  266 => 176,  260 => 172,  258 => 171,  252 => 167,  245 => 164,  240 => 162,  235 => 161,  233 => 160,  224 => 154,  211 => 144,  207 => 143,  195 => 134,  183 => 125,  171 => 116,  153 => 101,  55 => 6,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Instructor Details - {{ instructor.name }} - Capitol Academy</title>
    
    <!-- Bootstrap 5 for print styling -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12pt; }
            .page-break { page-break-before: always; }
        }
        
        :root {
            --primary-navy: #011a2d;
            --accent-red: #a90418;
            --light-gray: #f8f9fa;
            --text-dark: #343a40;
            --text-muted: #6c757d;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            color: var(--text-dark);
            background: white;
        }
        
        .print-header {
            border-bottom: 3px solid var(--primary-navy);
            margin-bottom: 2rem;
            padding-bottom: 1rem;
        }
        
        .print-logo {
            max-height: 60px;
            width: auto;
        }
        
        .instructor-photo {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            border: 2px solid var(--light-gray);
        }
        
        .section-title {
            color: var(--primary-navy);
            border-bottom: 2px solid var(--accent-red);
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--primary-navy);
        }
        
        .print-footer {
            border-top: 1px solid var(--light-gray);
            margin-top: 2rem;
            padding-top: 1rem;
            font-size: 0.9rem;
            color: var(--text-muted);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class=\"container-fluid\">
        <!-- Print Header -->
        <div class=\"print-header\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-8\">
                    <h1 class=\"h3 mb-1\" style=\"color: var(--primary-navy);\">Capitol Academy</h1>
                    <p class=\"mb-0 text-muted\">Instructor Details Report</p>
                </div>
                <div class=\"col-md-4 text-end\">
                    <img src=\"{{ asset('images/logos/logo-horizontal.png') }}\" alt=\"Capitol Academy\" class=\"print-logo\">
                </div>
            </div>
        </div>

        <!-- Instructor Information -->
        <div class=\"row\">
            <div class=\"col-md-8\">
                <h2 class=\"section-title\">Instructor Information</h2>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Full Name:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        {{ instructor.name }}
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Email:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        {{ instructor.email }}
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Phone:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        {{ instructor.phone ?? 'Not provided' }}
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Status:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        <span class=\"status-badge {{ instructor.isActive ? 'status-active' : 'status-inactive' }}\">
                            {{ instructor.isActive ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
                
                <div class=\"row mb-3\">
                    <div class=\"col-sm-3\">
                        <span class=\"info-label\">Joined Date:</span>
                    </div>
                    <div class=\"col-sm-9\">
                        {{ instructor.createdAt|date('F d, Y') }}
                    </div>
                </div>
            </div>
            
            <div class=\"col-md-4 text-center\">
                {% if instructor.profilePicture %}
                    <img src=\"{{ asset('uploads/instructors/' ~ instructor.profilePicture) }}\" 
                         alt=\"{{ instructor.name }}\" class=\"instructor-photo\">
                {% else %}
                    <img src=\"{{ asset('images/instructors/instructor-default-pp.png') }}\" 
                         alt=\"Default Instructor\" class=\"instructor-photo\">
                {% endif %}
            </div>
        </div>

        <!-- Biography Section -->
        {% if instructor.biography %}
        <div class=\"row mt-4\">
            <div class=\"col-12\">
                <h3 class=\"section-title\">Biography</h3>
                <div class=\"p-3\" style=\"background-color: var(--light-gray); border-radius: 8px;\">
                    {{ instructor.biography|nl2br }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Specializations Section -->
        {% if instructor.specializations %}
        <div class=\"row mt-4\">
            <div class=\"col-12\">
                <h3 class=\"section-title\">Specializations</h3>
                <div class=\"p-3\" style=\"background-color: var(--light-gray); border-radius: 8px;\">
                    {{ instructor.specializations|nl2br }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Print Footer -->
        <div class=\"print-footer\">
            <div class=\"row\">
                <div class=\"col-md-6\">
                    <strong>Capitol Academy</strong><br>
                    Professional Trading Education Platform
                </div>
                <div class=\"col-md-6 text-end\">
                    Generated on: {{ \"now\"|date('F d, Y \\\\a\\\\t g:i A') }}<br>
                    Report ID: INS-{{ instructor.id }}-{{ \"now\"|date('Ymd') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Print Button (hidden when printing) -->
    <div class=\"no-print position-fixed bottom-0 end-0 m-3\">
        <button onclick=\"window.print()\" class=\"btn btn-primary\">
            <i class=\"fas fa-print\"></i> Print Report
        </button>
        <button onclick=\"window.close()\" class=\"btn btn-secondary ms-2\">
            <i class=\"fas fa-times\"></i> Close
        </button>
    </div>

    <!-- Font Awesome for icons -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    
    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
</body>
</html>
", "admin/instructor/print.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\print.html.twig");
    }
}
