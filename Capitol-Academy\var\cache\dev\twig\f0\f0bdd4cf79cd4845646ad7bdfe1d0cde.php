<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* components/admin_table_header.html.twig */
class __TwigTemplate_db283d3ef3b8bb1eb4140cef3c790223 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_table_header.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_table_header.html.twig"));

        // line 13
        yield "
<!-- Professional Header Card -->
<div class=\"card border-0 shadow-lg mb-4\">
    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
        <div class=\"row align-items-center\">
            <div class=\"col-md-6\">
                <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                    <i class=\"";
        // line 20
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("icon", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["icon"]) || array_key_exists("icon", $context) ? $context["icon"] : (function () { throw new RuntimeError('Variable "icon" does not exist.', 20, $this->source); })()), "fas fa-table")) : ("fas fa-table")), "html", null, true);
        yield " mr-3\" style=\"font-size: 2rem;\"></i>
                    ";
        // line 21
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("title", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["title"]) || array_key_exists("title", $context) ? $context["title"] : (function () { throw new RuntimeError('Variable "title" does not exist.', 21, $this->source); })()), "Admin Management")) : ("Admin Management")), "html", null, true);
        yield "
                </h2>
            </div>
            <div class=\"col-md-6\">
                <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                    <!-- Professional Search -->
                    <div class=\"search-container me-3 mb-2 mb-md-0\" style=\"position: relative;\">
                        <div class=\"input-group\" style=\"width: 320px;\">
                            <input type=\"text\"
                                   id=\"professional-search\"
                                   class=\"form-control form-control-lg admin-search-input\"
                                   placeholder=\"";
        // line 32
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("search_placeholder", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["search_placeholder"]) || array_key_exists("search_placeholder", $context) ? $context["search_placeholder"] : (function () { throw new RuntimeError('Variable "search_placeholder" does not exist.', 32, $this->source); })()), "Search...")) : ("Search...")), "html", null, true);
        yield "\"
                                   style=\"border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\"
                                        class=\"btn btn-lg admin-search-btn\"
                                        id=\"search-clear-btn\"
                                        style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;\">
                                    <i class=\"fas fa-search\"></i>
                                </button>
                            </div>
                        </div>
                        <div id=\"search-results-count\" class=\"text-muted small mt-1\" style=\"display: none;\"></div>
                    </div>
                    
                    <!-- Additional Action Buttons -->
                    ";
        // line 47
        if ((array_key_exists("additional_buttons", $context) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["additional_buttons"]) || array_key_exists("additional_buttons", $context) ? $context["additional_buttons"] : (function () { throw new RuntimeError('Variable "additional_buttons" does not exist.', 47, $this->source); })())) > 0))) {
            // line 48
            yield "                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["additional_buttons"]) || array_key_exists("additional_buttons", $context) ? $context["additional_buttons"] : (function () { throw new RuntimeError('Variable "additional_buttons" does not exist.', 48, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["button"]) {
                // line 49
                yield "                            <a href=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "url", [], "any", false, false, false, 49), "html", null, true);
                yield "\" 
                               class=\"btn btn-outline-light me-2 mb-2 mb-md-0\" 
                               style=\"font-weight: 500; border-radius: 6px; padding: 0.5rem 1rem; transition: all 0.3s ease;\"
                               title=\"";
                // line 52
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, $context["button"], "title", [], "any", true, true, false, 52)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "title", [], "any", false, false, false, 52), "")) : ("")), "html", null, true);
                yield "\">
                                <i class=\"";
                // line 53
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "icon", [], "any", false, false, false, 53), "html", null, true);
                yield " me-2\"></i>";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["button"], "text", [], "any", false, false, false, 53), "html", null, true);
                yield "
                            </a>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['button'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 56
            yield "                    ";
        }
        // line 57
        yield "                    
                    <!-- Create Button -->
                    ";
        // line 59
        if (array_key_exists("create_button", $context)) {
            // line 60
            yield "                        <a href=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["create_button"]) || array_key_exists("create_button", $context) ? $context["create_button"] : (function () { throw new RuntimeError('Variable "create_button" does not exist.', 60, $this->source); })()), "url", [], "any", false, false, false, 60), "html", null, true);
            yield "\" 
                           class=\"btn btn-light admin-btn-create mb-2 mb-md-0\" 
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                            <i class=\"";
            // line 63
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["create_button"] ?? null), "icon", [], "any", true, true, false, 63)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["create_button"]) || array_key_exists("create_button", $context) ? $context["create_button"] : (function () { throw new RuntimeError('Variable "create_button" does not exist.', 63, $this->source); })()), "icon", [], "any", false, false, false, 63), "fas fa-plus")) : ("fas fa-plus")), "html", null, true);
            yield " me-2\"></i>";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["create_button"] ?? null), "text", [], "any", true, true, false, 63)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["create_button"]) || array_key_exists("create_button", $context) ? $context["create_button"] : (function () { throw new RuntimeError('Variable "create_button" does not exist.', 63, $this->source); })()), "text", [], "any", false, false, false, 63), "Create New")) : ("Create New")), "html", null, true);
            yield "
                        </a>
                    ";
        }
        // line 66
        yield "                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    ";
        // line 72
        if (array_key_exists("stats", $context)) {
            // line 73
            yield "    <div class=\"card-body pb-0\">
        <div class=\"row mb-4\">
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);\">
                                <i class=\"fas fa-layer-group text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Total Items</h6>
                                <h4 class=\"mb-0\" style=\"color: #011a2d;\">";
            // line 85
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["stats"] ?? null), "total", [], "any", true, true, false, 85)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 85, $this->source); })()), "total", [], "any", false, false, false, 85), 0)) : (0)), "html", null, true);
            yield "</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\">
                                <i class=\"fas fa-check-circle text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Active</h6>
                                <h4 class=\"mb-0 text-success\">";
            // line 101
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["stats"] ?? null), "active", [], "any", true, true, false, 101)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 101, $this->source); })()), "active", [], "any", false, false, false, 101), 0)) : (0)), "html", null, true);
            yield "</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\">
                                <i class=\"fas fa-pause-circle text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Inactive</h6>
                                <h4 class=\"mb-0 text-secondary\">";
            // line 117
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["stats"] ?? null), "inactive", [], "any", true, true, false, 117)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 117, $this->source); })()), "inactive", [], "any", false, false, false, 117), 0)) : (0)), "html", null, true);
            yield "</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);\">
                                <i class=\"fas fa-clock text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Recent</h6>
                                <h4 class=\"mb-0\" style=\"color: #a90418;\">";
            // line 133
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["stats"] ?? null), "recent", [], "any", true, true, false, 133)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 133, $this->source); })()), "recent", [], "any", false, false, false, 133), 0)) : (0)), "html", null, true);
            yield "</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    ";
        }
        // line 142
        yield "
    <!-- Table Content Area -->
    <div class=\"card-body pt-0\">
        <!-- This is where the table content will be placed -->
    </div>
</div>

<!-- Responsive Design Styles -->
<style>
@media (max-width: 768px) {
    .card-header .row {
        text-align: center;
    }
    
    .card-header .col-md-6:last-child {
        margin-top: 1rem;
    }
    
    .search-container {
        margin-right: 0 !important;
        margin-bottom: 1rem;
    }
    
    .search-container .input-group {
        width: 100% !important;
    }
    
    .admin-btn-create {
        width: 100% !important;
    }
}

@media (max-width: 576px) {
    .card-header h2 {
        font-size: 1.5rem !important;
    }
    
    .card-header h2 i {
        font-size: 1.5rem !important;
    }
    
    .admin-stat-card .card-body {
        padding: 1rem 0.75rem;
    }
    
    .admin-stat-icon {
        width: 40px !important;
        height: 40px !important;
    }
    
    .admin-stat-card h4 {
        font-size: 1.25rem;
    }
}
</style>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "components/admin_table_header.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  239 => 142,  227 => 133,  208 => 117,  189 => 101,  170 => 85,  156 => 73,  154 => 72,  146 => 66,  138 => 63,  131 => 60,  129 => 59,  125 => 57,  122 => 56,  111 => 53,  107 => 52,  100 => 49,  95 => 48,  93 => 47,  75 => 32,  61 => 21,  57 => 20,  48 => 13,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{#
    Capitol Academy Admin Table Header Component
    Standardized header with search, statistics, and action buttons
    
    Parameters:
    - title: Page title text
    - icon: FontAwesome icon class (e.g., 'fas fa-users')
    - search_placeholder: Search input placeholder text
    - create_button: Object with {url, text, icon} properties
    - additional_buttons: Array of button objects with {url, text, icon, title} properties
    - stats: Object with {total, active, inactive, recent} counts
#}

<!-- Professional Header Card -->
<div class=\"card border-0 shadow-lg mb-4\">
    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
        <div class=\"row align-items-center\">
            <div class=\"col-md-6\">
                <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                    <i class=\"{{ icon|default('fas fa-table') }} mr-3\" style=\"font-size: 2rem;\"></i>
                    {{ title|default('Admin Management') }}
                </h2>
            </div>
            <div class=\"col-md-6\">
                <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                    <!-- Professional Search -->
                    <div class=\"search-container me-3 mb-2 mb-md-0\" style=\"position: relative;\">
                        <div class=\"input-group\" style=\"width: 320px;\">
                            <input type=\"text\"
                                   id=\"professional-search\"
                                   class=\"form-control form-control-lg admin-search-input\"
                                   placeholder=\"{{ search_placeholder|default('Search...') }}\"
                                   style=\"border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\"
                                        class=\"btn btn-lg admin-search-btn\"
                                        id=\"search-clear-btn\"
                                        style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;\">
                                    <i class=\"fas fa-search\"></i>
                                </button>
                            </div>
                        </div>
                        <div id=\"search-results-count\" class=\"text-muted small mt-1\" style=\"display: none;\"></div>
                    </div>
                    
                    <!-- Additional Action Buttons -->
                    {% if additional_buttons is defined and additional_buttons|length > 0 %}
                        {% for button in additional_buttons %}
                            <a href=\"{{ button.url }}\" 
                               class=\"btn btn-outline-light me-2 mb-2 mb-md-0\" 
                               style=\"font-weight: 500; border-radius: 6px; padding: 0.5rem 1rem; transition: all 0.3s ease;\"
                               title=\"{{ button.title|default('') }}\">
                                <i class=\"{{ button.icon }} me-2\"></i>{{ button.text }}
                            </a>
                        {% endfor %}
                    {% endif %}
                    
                    <!-- Create Button -->
                    {% if create_button is defined %}
                        <a href=\"{{ create_button.url }}\" 
                           class=\"btn btn-light admin-btn-create mb-2 mb-md-0\" 
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                            <i class=\"{{ create_button.icon|default('fas fa-plus') }} me-2\"></i>{{ create_button.text|default('Create New') }}
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    {% if stats is defined %}
    <div class=\"card-body pb-0\">
        <div class=\"row mb-4\">
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);\">
                                <i class=\"fas fa-layer-group text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Total Items</h6>
                                <h4 class=\"mb-0\" style=\"color: #011a2d;\">{{ stats.total|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\">
                                <i class=\"fas fa-check-circle text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Active</h6>
                                <h4 class=\"mb-0 text-success\">{{ stats.active|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\">
                                <i class=\"fas fa-pause-circle text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Inactive</h6>
                                <h4 class=\"mb-0 text-secondary\">{{ stats.inactive|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class=\"col-xl-3 col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm admin-stat-card\">
                    <div class=\"card-body\">
                        <div class=\"d-flex align-items-center\">
                            <div class=\"admin-stat-icon me-3\"
                                 style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);\">
                                <i class=\"fas fa-clock text-white\"></i>
                            </div>
                            <div>
                                <h6 class=\"text-muted mb-1\">Recent</h6>
                                <h4 class=\"mb-0\" style=\"color: #a90418;\">{{ stats.recent|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Table Content Area -->
    <div class=\"card-body pt-0\">
        <!-- This is where the table content will be placed -->
    </div>
</div>

<!-- Responsive Design Styles -->
<style>
@media (max-width: 768px) {
    .card-header .row {
        text-align: center;
    }
    
    .card-header .col-md-6:last-child {
        margin-top: 1rem;
    }
    
    .search-container {
        margin-right: 0 !important;
        margin-bottom: 1rem;
    }
    
    .search-container .input-group {
        width: 100% !important;
    }
    
    .admin-btn-create {
        width: 100% !important;
    }
}

@media (max-width: 576px) {
    .card-header h2 {
        font-size: 1.5rem !important;
    }
    
    .card-header h2 i {
        font-size: 1.5rem !important;
    }
    
    .admin-stat-card .card-body {
        padding: 1rem 0.75rem;
    }
    
    .admin-stat-icon {
        width: 40px !important;
        height: 40px !important;
    }
    
    .admin-stat-card h4 {
        font-size: 1.25rem;
    }
}
</style>
", "components/admin_table_header.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\components\\admin_table_header.html.twig");
    }
}
