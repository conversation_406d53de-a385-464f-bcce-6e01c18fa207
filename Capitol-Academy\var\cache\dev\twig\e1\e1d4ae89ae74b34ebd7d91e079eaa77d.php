<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* @Twig/Exception/error404.html.twig */
class __TwigTemplate_a82137c5d6393dfebec82ebd027e7213 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "@Twig/Exception/error404.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "@Twig/Exception/error404.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Page Not Found - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "The page you're looking for could not be found. Return to Capitol Academy's homepage or explore our trading courses and market analysis.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 8
        yield "<div class=\"error-page-container\">
    <!-- Hero Section -->
    <section class=\"error-hero-section py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%); min-height: 70vh;\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-8 mx-auto text-center\">
                    <div class=\"error-content text-white\">
                        <!-- 404 Number -->
                        <h1 class=\"error-number display-1 fw-bold mb-4\" style=\"font-size: 8rem; color: #a90418; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\">
                            404
                        </h1>
                        
                        <!-- Error Title -->
                        <h2 class=\"error-title h1 fw-bold mb-4\">
                            Page Not Found
                        </h2>
                        
                        <!-- Error Description -->
                        <p class=\"error-description lead mb-5\" style=\"font-size: 1.3rem; line-height: 1.8; opacity: 0.9;\">
                            The page you're looking for doesn't exist or has been moved. 
                            Don't worry, let's get you back on track with your financial education journey.
                        </p>
                        
                        <!-- Action Buttons -->
                        <div class=\"error-actions d-flex flex-wrap justify-content-center gap-3\">
                            <a href=\"";
        // line 33
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\" class=\"btn btn-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-home me-2\"></i>
                                Return Home
                            </a>
                            <a href=\"";
        // line 37
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-graduation-cap me-2\"></i>
                                Browse Courses
                            </a>
                            <a href=\"";
        // line 41
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-chart-line me-2\"></i>
                                Market Analysis
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Helpful Links Section -->
    <section class=\"helpful-links-section py-5\" style=\"background: white;\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-8 mx-auto text-center mb-5\">
                    <h3 class=\"h2 fw-bold mb-4\" style=\"color: #011a2d;\">
                        Popular Pages
                    </h3>
                    <p class=\"lead\" style=\"color: #6c757d;\">
                        Here are some popular destinations that might interest you
                    </p>
                </div>
            </div>
            
            <div class=\"row g-4\">
                <!-- Trading Courses -->
                <div class=\"col-lg-4 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-graduation-cap\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Trading Courses</h5>
                            <p class=\"text-muted mb-4\">
                                Master financial markets with our comprehensive course catalog
                            </p>
                            <a href=\"";
        // line 78
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-outline-primary\">
                                Explore Courses
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Market Analysis -->
                <div class=\"col-lg-4 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-chart-line\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Market Analysis</h5>
                            <p class=\"text-muted mb-4\">
                                Stay ahead with expert insights on stocks, forex, and crypto
                            </p>
                            <a href=\"";
        // line 96
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "\" class=\"btn btn-outline-primary\">
                                Read Analysis
                            </a>
                        </div>
                    </div>
                </div>

                <!-- About Us -->
                <div class=\"col-lg-4 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-users\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">About Capitol Academy</h5>
                            <p class=\"text-muted mb-4\">
                                Learn about our mission and expert instructors
                            </p>
                            <a href=\"";
        // line 114
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_about");
        yield "\" class=\"btn btn-outline-primary\">
                                Learn More
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class=\"error-contact-section py-5\" style=\"background: #f8f9fa;\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-6 mx-auto text-center\">
                    <h4 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">
                        Still Can't Find What You're Looking For?
                    </h4>
                    <p class=\"mb-4\" style=\"color: #6c757d;\">
                        Our team is here to help you navigate your financial education journey
                    </p>
                    <a href=\"";
        // line 135
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" class=\"btn btn-primary btn-lg px-4 py-3\" style=\"background: #011a2d; border-color: #011a2d; border-radius: 8px; font-weight: 600;\">
                        <i class=\"fas fa-envelope me-2\"></i>
                        Contact Us
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* 404 Error Page Styles */
.error-page-container {
    min-height: 100vh;
}

.error-number {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.helpful-link-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.helpful-link-card .icon-container {
    transition: transform 0.3s ease;
}

.helpful-link-card:hover .icon-container {
    transform: scale(1.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .error-number {
        font-size: 5rem !important;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1.1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "@Twig/Exception/error404.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  271 => 135,  247 => 114,  226 => 96,  205 => 78,  165 => 41,  158 => 37,  151 => 33,  124 => 8,  111 => 7,  88 => 5,  65 => 3,  42 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Page Not Found - Capitol Academy{% endblock %}

{% block meta_description %}The page you're looking for could not be found. Return to Capitol Academy's homepage or explore our trading courses and market analysis.{% endblock %}

{% block body %}
<div class=\"error-page-container\">
    <!-- Hero Section -->
    <section class=\"error-hero-section py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%); min-height: 70vh;\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-8 mx-auto text-center\">
                    <div class=\"error-content text-white\">
                        <!-- 404 Number -->
                        <h1 class=\"error-number display-1 fw-bold mb-4\" style=\"font-size: 8rem; color: #a90418; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\">
                            404
                        </h1>
                        
                        <!-- Error Title -->
                        <h2 class=\"error-title h1 fw-bold mb-4\">
                            Page Not Found
                        </h2>
                        
                        <!-- Error Description -->
                        <p class=\"error-description lead mb-5\" style=\"font-size: 1.3rem; line-height: 1.8; opacity: 0.9;\">
                            The page you're looking for doesn't exist or has been moved. 
                            Don't worry, let's get you back on track with your financial education journey.
                        </p>
                        
                        <!-- Action Buttons -->
                        <div class=\"error-actions d-flex flex-wrap justify-content-center gap-3\">
                            <a href=\"{{ path('app_home') }}\" class=\"btn btn-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-home me-2\"></i>
                                Return Home
                            </a>
                            <a href=\"{{ path('app_courses') }}\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-graduation-cap me-2\"></i>
                                Browse Courses
                            </a>
                            <a href=\"{{ path('app_market_analysis') }}\" class=\"btn btn-outline-light btn-lg px-4 py-3\" style=\"border-radius: 8px; font-weight: 600;\">
                                <i class=\"fas fa-chart-line me-2\"></i>
                                Market Analysis
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Helpful Links Section -->
    <section class=\"helpful-links-section py-5\" style=\"background: white;\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-8 mx-auto text-center mb-5\">
                    <h3 class=\"h2 fw-bold mb-4\" style=\"color: #011a2d;\">
                        Popular Pages
                    </h3>
                    <p class=\"lead\" style=\"color: #6c757d;\">
                        Here are some popular destinations that might interest you
                    </p>
                </div>
            </div>
            
            <div class=\"row g-4\">
                <!-- Trading Courses -->
                <div class=\"col-lg-4 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-graduation-cap\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Trading Courses</h5>
                            <p class=\"text-muted mb-4\">
                                Master financial markets with our comprehensive course catalog
                            </p>
                            <a href=\"{{ path('app_courses') }}\" class=\"btn btn-outline-primary\">
                                Explore Courses
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Market Analysis -->
                <div class=\"col-lg-4 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-chart-line\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">Market Analysis</h5>
                            <p class=\"text-muted mb-4\">
                                Stay ahead with expert insights on stocks, forex, and crypto
                            </p>
                            <a href=\"{{ path('app_market_analysis') }}\" class=\"btn btn-outline-primary\">
                                Read Analysis
                            </a>
                        </div>
                    </div>
                </div>

                <!-- About Us -->
                <div class=\"col-lg-4 col-md-6\">
                    <div class=\"helpful-link-card card border-0 h-100 shadow-sm\" style=\"transition: transform 0.3s ease;\">
                        <div class=\"card-body p-4 text-center\">
                            <div class=\"icon-container mb-3\">
                                <i class=\"fas fa-users\" style=\"font-size: 3rem; color: #011a2d;\"></i>
                            </div>
                            <h5 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">About Capitol Academy</h5>
                            <p class=\"text-muted mb-4\">
                                Learn about our mission and expert instructors
                            </p>
                            <a href=\"{{ path('app_about') }}\" class=\"btn btn-outline-primary\">
                                Learn More
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class=\"error-contact-section py-5\" style=\"background: #f8f9fa;\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-6 mx-auto text-center\">
                    <h4 class=\"fw-bold mb-3\" style=\"color: #011a2d;\">
                        Still Can't Find What You're Looking For?
                    </h4>
                    <p class=\"mb-4\" style=\"color: #6c757d;\">
                        Our team is here to help you navigate your financial education journey
                    </p>
                    <a href=\"{{ path('app_contact') }}\" class=\"btn btn-primary btn-lg px-4 py-3\" style=\"background: #011a2d; border-color: #011a2d; border-radius: 8px; font-weight: 600;\">
                        <i class=\"fas fa-envelope me-2\"></i>
                        Contact Us
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* 404 Error Page Styles */
.error-page-container {
    min-height: 100vh;
}

.error-number {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.helpful-link-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.helpful-link-card .icon-container {
    transition: transform 0.3s ease;
}

.helpful-link-card:hover .icon-container {
    transform: scale(1.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .error-number {
        font-size: 5rem !important;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1.1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
{% endblock %}
", "@Twig/Exception/error404.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\bundles\\TwigBundle\\Exception\\error404.html.twig");
    }
}
