<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* components/seo_meta.html.twig */
class __TwigTemplate_08c4e8e1c30178da75b19aa328767ec8 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/seo_meta.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/seo_meta.html.twig"));

        // line 2
        $context["page_title"] = ((        $this->unwrap()->hasBlock("title", $context, $blocks)) ? (        $this->unwrap()->renderBlock("title", $context, $blocks)) : ("Capitol Academy - Financial Markets Education"));
        // line 3
        $context["page_description"] = ((        $this->unwrap()->hasBlock("meta_description", $context, $blocks)) ? (        $this->unwrap()->renderBlock("meta_description", $context, $blocks)) : ("Capitol Academy offers exceptional education programs in financial markets, trading strategies, and professional development for traders worldwide."));
        // line 4
        $context["page_keywords"] = ((        $this->unwrap()->hasBlock("meta_keywords", $context, $blocks)) ? (        $this->unwrap()->renderBlock("meta_keywords", $context, $blocks)) : ("financial markets, trading education, forex, technical analysis, fundamental analysis, capitol academy"));
        // line 5
        $context["page_url"] = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 5, $this->source); })()), "request", [], "any", false, false, false, 5), "uri", [], "any", false, false, false, 5);
        // line 6
        $context["site_name"] = "Capitol Academy";
        // line 7
        yield "
<!-- Open Graph / Facebook -->
<meta property=\"og:type\" content=\"website\">
<meta property=\"og:url\" content=\"";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_url"]) || array_key_exists("page_url", $context) ? $context["page_url"] : (function () { throw new RuntimeError('Variable "page_url" does not exist.', 10, $this->source); })()), "html", null, true);
        yield "\">
<meta property=\"og:title\" content=\"";
        // line 11
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_title"]) || array_key_exists("page_title", $context) ? $context["page_title"] : (function () { throw new RuntimeError('Variable "page_title" does not exist.', 11, $this->source); })()), "html", null, true);
        yield "\">
<meta property=\"og:description\" content=\"";
        // line 12
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_description"]) || array_key_exists("page_description", $context) ? $context["page_description"] : (function () { throw new RuntimeError('Variable "page_description" does not exist.', 12, $this->source); })()), "html", null, true);
        yield "\">
<meta property=\"og:image\" content=\"";
        // line 13
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\HttpFoundationExtension']->generateAbsoluteUrl($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("favicons/android-chrome-512x512.png")), "html", null, true);
        yield "\">
<meta property=\"og:site_name\" content=\"";
        // line 14
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["site_name"]) || array_key_exists("site_name", $context) ? $context["site_name"] : (function () { throw new RuntimeError('Variable "site_name" does not exist.', 14, $this->source); })()), "html", null, true);
        yield "\">
<meta property=\"og:locale\" content=\"en_US\">

<!-- Twitter -->
<meta property=\"twitter:card\" content=\"summary_large_image\">
<meta property=\"twitter:url\" content=\"";
        // line 19
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_url"]) || array_key_exists("page_url", $context) ? $context["page_url"] : (function () { throw new RuntimeError('Variable "page_url" does not exist.', 19, $this->source); })()), "html", null, true);
        yield "\">
<meta property=\"twitter:title\" content=\"";
        // line 20
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_title"]) || array_key_exists("page_title", $context) ? $context["page_title"] : (function () { throw new RuntimeError('Variable "page_title" does not exist.', 20, $this->source); })()), "html", null, true);
        yield "\">
<meta property=\"twitter:description\" content=\"";
        // line 21
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_description"]) || array_key_exists("page_description", $context) ? $context["page_description"] : (function () { throw new RuntimeError('Variable "page_description" does not exist.', 21, $this->source); })()), "html", null, true);
        yield "\">
<meta property=\"twitter:image\" content=\"";
        // line 22
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\HttpFoundationExtension']->generateAbsoluteUrl($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("favicons/android-chrome-512x512.png")), "html", null, true);
        yield "\">

<!-- LinkedIn -->
<meta property=\"linkedin:owner\" content=\"capitol-academy-tunisie\">

<!-- Additional SEO Meta Tags -->
<meta name=\"author\" content=\"Capitol Academy\">
<meta name=\"publisher\" content=\"Capitol Academy\">
<meta name=\"copyright\" content=\"Capitol Academy ";
        // line 30
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "Y"), "html", null, true);
        yield "\">
<meta name=\"robots\" content=\"index, follow\">
<meta name=\"googlebot\" content=\"index, follow\">
<meta name=\"revisit-after\" content=\"7 days\">
<meta name=\"rating\" content=\"general\">
<meta name=\"distribution\" content=\"global\">

<!-- Canonical URL -->
<link rel=\"canonical\" href=\"";
        // line 38
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_url"]) || array_key_exists("page_url", $context) ? $context["page_url"] : (function () { throw new RuntimeError('Variable "page_url" does not exist.', 38, $this->source); })()), "html", null, true);
        yield "\">

<!-- Preconnect to external domains for performance -->
<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">
<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>
<link rel=\"preconnect\" href=\"https://cdn.jsdelivr.net\">
<link rel=\"preconnect\" href=\"https://cdnjs.cloudflare.com\">

<!-- DNS Prefetch for better performance -->
<link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\">
<link rel=\"dns-prefetch\" href=\"//cdn.jsdelivr.net\">
<link rel=\"dns-prefetch\" href=\"//cdnjs.cloudflare.com\">

<!-- Structured Data for Organization -->
<script type=\"application/ld+json\">
{
  \"@context\": \"https://schema.org\",
  \"@type\": \"EducationalOrganization\",
  \"name\": \"Capitol Academy\",
  \"description\": \"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["page_description"]) || array_key_exists("page_description", $context) ? $context["page_description"] : (function () { throw new RuntimeError('Variable "page_description" does not exist.', 57, $this->source); })()), "html", null, true);
        yield "\",
  \"url\": \"";
        // line 58
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 58, $this->source); })()), "request", [], "any", false, false, false, 58), "schemeAndHttpHost", [], "any", false, false, false, 58), "html", null, true);
        yield "\",
  \"logo\": \"";
        // line 59
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\HttpFoundationExtension']->generateAbsoluteUrl($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("favicons/android-chrome-512x512.png")), "html", null, true);
        yield "\",
  \"image\": \"";
        // line 60
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\HttpFoundationExtension']->generateAbsoluteUrl($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("favicons/android-chrome-512x512.png")), "html", null, true);
        yield "\",
  \"sameAs\": [
    \"https://tn.linkedin.com/company/capitol-academy-tunisie\"
  ],
  \"address\": {
    \"@type\": \"PostalAddress\",
    \"addressCountry\": \"TN\"
  },
  \"contactPoint\": {
    \"@type\": \"ContactPoint\",
    \"contactType\": \"customer service\",
    \"email\": \"<EMAIL>\"
  }
}
</script>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "components/seo_meta.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  155 => 60,  151 => 59,  147 => 58,  143 => 57,  121 => 38,  110 => 30,  99 => 22,  95 => 21,  91 => 20,  87 => 19,  79 => 14,  75 => 13,  71 => 12,  67 => 11,  63 => 10,  58 => 7,  56 => 6,  54 => 5,  52 => 4,  50 => 3,  48 => 2,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{# SEO Meta Tags Component #}
{% set page_title = block('title') ?? 'Capitol Academy - Financial Markets Education' %}
{% set page_description = block('meta_description') ?? 'Capitol Academy offers exceptional education programs in financial markets, trading strategies, and professional development for traders worldwide.' %}
{% set page_keywords = block('meta_keywords') ?? 'financial markets, trading education, forex, technical analysis, fundamental analysis, capitol academy' %}
{% set page_url = app.request.uri %}
{% set site_name = 'Capitol Academy' %}

<!-- Open Graph / Facebook -->
<meta property=\"og:type\" content=\"website\">
<meta property=\"og:url\" content=\"{{ page_url }}\">
<meta property=\"og:title\" content=\"{{ page_title }}\">
<meta property=\"og:description\" content=\"{{ page_description }}\">
<meta property=\"og:image\" content=\"{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}\">
<meta property=\"og:site_name\" content=\"{{ site_name }}\">
<meta property=\"og:locale\" content=\"en_US\">

<!-- Twitter -->
<meta property=\"twitter:card\" content=\"summary_large_image\">
<meta property=\"twitter:url\" content=\"{{ page_url }}\">
<meta property=\"twitter:title\" content=\"{{ page_title }}\">
<meta property=\"twitter:description\" content=\"{{ page_description }}\">
<meta property=\"twitter:image\" content=\"{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}\">

<!-- LinkedIn -->
<meta property=\"linkedin:owner\" content=\"capitol-academy-tunisie\">

<!-- Additional SEO Meta Tags -->
<meta name=\"author\" content=\"Capitol Academy\">
<meta name=\"publisher\" content=\"Capitol Academy\">
<meta name=\"copyright\" content=\"Capitol Academy {{ 'now'|date('Y') }}\">
<meta name=\"robots\" content=\"index, follow\">
<meta name=\"googlebot\" content=\"index, follow\">
<meta name=\"revisit-after\" content=\"7 days\">
<meta name=\"rating\" content=\"general\">
<meta name=\"distribution\" content=\"global\">

<!-- Canonical URL -->
<link rel=\"canonical\" href=\"{{ page_url }}\">

<!-- Preconnect to external domains for performance -->
<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">
<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>
<link rel=\"preconnect\" href=\"https://cdn.jsdelivr.net\">
<link rel=\"preconnect\" href=\"https://cdnjs.cloudflare.com\">

<!-- DNS Prefetch for better performance -->
<link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\">
<link rel=\"dns-prefetch\" href=\"//cdn.jsdelivr.net\">
<link rel=\"dns-prefetch\" href=\"//cdnjs.cloudflare.com\">

<!-- Structured Data for Organization -->
<script type=\"application/ld+json\">
{
  \"@context\": \"https://schema.org\",
  \"@type\": \"EducationalOrganization\",
  \"name\": \"Capitol Academy\",
  \"description\": \"{{ page_description }}\",
  \"url\": \"{{ app.request.schemeAndHttpHost }}\",
  \"logo\": \"{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}\",
  \"image\": \"{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}\",
  \"sameAs\": [
    \"https://tn.linkedin.com/company/capitol-academy-tunisie\"
  ],
  \"address\": {
    \"@type\": \"PostalAddress\",
    \"addressCountry\": \"TN\"
  },
  \"contactPoint\": {
    \"@type\": \"ContactPoint\",
    \"contactType\": \"customer service\",
    \"email\": \"<EMAIL>\"
  }
}
</script>
", "components/seo_meta.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\components\\seo_meta.html.twig");
    }
}
