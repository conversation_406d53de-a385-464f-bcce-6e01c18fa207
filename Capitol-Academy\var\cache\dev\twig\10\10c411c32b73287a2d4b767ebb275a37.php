<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/courses/index.html.twig */
class __TwigTemplate_fdb94181d8c049609139505bbec36953 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Courses Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Courses Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Courses</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Courses Management", "page_icon" => "fas fa-graduation-cap", "search_placeholder" => "Search...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_create"), "text" => "Add New Course", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Courses", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 25
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 25, $this->source); })())), "icon" => "fas fa-layer-group", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 32
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 32, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 32, $this->source); })()), "isActive", [], "any", false, false, false, 32); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Inactive", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 39
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 39, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 39, $this->source); })()), "isActive", [], "any", false, false, false, 39); })), "icon" => "fas fa-pause-circle", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 46, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-clock", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/courses/index.html.twig", 54, "1051144456")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 113
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 114
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.course-row',
        ['.course-title', '.course-code', '.course-category']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Course management functions
function toggleCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeCourseStatusToggle(courseId);
    });
}

function deleteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeCourseDelete(courseId);
    });
}

// Actual execution functions
function executeCourseStatusToggle(courseId) {
    fetch(`/admin/courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the course status');
    });
}

function executeCourseDelete(courseId) {
    fetch(`/admin/courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the course');
    });
}


</script>


";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  187 => 114,  174 => 113,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Courses Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Courses Management',
    'page_icon': 'fas fa-graduation-cap',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_course_create'),
        'text': 'Add New Course',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Courses',
            'value': courses|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': courses|filter(course => course.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': courses|filter(course => not course.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Level'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set row_cells = [
                {
                    'content': '<code class=\"course-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">' ~ course.code ~ '</code>'
                },
                {
                    'content': '<h6 class=\"course-title mb-0 font-weight-bold text-dark\">' ~ course.title ~ '</h6>'
                },
                {
                    'content': '<span class=\"course-category text-dark font-weight-medium\">' ~ (course.category|default('General')) ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ (course.level|default('Beginner')) ~ '</span>'
                },
                {
                    'content': course.isActive ?
                        '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_course_preview', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Course\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_course_edit', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Course\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Course\" onclick=\"toggleCourseStatus(' ~ course.id ~ ', \\'' ~ course.title ~ '\\', ' ~ course.isActive ~ ')\"><i class=\"fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Course\" onclick=\"deleteCourse(' ~ course.id ~ ', \\'' ~ course.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'course-row',
            'empty_message': 'No courses found',
            'empty_icon': 'fas fa-graduation-cap',
            'empty_description': 'Get started by adding your first course.',
            'search_config': {
                'fields': ['.course-title', '.course-code', '.course-category']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.course-row',
        ['.course-title', '.course-code', '.course-category']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Course management functions
function toggleCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeCourseStatusToggle(courseId);
    });
}

function deleteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeCourseDelete(courseId);
    });
}

// Actual execution functions
function executeCourseStatusToggle(courseId) {
    fetch(`/admin/courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the course status');
    });
}

function executeCourseDelete(courseId) {
    fetch(`/admin/courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the course');
    });
}


</script>


{% endblock %}", "admin/courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\courses\\index.html.twig");
    }
}


/* admin/courses/index.html.twig */
class __TwigTemplate_fdb94181d8c049609139505bbec36953___1051144456 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "        <!-- Standardized Table -->
        ";
        // line 57
        $context["table_headers"] = [["text" => "Code"], ["text" => "Title"], ["text" => "Category"], ["text" => "Level"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 65
        yield "
        ";
        // line 66
        $context["table_rows"] = [];
        // line 67
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 67, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
            // line 68
            yield "            ";
            $context["row_cells"] = [["content" => (("<code class=\"course-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 70
$context["course"], "code", [], "any", false, false, false, 70)) . "</code>")], ["content" => (("<h6 class=\"course-title mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["course"], "title", [], "any", false, false, false, 73)) . "</h6>")], ["content" => (("<span class=\"course-category text-dark font-weight-medium\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 76
$context["course"], "category", [], "any", true, true, false, 76)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 76), "General")) : ("General"))) . "</span>")], ["content" => (("<span class=\"text-dark font-weight-medium\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 79
$context["course"], "level", [], "any", true, true, false, 79)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 79), "Beginner")) : ("Beginner"))) . "</span>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 82
$context["course"], "isActive", [], "any", false, false, false, 82)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>") : ("<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>"))], ["content" => (((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 88
$context["course"], "code", [], "any", false, false, false, 88)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Course\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 89
$context["course"], "code", [], "any", false, false, false, 89)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Course\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 90
$context["course"], "isActive", [], "any", false, false, false, 90)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . "; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 90)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " Course\" onclick=\"toggleCourseStatus(") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "id", [], "any", false, false, false, 90)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 90)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 90)) . ")\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 90)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"))) . "\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Course\" onclick=\"deleteCourse(") . CoreExtension::getAttribute($this->env, $this->source,             // line 91
$context["course"], "id", [], "any", false, false, false, 91)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 91)) . "')\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 95
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 95, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 95, $this->source); })())]]);
            // line 96
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 97
        yield "
        ";
        // line 98
        yield from $this->load("components/admin_table.html.twig", 98)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 99
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 99, $this->source); })()), "rows" =>         // line 100
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 100, $this->source); })()), "row_class" => "course-row", "empty_message" => "No courses found", "empty_icon" => "fas fa-graduation-cap", "empty_description" => "Get started by adding your first course.", "search_config" => ["fields" => [".course-title", ".course-code", ".course-category"]]]));
        // line 109
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  662 => 109,  660 => 100,  659 => 99,  658 => 98,  655 => 97,  649 => 96,  646 => 95,  643 => 91,  641 => 90,  639 => 89,  637 => 88,  635 => 82,  634 => 79,  633 => 76,  632 => 73,  631 => 70,  629 => 68,  624 => 67,  622 => 66,  619 => 65,  617 => 57,  614 => 56,  601 => 55,  578 => 54,  187 => 114,  174 => 113,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Courses Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Courses Management',
    'page_icon': 'fas fa-graduation-cap',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_course_create'),
        'text': 'Add New Course',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Courses',
            'value': courses|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': courses|filter(course => course.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': courses|filter(course => not course.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Level'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set row_cells = [
                {
                    'content': '<code class=\"course-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">' ~ course.code ~ '</code>'
                },
                {
                    'content': '<h6 class=\"course-title mb-0 font-weight-bold text-dark\">' ~ course.title ~ '</h6>'
                },
                {
                    'content': '<span class=\"course-category text-dark font-weight-medium\">' ~ (course.category|default('General')) ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ (course.level|default('Beginner')) ~ '</span>'
                },
                {
                    'content': course.isActive ?
                        '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_course_preview', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Course\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_course_edit', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Course\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Course\" onclick=\"toggleCourseStatus(' ~ course.id ~ ', \\'' ~ course.title ~ '\\', ' ~ course.isActive ~ ')\"><i class=\"fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Course\" onclick=\"deleteCourse(' ~ course.id ~ ', \\'' ~ course.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'course-row',
            'empty_message': 'No courses found',
            'empty_icon': 'fas fa-graduation-cap',
            'empty_description': 'Get started by adding your first course.',
            'search_config': {
                'fields': ['.course-title', '.course-code', '.course-category']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.course-row',
        ['.course-title', '.course-code', '.course-category']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Course management functions
function toggleCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeCourseStatusToggle(courseId);
    });
}

function deleteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeCourseDelete(courseId);
    });
}

// Actual execution functions
function executeCourseStatusToggle(courseId) {
    fetch(`/admin/courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the course status');
    });
}

function executeCourseDelete(courseId) {
    fetch(`/admin/courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the course');
    });
}


</script>


{% endblock %}", "admin/courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\courses\\index.html.twig");
    }
}
