<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* emails/test_email.html.twig */
class __TwigTemplate_f3878f8da6b56fbcfdd6571a80a2d1d4 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "emails/test_email.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "emails/test_email.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Email Test</title>
    <style>
        body {
            font-family: 'Calibri', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #011a2d;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            color: #011a2d;
            font-size: 28px;
            font-weight: bold;
            font-family: 'Montserrat', sans-serif;
        }
        .content {
            margin-bottom: 30px;
        }
        .test-info {
            background: #f6f7f9;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #a90418;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .success-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class=\"email-container\">
        <div class=\"header\">
            <div class=\"logo\">Capitol Academy</div>
            <p style=\"margin: 10px 0 0 0; color: #6c757d;\">Professional Trading Education</p>
        </div>

        <div class=\"content\">
            <h2 style=\"color: #011a2d; margin-bottom: 20px;\">Email System Test</h2>
            
            <div class=\"success-badge\">✓ Email System Working</div>
            
            <p>This is a test email to verify that the Capitol Academy email system is functioning correctly.</p>
            
            <div class=\"test-info\">
                <h4 style=\"color: #a90418; margin-top: 0;\">Test Details:</h4>
                <ul style=\"margin: 10px 0;\">
                    <li><strong>Test Time:</strong> ";
        // line 81
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate((isset($context["test_time"]) || array_key_exists("test_time", $context) ? $context["test_time"] : (function () { throw new RuntimeError('Variable "test_time" does not exist.', 81, $this->source); })()), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</li>
                    <li><strong>Initiated by:</strong> ";
        // line 82
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["admin_name"]) || array_key_exists("admin_name", $context) ? $context["admin_name"] : (function () { throw new RuntimeError('Variable "admin_name" does not exist.', 82, $this->source); })()), "html", null, true);
        yield "</li>
                    <li><strong>From Email:</strong> <EMAIL></li>
                    <li><strong>To Email:</strong> <EMAIL></li>
                    <li><strong>SMTP Configuration:</strong> Gmail SMTP with App Password</li>
                </ul>
            </div>

            <p>If you received this email, it means:</p>
            <ul>
                <li>✓ SMTP configuration is correct</li>
                <li>✓ Email credentials are working</li>
                <li>✓ Symfony Mailer is properly configured</li>
                <li>✓ Email delivery is successful</li>
            </ul>

            <p style=\"margin-top: 30px;\">
                <strong>Next Steps:</strong><br>
                You can now proceed with confidence that the email system is working correctly for:
            </p>
            <ul>
                <li>Contact form notifications</li>
                <li>User registration confirmations</li>
                <li>Password reset emails</li>
                <li>Order confirmation emails</li>
            </ul>
        </div>

        <div class=\"footer\">
            <p>
                <strong>Capitol Academy</strong><br>
                Professional Trading Education Platform<br>
                Registration #1264639G
            </p>
            <p style=\"margin-top: 15px; font-size: 12px;\">
                This is an automated test email from the Capitol Academy admin panel.
            </p>
        </div>
    </div>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "emails/test_email.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  134 => 82,  130 => 81,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Email Test</title>
    <style>
        body {
            font-family: 'Calibri', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #011a2d;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            color: #011a2d;
            font-size: 28px;
            font-weight: bold;
            font-family: 'Montserrat', sans-serif;
        }
        .content {
            margin-bottom: 30px;
        }
        .test-info {
            background: #f6f7f9;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #a90418;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .success-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class=\"email-container\">
        <div class=\"header\">
            <div class=\"logo\">Capitol Academy</div>
            <p style=\"margin: 10px 0 0 0; color: #6c757d;\">Professional Trading Education</p>
        </div>

        <div class=\"content\">
            <h2 style=\"color: #011a2d; margin-bottom: 20px;\">Email System Test</h2>
            
            <div class=\"success-badge\">✓ Email System Working</div>
            
            <p>This is a test email to verify that the Capitol Academy email system is functioning correctly.</p>
            
            <div class=\"test-info\">
                <h4 style=\"color: #a90418; margin-top: 0;\">Test Details:</h4>
                <ul style=\"margin: 10px 0;\">
                    <li><strong>Test Time:</strong> {{ test_time|date('F j, Y \\\\a\\\\t g:i A') }}</li>
                    <li><strong>Initiated by:</strong> {{ admin_name }}</li>
                    <li><strong>From Email:</strong> <EMAIL></li>
                    <li><strong>To Email:</strong> <EMAIL></li>
                    <li><strong>SMTP Configuration:</strong> Gmail SMTP with App Password</li>
                </ul>
            </div>

            <p>If you received this email, it means:</p>
            <ul>
                <li>✓ SMTP configuration is correct</li>
                <li>✓ Email credentials are working</li>
                <li>✓ Symfony Mailer is properly configured</li>
                <li>✓ Email delivery is successful</li>
            </ul>

            <p style=\"margin-top: 30px;\">
                <strong>Next Steps:</strong><br>
                You can now proceed with confidence that the email system is working correctly for:
            </p>
            <ul>
                <li>Contact form notifications</li>
                <li>User registration confirmations</li>
                <li>Password reset emails</li>
                <li>Order confirmation emails</li>
            </ul>
        </div>

        <div class=\"footer\">
            <p>
                <strong>Capitol Academy</strong><br>
                Professional Trading Education Platform<br>
                Registration #1264639G
            </p>
            <p style=\"margin-top: 15px; font-size: 12px;\">
                This is an automated test email from the Capitol Academy admin panel.
            </p>
        </div>
    </div>
</body>
</html>
", "emails/test_email.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\emails\\test_email.html.twig");
    }
}
