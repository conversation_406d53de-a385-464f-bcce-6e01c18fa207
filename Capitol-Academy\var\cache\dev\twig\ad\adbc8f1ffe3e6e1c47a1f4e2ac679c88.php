<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/instructor/new.html.twig */
class __TwigTemplate_de7fc67dda4750c606620b34e23722f1 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/new.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/new.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Instructor - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create New Instructor";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\">Instructors</a></li>
<li class=\"breadcrumb-item active\">Create Instructor</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user-tie mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Instructor
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Instructors Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Instructors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("instructor_create"), "html", null, true);
        yield "\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Name and Email Row -->
                            <div class=\"row\">
                                <!-- Full Name -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"name\" class=\"form-label\">
                                            <i class=\"fas fa-user text-primary mr-1\"></i>
                                            Full Name <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"name\"
                                               name=\"name\"
                                               placeholder=\"Enter instructor full name\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's full name.
                                        </div>
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"email\" class=\"form-label\">
                                            <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                            Email Address <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"email\"
                                               class=\"form-control enhanced-field\"
                                               id=\"email\"
                                               name=\"email\"
                                               placeholder=\"<EMAIL>\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid email address.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bio -->
                            <div class=\"form-group\">
                                <label for=\"bio\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Biography
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"bio\"
                                          name=\"bio\"
                                          rows=\"4\"
                                          placeholder=\"Enter instructor biography and background...\"
                                          style=\"border: 2px solid #ced4da;\"></textarea>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Provide a comprehensive biography of the instructor.
                                </small>
                            </div>

                            <!-- Specialization and LinkedIn Row -->
                            <div class=\"row\">
                                <!-- Specialization -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"specialization\" class=\"form-label\">
                                            <i class=\"fas fa-star text-primary mr-1\"></i>
                                            Specialization <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"specialization\"
                                               name=\"specialization\"
                                               placeholder=\"e.g., Technical Analysis & Risk Management\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's specialization.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Instructor's area of expertise.
                                        </small>
                                    </div>
                                </div>

                                <!-- LinkedIn URL -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"linkedinUrl\" class=\"form-label\">
                                            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
                                            LinkedIn Profile URL
                                        </label>
                                        <input type=\"url\"
                                               class=\"form-control enhanced-field\"
                                               id=\"linkedinUrl\"
                                               name=\"linkedinUrl\"
                                               placeholder=\"https://tn.linkedin.com/in/instructor-name\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Professional LinkedIn profile URL.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Phone and Display Order Row -->
                            <div class=\"row\">
                                <!-- Phone -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"phone\" class=\"form-label\">
                                            <i class=\"fas fa-phone text-primary mr-1\"></i>
                                            Phone Number
                                        </label>
                                        <input type=\"tel\"
                                               class=\"form-control enhanced-field\"
                                               id=\"phone\"
                                               name=\"phone\"
                                               placeholder=\"+216 70 123 456\"
                                               maxlength=\"20\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Contact phone number.
                                        </small>
                                    </div>
                                </div>

                                <!-- Display Order -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"displayOrder\" class=\"form-label\">
                                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                                            Display Order
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"displayOrder\"
                                               name=\"displayOrder\"
                                               placeholder=\"e.g., 1, 2, 3...\"
                                               min=\"1\"
                                               value=\"1\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Order in which instructor appears on the website.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Qualifications -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Qualifications
                                </label>
                                <div id=\"qualifications-container\">
                                    <div class=\"input-group mb-2 qualification-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"qualifications[]\"
                                               placeholder=\"e.g., CFA Charterholder, MBA Finance...\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-qualification\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Achievements -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-trophy text-primary mr-1\"></i>
                                    Achievements
                                </label>
                                <div id=\"achievements-container\">
                                    <div class=\"input-group mb-2 achievement-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"achievements[]\"
                                               placeholder=\"e.g., 15+ years trading experience, Published author...\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-achievement\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Profile Image Upload -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-camera text-primary mr-1\"></i>
                                    Profile Image
                                </label>
                                <div class=\"mb-3\">
                                    <input type=\"file\"
                                           class=\"form-control enhanced-field\"
                                           id=\"profileImageFile\"
                                           name=\"profileImageFile\"
                                           accept=\"image/jpeg,image/png,image/jpg\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">

                                    <div class=\"image-preview mt-2\" id=\"profile-preview\" style=\"display: none;\">
                                        <div class=\"professional-image-container\" style=\"width: 150px; height: 150px; border: 2px solid #1e3c72; border-radius: 50%; background: #f8f9fa; display: flex; align-items: center; justify-content: center; overflow: hidden; margin: 0 auto;\">
                                            <img src=\"\" alt=\"Profile Preview\" style=\"width: 100%; height: 100%; object-fit: cover; border-radius: 50%;\">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Instructor
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 293
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 305
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 306
        yield "<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Instructor';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Instructor...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Qualifications Management
    \$(document).on('click', '.add-qualification', function() {
        var container = \$('#qualifications-container');
        var newItem = `
            <div class=\"input-group mb-2 qualification-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"qualifications[]\"
                       placeholder=\"e.g., CFA Charterholder, MBA Finance...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-qualification\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-qualification\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
        updateQualificationButtons();
    });

    \$(document).on('click', '.remove-qualification', function() {
        if (\$('.qualification-item').length > 1) {
            \$(this).closest('.qualification-item').remove();
            updateQualificationButtons();
        }
    });

    function updateQualificationButtons() {
        \$('.qualification-item').each(function(index) {
            var removeBtn = \$(this).find('.remove-qualification');
            var addBtn = \$(this).find('.add-qualification');

            if (index === 0) {
                // First item: only show + button
                removeBtn.hide();
                addBtn.show();
            } else {
                // Other items: show both buttons
                removeBtn.show();
                addBtn.show();
            }
        });
    }

    // Dynamic Achievements Management
    \$(document).on('click', '.add-achievement', function() {
        var container = \$('#achievements-container');
        var newItem = `
            <div class=\"input-group mb-2 achievement-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"achievements[]\"
                       placeholder=\"e.g., 15+ years trading experience, Published author...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-achievement\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-achievement\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
        updateAchievementButtons();
    });

    \$(document).on('click', '.remove-achievement', function() {
        if (\$('.achievement-item').length > 1) {
            \$(this).closest('.achievement-item').remove();
            updateAchievementButtons();
        }
    });

    function updateAchievementButtons() {
        \$('.achievement-item').each(function(index) {
            var removeBtn = \$(this).find('.remove-achievement');
            var addBtn = \$(this).find('.add-achievement');

            if (index === 0) {
                // First item: only show + button
                removeBtn.hide();
                addBtn.show();
            } else {
                // Other items: show both buttons
                removeBtn.show();
                addBtn.show();
            }
        });
    }

    // Profile Image Preview Functionality
    \$('#profileImageFile').on('change', function() {
        previewImage(this, '#profile-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }

    // Initialize dynamic field buttons
    updateQualificationButtons();
    updateAchievementButtons();
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #2a5298 !important;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/new.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  506 => 306,  493 => 305,  471 => 293,  232 => 57,  215 => 43,  199 => 29,  189 => 25,  186 => 24,  182 => 23,  179 => 22,  169 => 18,  166 => 17,  162 => 16,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Instructor - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Instructor{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_instructor_index') }}\">Instructors</a></li>
<li class=\"breadcrumb-item active\">Create Instructor</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user-tie mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Instructor
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Instructors Button -->
                        <a href=\"{{ path('admin_instructor_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Instructors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('instructor_create') }}\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Name and Email Row -->
                            <div class=\"row\">
                                <!-- Full Name -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"name\" class=\"form-label\">
                                            <i class=\"fas fa-user text-primary mr-1\"></i>
                                            Full Name <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"name\"
                                               name=\"name\"
                                               placeholder=\"Enter instructor full name\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's full name.
                                        </div>
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"email\" class=\"form-label\">
                                            <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                            Email Address <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"email\"
                                               class=\"form-control enhanced-field\"
                                               id=\"email\"
                                               name=\"email\"
                                               placeholder=\"<EMAIL>\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid email address.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bio -->
                            <div class=\"form-group\">
                                <label for=\"bio\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Biography
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"bio\"
                                          name=\"bio\"
                                          rows=\"4\"
                                          placeholder=\"Enter instructor biography and background...\"
                                          style=\"border: 2px solid #ced4da;\"></textarea>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Provide a comprehensive biography of the instructor.
                                </small>
                            </div>

                            <!-- Specialization and LinkedIn Row -->
                            <div class=\"row\">
                                <!-- Specialization -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"specialization\" class=\"form-label\">
                                            <i class=\"fas fa-star text-primary mr-1\"></i>
                                            Specialization <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"specialization\"
                                               name=\"specialization\"
                                               placeholder=\"e.g., Technical Analysis & Risk Management\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide the instructor's specialization.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Instructor's area of expertise.
                                        </small>
                                    </div>
                                </div>

                                <!-- LinkedIn URL -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"linkedinUrl\" class=\"form-label\">
                                            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
                                            LinkedIn Profile URL
                                        </label>
                                        <input type=\"url\"
                                               class=\"form-control enhanced-field\"
                                               id=\"linkedinUrl\"
                                               name=\"linkedinUrl\"
                                               placeholder=\"https://tn.linkedin.com/in/instructor-name\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Professional LinkedIn profile URL.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Phone and Display Order Row -->
                            <div class=\"row\">
                                <!-- Phone -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"phone\" class=\"form-label\">
                                            <i class=\"fas fa-phone text-primary mr-1\"></i>
                                            Phone Number
                                        </label>
                                        <input type=\"tel\"
                                               class=\"form-control enhanced-field\"
                                               id=\"phone\"
                                               name=\"phone\"
                                               placeholder=\"+216 70 123 456\"
                                               maxlength=\"20\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Contact phone number.
                                        </small>
                                    </div>
                                </div>

                                <!-- Display Order -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"displayOrder\" class=\"form-label\">
                                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                                            Display Order
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"displayOrder\"
                                               name=\"displayOrder\"
                                               placeholder=\"e.g., 1, 2, 3...\"
                                               min=\"1\"
                                               value=\"1\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Order in which instructor appears on the website.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Qualifications -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Qualifications
                                </label>
                                <div id=\"qualifications-container\">
                                    <div class=\"input-group mb-2 qualification-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"qualifications[]\"
                                               placeholder=\"e.g., CFA Charterholder, MBA Finance...\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-qualification\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Achievements -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-trophy text-primary mr-1\"></i>
                                    Achievements
                                </label>
                                <div id=\"achievements-container\">
                                    <div class=\"input-group mb-2 achievement-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"achievements[]\"
                                               placeholder=\"e.g., 15+ years trading experience, Published author...\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-achievement\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Profile Image Upload -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-camera text-primary mr-1\"></i>
                                    Profile Image
                                </label>
                                <div class=\"mb-3\">
                                    <input type=\"file\"
                                           class=\"form-control enhanced-field\"
                                           id=\"profileImageFile\"
                                           name=\"profileImageFile\"
                                           accept=\"image/jpeg,image/png,image/jpg\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">

                                    <div class=\"image-preview mt-2\" id=\"profile-preview\" style=\"display: none;\">
                                        <div class=\"professional-image-container\" style=\"width: 150px; height: 150px; border: 2px solid #1e3c72; border-radius: 50%; background: #f8f9fa; display: flex; align-items: center; justify-content: center; overflow: hidden; margin: 0 auto;\">
                                            <img src=\"\" alt=\"Profile Preview\" style=\"width: 100%; height: 100%; object-fit: cover; border-radius: 50%;\">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Instructor
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_instructor_index') }}\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Instructor';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Instructor...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Qualifications Management
    \$(document).on('click', '.add-qualification', function() {
        var container = \$('#qualifications-container');
        var newItem = `
            <div class=\"input-group mb-2 qualification-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"qualifications[]\"
                       placeholder=\"e.g., CFA Charterholder, MBA Finance...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-qualification\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-qualification\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
        updateQualificationButtons();
    });

    \$(document).on('click', '.remove-qualification', function() {
        if (\$('.qualification-item').length > 1) {
            \$(this).closest('.qualification-item').remove();
            updateQualificationButtons();
        }
    });

    function updateQualificationButtons() {
        \$('.qualification-item').each(function(index) {
            var removeBtn = \$(this).find('.remove-qualification');
            var addBtn = \$(this).find('.add-qualification');

            if (index === 0) {
                // First item: only show + button
                removeBtn.hide();
                addBtn.show();
            } else {
                // Other items: show both buttons
                removeBtn.show();
                addBtn.show();
            }
        });
    }

    // Dynamic Achievements Management
    \$(document).on('click', '.add-achievement', function() {
        var container = \$('#achievements-container');
        var newItem = `
            <div class=\"input-group mb-2 achievement-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"achievements[]\"
                       placeholder=\"e.g., 15+ years trading experience, Published author...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-achievement\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-achievement\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
        updateAchievementButtons();
    });

    \$(document).on('click', '.remove-achievement', function() {
        if (\$('.achievement-item').length > 1) {
            \$(this).closest('.achievement-item').remove();
            updateAchievementButtons();
        }
    });

    function updateAchievementButtons() {
        \$('.achievement-item').each(function(index) {
            var removeBtn = \$(this).find('.remove-achievement');
            var addBtn = \$(this).find('.add-achievement');

            if (index === 0) {
                // First item: only show + button
                removeBtn.hide();
                addBtn.show();
            } else {
                // Other items: show both buttons
                removeBtn.show();
                addBtn.show();
            }
        });
    }

    // Profile Image Preview Functionality
    \$('#profileImageFile').on('change', function() {
        previewImage(this, '#profile-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }

    // Initialize dynamic field buttons
    updateQualificationButtons();
    updateAchievementButtons();
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #2a5298 !important;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
</style>
{% endblock %}
", "admin/instructor/new.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\new.html.twig");
    }
}
