<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* public/courses/detail.html.twig */
class __TwigTemplate_ee5e1227dc9e0db00d6072c62fc27496 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "public/courses/detail.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "public/courses/detail.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "html", null, true);
        yield " - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid px-0\">
    <!-- Course Header -->
    <section class=\"course-header py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <div class=\"d-flex align-items-center mb-3\">
                        <span class=\"badge bg-light text-dark me-3 fs-6\">";
        // line 13
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 13, $this->source); })()), "code", [], "any", false, false, false, 13), "html", null, true);
        yield "</span>
                        <span class=\"badge bg-warning text-dark\">";
        // line 14
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "level", [], "any", true, true, false, 14)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 14, $this->source); })()), "level", [], "any", false, false, false, 14), "Beginner")) : ("Beginner")), "html", null, true);
        yield "</span>
                    </div>
                    <h1 class=\"display-5 fw-bold text-white mb-3\">";
        // line 16
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 16, $this->source); })()), "title", [], "any", false, false, false, 16), "html", null, true);
        yield "</h1>
                    <p class=\"lead text-white-50 mb-4\">";
        // line 17
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 17, $this->source); })()), "description", [], "any", false, false, false, 17), "html", null, true);
        yield "</p>
                    <div class=\"d-flex align-items-center text-white-50\">
                        <i class=\"fas fa-tag me-2\"></i>
                        <span>";
        // line 20
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 20, $this->source); })()), "category", [], "any", false, false, false, 20), "html", null, true);
        yield "</span>
                    </div>
                </div>
                <div class=\"col-lg-4\">
                    ";
        // line 24
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 24, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 24)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 25
            yield "                        <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 25, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 25), "html", null, true);
            yield "\" class=\"img-fluid rounded shadow\" alt=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 25, $this->source); })()), "title", [], "any", false, false, false, 25), "html", null, true);
            yield "\">
                    ";
        }
        // line 27
        yield "                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-8\">
                    <!-- Learning Outcomes -->
                    ";
        // line 38
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 38, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 38)) > 0)) {
            // line 39
            yield "                        <div class=\"card border-0 shadow-sm mb-4\">
                            <div class=\"card-body\">
                                <h3 class=\"fw-bold text-dark mb-4\">
                                    <i class=\"fas fa-lightbulb text-warning me-2\"></i>What You'll Learn
                                </h3>
                                <div class=\"row\">
                                    ";
            // line 45
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 45, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 45));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 46
                yield "                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-check-circle text-success me-3 mt-1\"></i>
                                                <span>";
                // line 49
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "</span>
                                            </div>
                                        </div>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 53
            yield "                                </div>
                            </div>
                        </div>
                    ";
        }
        // line 57
        yield "
                    <!-- Course Features -->
                    ";
        // line 59
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 59, $this->source); })()), "features", [], "any", false, false, false, 59)) > 0)) {
            // line 60
            yield "                        <div class=\"card border-0 shadow-sm mb-4\">
                            <div class=\"card-body\">
                                <h3 class=\"fw-bold text-dark mb-4\">
                                    <i class=\"fas fa-star text-warning me-2\"></i>Course Features
                                </h3>
                                <div class=\"row\">
                                    ";
            // line 66
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 66, $this->source); })()), "features", [], "any", false, false, false, 66));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 67
                yield "                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-gem text-primary me-3 mt-1\"></i>
                                                <span>";
                // line 70
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "</span>
                                            </div>
                                        </div>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 74
            yield "                                </div>
                            </div>
                        </div>
                    ";
        }
        // line 78
        yield "
                    <!-- Course Modules -->
                    ";
        // line 80
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 80, $this->source); })()), "modules", [], "any", false, false, false, 80)) > 0)) {
            // line 81
            yield "                        <div class=\"card border-0 shadow-sm mb-4\">
                            <div class=\"card-body\">
                                <h3 class=\"fw-bold text-dark mb-4\">
                                    <i class=\"fas fa-list text-info me-2\"></i>Course Modules
                                </h3>
                                ";
            // line 86
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 86, $this->source); })()), "modules", [], "any", false, false, false, 86));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                // line 87
                yield "                                    <div class=\"module-item border-bottom pb-3 mb-3\">
                                        <div class=\"d-flex justify-content-between align-items-start\">
                                            <div class=\"flex-grow-1\">
                                                <h5 class=\"fw-bold text-dark mb-2\">
                                                    ";
                // line 91
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "code", [], "any", false, false, false, 91), "html", null, true);
                yield " - ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 91), "html", null, true);
                yield "
                                                </h5>
                                                <p class=\"text-muted mb-2\">";
                // line 93
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 93), "html", null, true);
                yield "</p>
                                                
                                                ";
                // line 95
                if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 95)) > 0)) {
                    // line 96
                    yield "                                                    <div class=\"mb-2\">
                                                        <h6 class=\"fw-bold text-dark mb-2\">Module Outcomes:</h6>
                                                        <ul class=\"list-unstyled\">
                                                            ";
                    // line 99
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 99));
                    foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                        // line 100
                        yield "                                                                <li class=\"mb-1\">
                                                                    <i class=\"fas fa-arrow-right text-primary me-2\"></i>
                                                                    <small>";
                        // line 102
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                        yield "</small>
                                                                </li>
                                                            ";
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 105
                    yield "                                                        </ul>
                                                    </div>
                                                ";
                }
                // line 108
                yield "                                            </div>
                                            <span class=\"badge bg-secondary ms-3\">Module ";
                // line 109
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 109), "html", null, true);
                yield "</span>
                                        </div>
                                    </div>
                                ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['module'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 113
            yield "                            </div>
                        </div>
                    ";
        }
        // line 116
        yield "                </div>

                <!-- Contact Form Sidebar -->
                <div class=\"col-lg-4\">
                    <div class=\"card border-0 shadow-sm sticky-top\" style=\"top: 2rem;\">
                        <div class=\"card-body\">
                            <h4 class=\"fw-bold text-dark mb-4\">
                                <i class=\"fas fa-envelope text-primary me-2\"></i>Interested in this course?
                            </h4>
                            <p class=\"text-muted mb-4\">Contact us to learn more about enrollment and get started with your trading education.</p>
                            
                            <form method=\"POST\" action=\"";
        // line 127
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("public_course_contact", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 127, $this->source); })()), "code", [], "any", false, false, false, 127)]), "html", null, true);
        yield "\">
                                <div class=\"mb-3\">
                                    <label for=\"name\" class=\"form-label\">Full Name <span class=\"text-danger\">*</span></label>
                                    <input type=\"text\" class=\"form-control\" id=\"name\" name=\"name\" required>
                                </div>
                                
                                <div class=\"mb-3\">
                                    <label for=\"email\" class=\"form-label\">Email Address <span class=\"text-danger\">*</span></label>
                                    <input type=\"email\" class=\"form-control\" id=\"email\" name=\"email\" required>
                                </div>
                                
                                <div class=\"mb-3\">
                                    <label for=\"message\" class=\"form-label\">Message</label>
                                    <textarea class=\"form-control\" id=\"message\" name=\"message\" rows=\"4\" placeholder=\"Tell us about your trading experience and goals...\"></textarea>
                                </div>
                                
                                <button type=\"submit\" class=\"btn btn-primary w-100\">
                                    <i class=\"fas fa-paper-plane me-2\"></i>Send Message
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.course-header {
    position: relative;
}

.course-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.course-header > .container {
    position: relative;
    z-index: 2;
}

.module-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "public/courses/detail.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  349 => 127,  336 => 116,  331 => 113,  313 => 109,  310 => 108,  305 => 105,  296 => 102,  292 => 100,  288 => 99,  283 => 96,  281 => 95,  276 => 93,  269 => 91,  263 => 87,  246 => 86,  239 => 81,  237 => 80,  233 => 78,  227 => 74,  217 => 70,  212 => 67,  208 => 66,  200 => 60,  198 => 59,  194 => 57,  188 => 53,  178 => 49,  173 => 46,  169 => 45,  161 => 39,  159 => 38,  146 => 27,  138 => 25,  136 => 24,  129 => 20,  123 => 17,  119 => 16,  114 => 14,  110 => 13,  101 => 6,  88 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}{{ course.title }} - Capitol Academy{% endblock %}

{% block body %}
<div class=\"container-fluid px-0\">
    <!-- Course Header -->
    <section class=\"course-header py-5\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <div class=\"d-flex align-items-center mb-3\">
                        <span class=\"badge bg-light text-dark me-3 fs-6\">{{ course.code }}</span>
                        <span class=\"badge bg-warning text-dark\">{{ course.level|default('Beginner') }}</span>
                    </div>
                    <h1 class=\"display-5 fw-bold text-white mb-3\">{{ course.title }}</h1>
                    <p class=\"lead text-white-50 mb-4\">{{ course.description }}</p>
                    <div class=\"d-flex align-items-center text-white-50\">
                        <i class=\"fas fa-tag me-2\"></i>
                        <span>{{ course.category }}</span>
                    </div>
                </div>
                <div class=\"col-lg-4\">
                    {% if course.thumbnailImage %}
                        <img src=\"{{ course.thumbnailUrl }}\" class=\"img-fluid rounded shadow\" alt=\"{{ course.title }}\">
                    {% endif %}
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-lg-8\">
                    <!-- Learning Outcomes -->
                    {% if course.learningOutcomes|length > 0 %}
                        <div class=\"card border-0 shadow-sm mb-4\">
                            <div class=\"card-body\">
                                <h3 class=\"fw-bold text-dark mb-4\">
                                    <i class=\"fas fa-lightbulb text-warning me-2\"></i>What You'll Learn
                                </h3>
                                <div class=\"row\">
                                    {% for outcome in course.learningOutcomes %}
                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-check-circle text-success me-3 mt-1\"></i>
                                                <span>{{ outcome }}</span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Course Features -->
                    {% if course.features|length > 0 %}
                        <div class=\"card border-0 shadow-sm mb-4\">
                            <div class=\"card-body\">
                                <h3 class=\"fw-bold text-dark mb-4\">
                                    <i class=\"fas fa-star text-warning me-2\"></i>Course Features
                                </h3>
                                <div class=\"row\">
                                    {% for feature in course.features %}
                                        <div class=\"col-md-6 mb-3\">
                                            <div class=\"d-flex align-items-start\">
                                                <i class=\"fas fa-gem text-primary me-3 mt-1\"></i>
                                                <span>{{ feature }}</span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Course Modules -->
                    {% if course.modules|length > 0 %}
                        <div class=\"card border-0 shadow-sm mb-4\">
                            <div class=\"card-body\">
                                <h3 class=\"fw-bold text-dark mb-4\">
                                    <i class=\"fas fa-list text-info me-2\"></i>Course Modules
                                </h3>
                                {% for module in course.modules %}
                                    <div class=\"module-item border-bottom pb-3 mb-3\">
                                        <div class=\"d-flex justify-content-between align-items-start\">
                                            <div class=\"flex-grow-1\">
                                                <h5 class=\"fw-bold text-dark mb-2\">
                                                    {{ module.code }} - {{ module.title }}
                                                </h5>
                                                <p class=\"text-muted mb-2\">{{ module.description }}</p>
                                                
                                                {% if module.learningOutcomes|length > 0 %}
                                                    <div class=\"mb-2\">
                                                        <h6 class=\"fw-bold text-dark mb-2\">Module Outcomes:</h6>
                                                        <ul class=\"list-unstyled\">
                                                            {% for outcome in module.learningOutcomes %}
                                                                <li class=\"mb-1\">
                                                                    <i class=\"fas fa-arrow-right text-primary me-2\"></i>
                                                                    <small>{{ outcome }}</small>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <span class=\"badge bg-secondary ms-3\">Module {{ loop.index }}</span>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Contact Form Sidebar -->
                <div class=\"col-lg-4\">
                    <div class=\"card border-0 shadow-sm sticky-top\" style=\"top: 2rem;\">
                        <div class=\"card-body\">
                            <h4 class=\"fw-bold text-dark mb-4\">
                                <i class=\"fas fa-envelope text-primary me-2\"></i>Interested in this course?
                            </h4>
                            <p class=\"text-muted mb-4\">Contact us to learn more about enrollment and get started with your trading education.</p>
                            
                            <form method=\"POST\" action=\"{{ path('public_course_contact', {code: course.code}) }}\">
                                <div class=\"mb-3\">
                                    <label for=\"name\" class=\"form-label\">Full Name <span class=\"text-danger\">*</span></label>
                                    <input type=\"text\" class=\"form-control\" id=\"name\" name=\"name\" required>
                                </div>
                                
                                <div class=\"mb-3\">
                                    <label for=\"email\" class=\"form-label\">Email Address <span class=\"text-danger\">*</span></label>
                                    <input type=\"email\" class=\"form-control\" id=\"email\" name=\"email\" required>
                                </div>
                                
                                <div class=\"mb-3\">
                                    <label for=\"message\" class=\"form-label\">Message</label>
                                    <textarea class=\"form-control\" id=\"message\" name=\"message\" rows=\"4\" placeholder=\"Tell us about your trading experience and goals...\"></textarea>
                                </div>
                                
                                <button type=\"submit\" class=\"btn btn-primary w-100\">
                                    <i class=\"fas fa-paper-plane me-2\"></i>Send Message
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.course-header {
    position: relative;
}

.course-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.course-header > .container {
    position: relative;
    z-index: 2;
}

.module-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
</style>
{% endblock %}
", "public/courses/detail.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\public\\courses\\detail.html.twig");
    }
}
