<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/order/index.html.twig */
class __TwigTemplate_612d81cc2a1aa00393697b90f1b4eb30 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Orders Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Orders Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Orders</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Orders Management", "page_icon" => "fas fa-shopping-cart", "search_placeholder" => "Search orders by ID, customer, or plan...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_order_export"), "text" => "Export Orders", "icon" => "fas fa-download", "class" => "btn-success"], "stats" => [["title" => "Total Orders", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 26
(isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 26, $this->source); })())), "icon" => "fas fa-shopping-cart", "color" => "#1e3c72", "gradient" => "linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)"], ["title" => "Completed Orders", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 33
(isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 33, $this->source); })()), function ($__order__) use ($context, $macros) { $context["order"] = $__order__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 33, $this->source); })()), "status", [], "any", false, false, false, 33) == "completed"); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Pending Orders", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 40
(isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 40, $this->source); })()), function ($__order__) use ($context, $macros) { $context["order"] = $__order__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 40, $this->source); })()), "status", [], "any", false, false, false, 40) == "pending"); })), "icon" => "fas fa-clock", "color" => "#ffc107", "gradient" => "linear-gradient(135deg, #ffc107 0%, #e0a800 100%)"], ["title" => "Total Revenue", "value" => ("\$" . $this->extensions['Twig\Extension\CoreExtension']->formatNumber(Twig\Extension\CoreExtension::reduce($this->env, Twig\Extension\CoreExtension::filter($this->env,         // line 47
(isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 47, $this->source); })()), function ($__order__) use ($context, $macros) { $context["order"] = $__order__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 47, $this->source); })()), "status", [], "any", false, false, false, 47) == "completed"); }), function ($__carry__, $__order__) use ($context, $macros) { $context["carry"] = $__carry__; $context["order"] = $__order__; return ((isset($context["carry"]) || array_key_exists("carry", $context) ? $context["carry"] : (function () { throw new RuntimeError('Variable "carry" does not exist.', 47, $this->source); })()) + CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 47, $this->source); })()), "amount", [], "any", false, false, false, 47)); }, 0), 2)), "icon" => "fas fa-dollar-sign", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 54
        yield "
";
        // line 55
        yield from $this->load("admin/order/index.html.twig", 55, "1302025840")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 55, $this->source); })())));
        // line 126
        yield "
<!-- Order Refund Modal -->
<div class=\"modal fade\" id=\"orderRefundModal\" tabindex=\"-1\" aria-labelledby=\"orderRefundModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"orderRefundModalLabel\">Refund Order</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to refund this order?</p>
                <p><strong>Order ID:</strong> <span id=\"refundOrderId\"></span></p>
                <div class=\"alert alert-warning\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>
                    This will process a refund through the payment gateway and revoke user access.
                </div>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmRefundAction\">Process Refund</button>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 152
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 153
        yield "<script>
let currentOrderId = null;

function refundOrder(orderId, orderNumber) {
    currentOrderId = orderId;

    document.getElementById('refundOrderId').textContent = '#' + orderNumber;

    const modal = new bootstrap.Modal(document.getElementById('orderRefundModal'));
    modal.show();
}

document.getElementById('confirmRefundAction').addEventListener('click', function() {
    if (currentOrderId) {
        // Create and submit form for refund
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/orders/\${currentOrderId}/refund`;

        document.body.appendChild(form);
        form.submit();
    }
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/order/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  213 => 153,  200 => 152,  165 => 126,  163 => 55,  160 => 54,  158 => 47,  157 => 40,  156 => 33,  155 => 26,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Orders Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Orders Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Orders</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Orders Management',
    'page_icon': 'fas fa-shopping-cart',
    'search_placeholder': 'Search orders by ID, customer, or plan...',
    'create_button': {
        'url': path('admin_order_export'),
        'text': 'Export Orders',
        'icon': 'fas fa-download',
        'class': 'btn-success'
    },
    'stats': [
        {
            'title': 'Total Orders',
            'value': orders|length,
            'icon': 'fas fa-shopping-cart',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Completed Orders',
            'value': orders|filter(order => order.status == 'completed')|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Pending Orders',
            'value': orders|filter(order => order.status == 'pending')|length,
            'icon': 'fas fa-clock',
            'color': '#ffc107',
            'gradient': 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)'
        },
        {
            'title': 'Total Revenue',
            'value': '\$' ~ (orders|filter(order => order.status == 'completed')|reduce((carry, order) => carry + order.amount, 0)|number_format(2)),
            'icon': 'fas fa-dollar-sign',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Order ID'},
            {'text': 'Customer'},
            {'text': 'Plan'},
            {'text': 'Amount'},
            {'text': 'Payment Method'},
            {'text': 'Status'},
            {'text': 'Date'},
            {'text': 'Actions', 'style': 'width: 150px;'}
        ] %}

        {% set table_rows = [] %}
        {% for order in orders %}
            {% set row_cells = [
                {
                    'content': '<span class=\"badge bg-secondary\">#' ~ order.id ~ '</span>'
                },
                {
                    'content': '<h6 class=\"order-customer mb-0 font-weight-bold text-dark\">' ~ (order.user ? order.user.fullName : 'Guest') ~ '</h6>' ~
                               (order.user and order.user.email ? '<small class=\"text-muted d-block\">' ~ order.user.email ~ '</small>' : '')
                },
                {
                    'content': order.course ?
                        '<span class=\"text-dark\">' ~ order.course.title ~ '</span>' :
                        '<span class=\"text-muted\">No course</span>'
                },
                {
                    'content': '<span class=\"text-success font-weight-bold\">\$' ~ order.amount|number_format(2) ~ '</span>'
                },
                {
                    'content': order.paymentMethod ?
                        '<span class=\"badge bg-info\">' ~ order.paymentMethod|title ~ '</span>' :
                        '<span class=\"text-muted\">Unknown</span>'
                },
                {
                    'content': '<span class=\"badge ' ~
                        (order.status == 'completed' ? 'bg-success' :
                         order.status == 'pending' ? 'bg-warning' :
                         order.status == 'failed' ? 'bg-danger' : 'bg-secondary') ~
                        '\">' ~ order.status|title ~ '</span>'
                },
                {
                    'content': '<small class=\"text-muted\">' ~ order.createdAt|date('M d, Y H:i') ~ '</small>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_order_show', {'id': order.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Order\"><i class=\"fas fa-eye\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Refund Order\" onclick=\"refundOrder(' ~ order.id ~ ', \\'' ~ order.id ~ '\\')\"><i class=\"fas fa-undo\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'order-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'order-row',
            'empty_message': 'No orders found',
            'empty_icon': 'fas fa-shopping-cart',
            'empty_description': 'Orders will appear here when customers make purchases.',
            'search_config': {
                'fields': ['.order-customer']
            }
        } %}
    {% endblock %}
{% endembed %}

<!-- Order Refund Modal -->
<div class=\"modal fade\" id=\"orderRefundModal\" tabindex=\"-1\" aria-labelledby=\"orderRefundModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"orderRefundModalLabel\">Refund Order</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to refund this order?</p>
                <p><strong>Order ID:</strong> <span id=\"refundOrderId\"></span></p>
                <div class=\"alert alert-warning\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>
                    This will process a refund through the payment gateway and revoke user access.
                </div>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmRefundAction\">Process Refund</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
let currentOrderId = null;

function refundOrder(orderId, orderNumber) {
    currentOrderId = orderId;

    document.getElementById('refundOrderId').textContent = '#' + orderNumber;

    const modal = new bootstrap.Modal(document.getElementById('orderRefundModal'));
    modal.show();
}

document.getElementById('confirmRefundAction').addEventListener('click', function() {
    if (currentOrderId) {
        // Create and submit form for refund
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/orders/\${currentOrderId}/refund`;

        document.body.appendChild(form);
        form.submit();
    }
});
</script>
{% endblock %}
", "admin/order/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\order\\index.html.twig");
    }
}


/* admin/order/index.html.twig */
class __TwigTemplate_612d81cc2a1aa00393697b90f1b4eb30___1302025840 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 55
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/order/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 55);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 56
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 57
        yield "
        <!-- Standardized Table -->
        ";
        // line 59
        $context["table_headers"] = [["text" => "Order ID"], ["text" => "Customer"], ["text" => "Plan"], ["text" => "Amount"], ["text" => "Payment Method"], ["text" => "Status"], ["text" => "Date"], ["text" => "Actions", "style" => "width: 150px;"]];
        // line 69
        yield "
        ";
        // line 70
        $context["table_rows"] = [];
        // line 71
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["orders"]) || array_key_exists("orders", $context) ? $context["orders"] : (function () { throw new RuntimeError('Variable "orders" does not exist.', 71, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["order"]) {
            // line 72
            yield "            ";
            $context["row_cells"] = [["content" => (("<span class=\"badge bg-secondary\">#" . CoreExtension::getAttribute($this->env, $this->source,             // line 74
$context["order"], "id", [], "any", false, false, false, 74)) . "</span>")], ["content" => ((("<h6 class=\"order-customer mb-0 font-weight-bold text-dark\">" . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 77
$context["order"], "user", [], "any", false, false, false, 77)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? (CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["order"], "user", [], "any", false, false, false, 77), "fullName", [], "any", false, false, false, 77)) : ("Guest"))) . "</h6>") . (((CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["order"], "user", [], "any", false, false, false, 78) && CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["order"], "user", [], "any", false, false, false, 78), "email", [], "any", false, false, false, 78))) ? ((("<small class=\"text-muted d-block\">" . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["order"], "user", [], "any", false, false, false, 78), "email", [], "any", false, false, false, 78)) . "</small>")) : ("")))], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 81
$context["order"], "course", [], "any", false, false, false, 81)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<span class=\"text-dark\">" . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 82
$context["order"], "course", [], "any", false, false, false, 82), "title", [], "any", false, false, false, 82)) . "</span>")) : ("<span class=\"text-muted\">No course</span>"))], ["content" => (("<span class=\"text-success font-weight-bold\">\$" . $this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source,             // line 86
$context["order"], "amount", [], "any", false, false, false, 86), 2)) . "</span>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 89
$context["order"], "paymentMethod", [], "any", false, false, false, 89)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<span class=\"badge bg-info\">" . Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 90
$context["order"], "paymentMethod", [], "any", false, false, false, 90))) . "</span>")) : ("<span class=\"text-muted\">Unknown</span>"))], ["content" => (((("<span class=\"badge " . (((CoreExtension::getAttribute($this->env, $this->source,             // line 95
$context["order"], "status", [], "any", false, false, false, 95) == "completed")) ? ("bg-success") : ((((CoreExtension::getAttribute($this->env, $this->source,             // line 96
$context["order"], "status", [], "any", false, false, false, 96) == "pending")) ? ("bg-warning") : ((((CoreExtension::getAttribute($this->env, $this->source,             // line 97
$context["order"], "status", [], "any", false, false, false, 97) == "failed")) ? ("bg-danger") : ("bg-secondary"))))))) . "\">") . Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 98
$context["order"], "status", [], "any", false, false, false, 98))) . "</span>")], ["content" => (("<small class=\"text-muted\">" . $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source,             // line 101
$context["order"], "createdAt", [], "any", false, false, false, 101), "M d, Y H:i")) . "</small>")], ["content" => (((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_order_show", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 105
$context["order"], "id", [], "any", false, false, false, 105)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Order\"><i class=\"fas fa-eye\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Refund Order\" onclick=\"refundOrder(") . CoreExtension::getAttribute($this->env, $this->source,             // line 106
$context["order"], "id", [], "any", false, false, false, 106)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["order"], "id", [], "any", false, false, false, 106)) . "')\"><i class=\"fas fa-undo\"></i></button>
                    </div>")]];
            // line 110
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 110, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 110, $this->source); })()), "class" => "order-row"]]);
            // line 111
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['order'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 112
        yield "
        ";
        // line 113
        yield from $this->load("components/admin_table.html.twig", 113)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 114
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 114, $this->source); })()), "rows" =>         // line 115
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 115, $this->source); })()), "row_class" => "order-row", "empty_message" => "No orders found", "empty_icon" => "fas fa-shopping-cart", "empty_description" => "Orders will appear here when customers make purchases.", "search_config" => ["fields" => [".order-customer"]]]));
        // line 124
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/order/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  567 => 124,  565 => 115,  564 => 114,  563 => 113,  560 => 112,  554 => 111,  551 => 110,  548 => 106,  546 => 105,  544 => 101,  543 => 98,  542 => 97,  541 => 96,  540 => 95,  539 => 90,  538 => 89,  537 => 86,  536 => 82,  535 => 81,  534 => 78,  533 => 77,  532 => 74,  530 => 72,  525 => 71,  523 => 70,  520 => 69,  518 => 59,  514 => 57,  501 => 56,  478 => 55,  213 => 153,  200 => 152,  165 => 126,  163 => 55,  160 => 54,  158 => 47,  157 => 40,  156 => 33,  155 => 26,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Orders Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Orders Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Orders</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Orders Management',
    'page_icon': 'fas fa-shopping-cart',
    'search_placeholder': 'Search orders by ID, customer, or plan...',
    'create_button': {
        'url': path('admin_order_export'),
        'text': 'Export Orders',
        'icon': 'fas fa-download',
        'class': 'btn-success'
    },
    'stats': [
        {
            'title': 'Total Orders',
            'value': orders|length,
            'icon': 'fas fa-shopping-cart',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Completed Orders',
            'value': orders|filter(order => order.status == 'completed')|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Pending Orders',
            'value': orders|filter(order => order.status == 'pending')|length,
            'icon': 'fas fa-clock',
            'color': '#ffc107',
            'gradient': 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)'
        },
        {
            'title': 'Total Revenue',
            'value': '\$' ~ (orders|filter(order => order.status == 'completed')|reduce((carry, order) => carry + order.amount, 0)|number_format(2)),
            'icon': 'fas fa-dollar-sign',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Order ID'},
            {'text': 'Customer'},
            {'text': 'Plan'},
            {'text': 'Amount'},
            {'text': 'Payment Method'},
            {'text': 'Status'},
            {'text': 'Date'},
            {'text': 'Actions', 'style': 'width: 150px;'}
        ] %}

        {% set table_rows = [] %}
        {% for order in orders %}
            {% set row_cells = [
                {
                    'content': '<span class=\"badge bg-secondary\">#' ~ order.id ~ '</span>'
                },
                {
                    'content': '<h6 class=\"order-customer mb-0 font-weight-bold text-dark\">' ~ (order.user ? order.user.fullName : 'Guest') ~ '</h6>' ~
                               (order.user and order.user.email ? '<small class=\"text-muted d-block\">' ~ order.user.email ~ '</small>' : '')
                },
                {
                    'content': order.course ?
                        '<span class=\"text-dark\">' ~ order.course.title ~ '</span>' :
                        '<span class=\"text-muted\">No course</span>'
                },
                {
                    'content': '<span class=\"text-success font-weight-bold\">\$' ~ order.amount|number_format(2) ~ '</span>'
                },
                {
                    'content': order.paymentMethod ?
                        '<span class=\"badge bg-info\">' ~ order.paymentMethod|title ~ '</span>' :
                        '<span class=\"text-muted\">Unknown</span>'
                },
                {
                    'content': '<span class=\"badge ' ~
                        (order.status == 'completed' ? 'bg-success' :
                         order.status == 'pending' ? 'bg-warning' :
                         order.status == 'failed' ? 'bg-danger' : 'bg-secondary') ~
                        '\">' ~ order.status|title ~ '</span>'
                },
                {
                    'content': '<small class=\"text-muted\">' ~ order.createdAt|date('M d, Y H:i') ~ '</small>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_order_show', {'id': order.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Order\"><i class=\"fas fa-eye\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Refund Order\" onclick=\"refundOrder(' ~ order.id ~ ', \\'' ~ order.id ~ '\\')\"><i class=\"fas fa-undo\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'order-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'order-row',
            'empty_message': 'No orders found',
            'empty_icon': 'fas fa-shopping-cart',
            'empty_description': 'Orders will appear here when customers make purchases.',
            'search_config': {
                'fields': ['.order-customer']
            }
        } %}
    {% endblock %}
{% endembed %}

<!-- Order Refund Modal -->
<div class=\"modal fade\" id=\"orderRefundModal\" tabindex=\"-1\" aria-labelledby=\"orderRefundModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"orderRefundModalLabel\">Refund Order</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to refund this order?</p>
                <p><strong>Order ID:</strong> <span id=\"refundOrderId\"></span></p>
                <div class=\"alert alert-warning\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>
                    This will process a refund through the payment gateway and revoke user access.
                </div>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmRefundAction\">Process Refund</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
let currentOrderId = null;

function refundOrder(orderId, orderNumber) {
    currentOrderId = orderId;

    document.getElementById('refundOrderId').textContent = '#' + orderNumber;

    const modal = new bootstrap.Modal(document.getElementById('orderRefundModal'));
    modal.show();
}

document.getElementById('confirmRefundAction').addEventListener('click', function() {
    if (currentOrderId) {
        // Create and submit form for refund
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/orders/\${currentOrderId}/refund`;

        document.body.appendChild(form);
        form.submit();
    }
});
</script>
{% endblock %}
", "admin/order/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\order\\index.html.twig");
    }
}
