<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/admins/index.html.twig */
class __TwigTemplate_fa8f86205fb6f44e2c8fc27a5ceace33 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Manage Admins - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Manage Administrators";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Dashboard</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_profile");
        yield "\">Profile</a></li>
<li class=\"breadcrumb-item active\">Manage Admins</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        $context["page_config"] = ["page_title" => "Administrator Management", "page_icon" => "fas fa-users-cog", "search_placeholder" => "Search administrators by name, username, or role...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_add_admin"), "text" => "Add New Admin", "icon" => "fas fa-user-plus"], "stats" => [["title" => "Total Admins", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 26
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 26, $this->source); })()), "total", [], "any", false, false, false, 26), "icon" => "fas fa-layer-group", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 33
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 33, $this->source); })()), "active", [], "any", false, false, false, 33), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Blocked", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 40
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 40, $this->source); })()), "blocked", [], "any", false, false, false, 40), "icon" => "fas fa-pause-circle", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"], ["title" => "Recent (30 days)", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 47
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 47, $this->source); })()), "recent", [], "any", false, false, false, 47), "icon" => "fas fa-clock", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 54
        yield "
";
        // line 55
        yield from $this->load("admin/admins/index.html.twig", 55, "908959130")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 55, $this->source); })())));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 130
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 131
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.admin-row',
        ['.admin-name', '.admin-username', '.admin-role', '.admin-date']
    );
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Admin management functions
function toggleAdminStatus(adminId, activate) {
    fetch(`/admin/admin/\${adminId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the administrator status');
    });
}

function deleteAdmin(adminId) {
    fetch(`/admin/admin/\${adminId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the administrator');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/admins/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  191 => 131,  178 => 130,  167 => 55,  164 => 54,  162 => 47,  161 => 40,  160 => 33,  159 => 26,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Manage Admins - Capitol Academy Admin{% endblock %}

{% block page_title %}Manage Administrators{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Dashboard</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_profile') }}\">Profile</a></li>
<li class=\"breadcrumb-item active\">Manage Admins</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Administrator Management',
    'page_icon': 'fas fa-users-cog',
    'search_placeholder': 'Search administrators by name, username, or role...',
    'create_button': {
        'url': path('admin_add_admin'),
        'text': 'Add New Admin',
        'icon': 'fas fa-user-plus'
    },
    'stats': [
        {
            'title': 'Total Admins',
            'value': stats.total,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': stats.active,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Blocked',
            'value': stats.blocked,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': stats.recent,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Profile'},
            {'text': 'Full Name'},
            {'text': 'Username'},
            {'text': 'Role/Permissions'},
            {'text': 'Created Date'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for adminUser in admins %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center justify-content-center\">
                        ' ~ (adminUser.profileImageName ?
                            '<img src=\"' ~ adminUser.profileImageUrl ~ '\" alt=\"' ~ adminUser.fullName ~ '\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\">' :
                            '<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">' ~ adminUser.firstName|first ~ adminUser.lastName|first ~ '</div>'
                        ) ~ '
                    </div>'
                },
                {
                    'content': '<h6 class=\"admin-name mb-0 font-weight-bold text-dark\">' ~ adminUser.fullName ~ '</h6>
                    <small class=\"text-muted\">
                        ' ~ (adminUser.isMasterAdmin ? 'Master Administrator' : 'Administrator') ~ '
                    </small>'
                },
                {
                    'content': '<code class=\"admin-username bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">' ~ adminUser.username ~ '</code>'
                },
                {
                    'content': adminUser.isMasterAdmin ?
                        '<span class=\"badge admin-role\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-crown mr-1\"></i> Master Admin</span><br><small class=\"text-muted\">Full Access</small>' :
                        '<span class=\"badge admin-role\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-user-shield mr-1\"></i> Admin</span><br><small class=\"text-muted\">' ~ adminUser.permissions|length ~ ' permissions</small>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium admin-date\">' ~ adminUser.createdAt|date('M d, Y H:i') ~ '</span>'
                },
                {
                    'content': adminUser.isActive ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Blocked</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_admin_view', {'id': adminUser.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ' ~ (not adminUser.isMasterAdmin ?
                            '<a href=\"' ~ path('admin_admin_edit', {'id': adminUser.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Admin\"><i class=\"fas fa-edit\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (adminUser.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (adminUser.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (adminUser.isActive ? 'Block' : 'Unblock') ~ ' Admin\" onclick=\"showStatusModal(\\'' ~ adminUser.fullName ~ '\\', ' ~ (adminUser.isActive ? 'true' : 'false') ~ ', function() { toggleAdminStatus(' ~ adminUser.id ~ ', ' ~ (not adminUser.isActive) ~ '); })\"><i class=\"fas fa-' ~ (adminUser.isActive ? 'lock' : 'unlock') ~ '\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Admin\" onclick=\"showDeleteModal(\\'' ~ adminUser.fullName ~ '\\', function() { deleteAdmin(' ~ adminUser.id ~ '); })\"><i class=\"fas fa-trash\"></i></button>' : ''
                        ) ~ '
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'admin-row',
            'empty_message': 'No administrators found',
            'empty_icon': 'fas fa-users-cog',
            'empty_description': 'No system administrators configured.',
            'search_config': {
                'fields': ['.admin-name', '.admin-username', '.admin-role', '.admin-date']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.admin-row',
        ['.admin-name', '.admin-username', '.admin-role', '.admin-date']
    );
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Admin management functions
function toggleAdminStatus(adminId, activate) {
    fetch(`/admin/admin/\${adminId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the administrator status');
    });
}

function deleteAdmin(adminId) {
    fetch(`/admin/admin/\${adminId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the administrator');
    });
}
</script>
{% endblock %}
", "admin/admins/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\admins\\index.html.twig");
    }
}


/* admin/admins/index.html.twig */
class __TwigTemplate_fa8f86205fb6f44e2c8fc27a5ceace33___908959130 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 55
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 55);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 56
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 57
        yield "        <!-- Standardized Table -->
        ";
        // line 58
        $context["table_headers"] = [["text" => "Profile"], ["text" => "Full Name"], ["text" => "Username"], ["text" => "Role/Permissions"], ["text" => "Created Date"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 67
        yield "
        ";
        // line 68
        $context["table_rows"] = [];
        // line 69
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["admins"]) || array_key_exists("admins", $context) ? $context["admins"] : (function () { throw new RuntimeError('Variable "admins" does not exist.', 69, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["adminUser"]) {
            // line 70
            yield "            ";
            $context["row_cells"] = [["content" => (("<div class=\"d-flex align-items-center justify-content-center\">
                        " . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["adminUser"], "profileImageName", [], "any", false, false, false, 73)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((("<img src=\"" . CoreExtension::getAttribute($this->env, $this->source,             // line 74
$context["adminUser"], "profileImageUrl", [], "any", false, false, false, 74)) . "\" alt=\"") . CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "fullName", [], "any", false, false, false, 74)) . "\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\">")) : (((("<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">" . Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 75
$context["adminUser"], "firstName", [], "any", false, false, false, 75))) . Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "lastName", [], "any", false, false, false, 75))) . "</div>")))) . "
                    </div>")], ["content" => (((("<h6 class=\"admin-name mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 80
$context["adminUser"], "fullName", [], "any", false, false, false, 80)) . "</h6>
                    <small class=\"text-muted\">
                        ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 82
$context["adminUser"], "isMasterAdmin", [], "any", false, false, false, 82)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Master Administrator") : ("Administrator"))) . "
                    </small>")], ["content" => (("<code class=\"admin-username bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 86
$context["adminUser"], "username", [], "any", false, false, false, 86)) . "</code>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 89
$context["adminUser"], "isMasterAdmin", [], "any", false, false, false, 89)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge admin-role\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-crown mr-1\"></i> Master Admin</span><br><small class=\"text-muted\">Full Access</small>") : ((("<span class=\"badge admin-role\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-user-shield mr-1\"></i> Admin</span><br><small class=\"text-muted\">" . Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 91
$context["adminUser"], "permissions", [], "any", false, false, false, 91))) . " permissions</small>")))], ["content" => (("<span class=\"text-dark font-weight-medium admin-date\">" . $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source,             // line 94
$context["adminUser"], "createdAt", [], "any", false, false, false, 94), "M d, Y H:i")) . "</span>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 97
$context["adminUser"], "isActive", [], "any", false, false, false, 97)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>") : ("<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Blocked</span>"))], ["content" => (((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admin_view", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 103
$context["adminUser"], "id", [], "any", false, false, false, 103)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ") . (((($tmp =  !CoreExtension::getAttribute($this->env, $this->source,             // line 104
$context["adminUser"], "isMasterAdmin", [], "any", false, false, false, 104)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((((((((((((((((((((("<a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admin_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 105
$context["adminUser"], "id", [], "any", false, false, false, 105)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Admin\"><i class=\"fas fa-edit\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 106
$context["adminUser"], "isActive", [], "any", false, false, false, 106)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . " 0%, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "isActive", [], "any", false, false, false, 106)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#5a6268") : ("#1e7e34"))) . " 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "isActive", [], "any", false, false, false, 106)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Block") : ("Unblock"))) . " Admin\" onclick=\"showStatusModal('") . CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "fullName", [], "any", false, false, false, 106)) . "', ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "isActive", [], "any", false, false, false, 106)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("true") : ("false"))) . ", function() { toggleAdminStatus(") . CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "id", [], "any", false, false, false, 106)) . ", ") . (!CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "isActive", [], "any", false, false, false, 106))) . "); })\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "isActive", [], "any", false, false, false, 106)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("lock") : ("unlock"))) . "\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Admin\" onclick=\"showDeleteModal('") . CoreExtension::getAttribute($this->env, $this->source,             // line 107
$context["adminUser"], "fullName", [], "any", false, false, false, 107)) . "', function() { deleteAdmin(") . CoreExtension::getAttribute($this->env, $this->source, $context["adminUser"], "id", [], "any", false, false, false, 107)) . "); })\"><i class=\"fas fa-trash\"></i></button>")) : (""))) . "
                    </div>")]];
            // line 112
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 112, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 112, $this->source); })())]]);
            // line 113
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['adminUser'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 114
        yield "
        ";
        // line 115
        yield from $this->load("components/admin_table.html.twig", 115)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 116
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 116, $this->source); })()), "rows" =>         // line 117
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 117, $this->source); })()), "row_class" => "admin-row", "empty_message" => "No administrators found", "empty_icon" => "fas fa-users-cog", "empty_description" => "No system administrators configured.", "search_config" => ["fields" => [".admin-name", ".admin-username", ".admin-role", ".admin-date"]]]));
        // line 126
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/admins/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  649 => 126,  647 => 117,  646 => 116,  645 => 115,  642 => 114,  636 => 113,  633 => 112,  630 => 107,  628 => 106,  626 => 105,  625 => 104,  623 => 103,  621 => 97,  620 => 94,  619 => 91,  618 => 89,  617 => 86,  615 => 82,  612 => 80,  610 => 75,  609 => 74,  608 => 73,  605 => 70,  600 => 69,  598 => 68,  595 => 67,  593 => 58,  590 => 57,  577 => 56,  554 => 55,  191 => 131,  178 => 130,  167 => 55,  164 => 54,  162 => 47,  161 => 40,  160 => 33,  159 => 26,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Manage Admins - Capitol Academy Admin{% endblock %}

{% block page_title %}Manage Administrators{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Dashboard</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_profile') }}\">Profile</a></li>
<li class=\"breadcrumb-item active\">Manage Admins</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Administrator Management',
    'page_icon': 'fas fa-users-cog',
    'search_placeholder': 'Search administrators by name, username, or role...',
    'create_button': {
        'url': path('admin_add_admin'),
        'text': 'Add New Admin',
        'icon': 'fas fa-user-plus'
    },
    'stats': [
        {
            'title': 'Total Admins',
            'value': stats.total,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': stats.active,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Blocked',
            'value': stats.blocked,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': stats.recent,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Profile'},
            {'text': 'Full Name'},
            {'text': 'Username'},
            {'text': 'Role/Permissions'},
            {'text': 'Created Date'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for adminUser in admins %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center justify-content-center\">
                        ' ~ (adminUser.profileImageName ?
                            '<img src=\"' ~ adminUser.profileImageUrl ~ '\" alt=\"' ~ adminUser.fullName ~ '\" class=\"rounded-circle\" style=\"width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;\">' :
                            '<div class=\"rounded-circle d-flex align-items-center justify-content-center\" style=\"width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;\">' ~ adminUser.firstName|first ~ adminUser.lastName|first ~ '</div>'
                        ) ~ '
                    </div>'
                },
                {
                    'content': '<h6 class=\"admin-name mb-0 font-weight-bold text-dark\">' ~ adminUser.fullName ~ '</h6>
                    <small class=\"text-muted\">
                        ' ~ (adminUser.isMasterAdmin ? 'Master Administrator' : 'Administrator') ~ '
                    </small>'
                },
                {
                    'content': '<code class=\"admin-username bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">' ~ adminUser.username ~ '</code>'
                },
                {
                    'content': adminUser.isMasterAdmin ?
                        '<span class=\"badge admin-role\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-crown mr-1\"></i> Master Admin</span><br><small class=\"text-muted\">Full Access</small>' :
                        '<span class=\"badge admin-role\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-user-shield mr-1\"></i> Admin</span><br><small class=\"text-muted\">' ~ adminUser.permissions|length ~ ' permissions</small>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium admin-date\">' ~ adminUser.createdAt|date('M d, Y H:i') ~ '</span>'
                },
                {
                    'content': adminUser.isActive ?
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-ban mr-1\"></i> Blocked</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_admin_view', {'id': adminUser.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ' ~ (not adminUser.isMasterAdmin ?
                            '<a href=\"' ~ path('admin_admin_edit', {'id': adminUser.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Admin\"><i class=\"fas fa-edit\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (adminUser.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (adminUser.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (adminUser.isActive ? 'Block' : 'Unblock') ~ ' Admin\" onclick=\"showStatusModal(\\'' ~ adminUser.fullName ~ '\\', ' ~ (adminUser.isActive ? 'true' : 'false') ~ ', function() { toggleAdminStatus(' ~ adminUser.id ~ ', ' ~ (not adminUser.isActive) ~ '); })\"><i class=\"fas fa-' ~ (adminUser.isActive ? 'lock' : 'unlock') ~ '\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Admin\" onclick=\"showDeleteModal(\\'' ~ adminUser.fullName ~ '\\', function() { deleteAdmin(' ~ adminUser.id ~ '); })\"><i class=\"fas fa-trash\"></i></button>' : ''
                        ) ~ '
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'admin-row',
            'empty_message': 'No administrators found',
            'empty_icon': 'fas fa-users-cog',
            'empty_description': 'No system administrators configured.',
            'search_config': {
                'fields': ['.admin-name', '.admin-username', '.admin-role', '.admin-date']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.admin-row',
        ['.admin-name', '.admin-username', '.admin-role', '.admin-date']
    );
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Admin management functions
function toggleAdminStatus(adminId, activate) {
    fetch(`/admin/admin/\${adminId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the administrator status');
    });
}

function deleteAdmin(adminId) {
    fetch(`/admin/admin/\${adminId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the administrator');
    });
}
</script>
{% endblock %}
", "admin/admins/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\admins\\index.html.twig");
    }
}
