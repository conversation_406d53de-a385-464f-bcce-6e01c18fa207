a:4:{i:0;a:6:{s:4:"type";i:16384;s:7:"message";s:201:"Since doctrine/doctrine-bundle 2.12: The default value of "doctrine.orm.controller_resolver.auto_mapping" will be changed from `true` to `false`. Explicitly configure `true` to keep existing behaviour.";s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:504;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:122;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:1;a:6:{s:4:"type";i:16384;s:7:"message";s:173:"Since doctrine/doctrine-bundle 2.13: Enabling the controller resolver automapping feature has been deprecated. Symfony Mapped Route Parameters should be used as replacement.";s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:509;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:122;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:2;a:6:{s:4:"type";i:16384;s:7:"message";s:240:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Service\AdminPermissionService" service.";s:4:"file";s:134:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:134:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:3;a:6:{s:4:"type";i:16384;s:7:"message";s:276:"Version detection logic for MySQL will change in DBAL 4. Please specify the version as the server reports it, e.g. "8.0.31" instead of "8". (AbstractMySQLDriver.php:89 called by AbstractDriverMiddleware.php:68, https://github.com/doctrine/dbal/pull/5779, package doctrine/orm)";s:4:"file";s:103:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\deprecations\src\Deprecation.php";s:4:"line";i:208;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:103:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\deprecations\src\Deprecation.php";s:4:"line";i:108;s:8:"function";s:24:"delegateTriggerToBackend";s:5:"class";s:33:"Doctrine\Deprecations\Deprecation";s:4:"type";s:2:"::";}}s:5:"count";i:1;}}