<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* market_analysis/index.html.twig */
class __TwigTemplate_75e40baf84d867da4b1f2026740d0c8c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "market_analysis/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "market_analysis/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Market Analysis - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Stay ahead of the markets with Capitol Academy's comprehensive market analysis. Get expert insights on stocks, forex, crypto, commodities, and more.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
<style>
    /* Market Analysis Page Styles */
    .main-content {
        background: #f8f9fa;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(1, 26, 45, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(169, 4, 24, 0.02) 0%, transparent 50%);
        min-height: 100vh;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        padding: 2rem 0;
        border-bottom: 1px solid #e9ecef;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .filter-buttons {
        display: flex;
        justify-content: space-between;
        gap: 0.5rem;
        flex-wrap: nowrap;
        width: 100%;
    }

    .filter-btn {
        background: white;
        border: 2px solid #e9ecef;
        color: #6c757d;
        padding: 1rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        flex: 1;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        white-space: nowrap;
    }

    .filter-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .filter-btn:hover::before {
        left: 100%;
    }

    .filter-btn:hover {
        background: #f8f9fa;
        border-color: #011a2d;
        color: #011a2d;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(1, 26, 45, 0.2);
        text-decoration: none;
    }

    .filter-btn.active {
        background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%);
        border-color: #011a2d;
        color: white;
        box-shadow: 0 6px 25px rgba(1, 26, 45, 0.4);
        transform: translateY(-1px);
    }

    .filter-btn.loading {
        pointer-events: none;
        opacity: 0.7;
    }



    .filter-btn.active .badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .filter-loading {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #011a2d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 0.5rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Enhanced Professional News Feed Styles */
    .enhanced-news-feed {
        background: #ffffff;
        border-radius: 12px;
        margin: 2rem 0;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
        border: 1px solid #e9ecef;
        overflow: hidden;
        max-width: 100%;
        width: 100%;
        margin-right: 1rem; /* Add right margin to align with sidebar */
    }

    /* Column Alignment for Professional Layout */
    .col-lg-8 {
        padding-right: 0.75rem;
    }

    .col-lg-4 {
        padding-left: 0.75rem;
    }

    .enhanced-news-header {
        background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%);
        color: white;
        padding: 2rem;
        border-bottom: 3px solid #a90418;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 2rem;
    }

    .header-left .section-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 0.75rem 0;
        color: white;
        font-family: 'Georgia', serif;
        letter-spacing: -0.02em;
    }

    .section-subtitle {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 0.95rem;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
    }

    .live-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(169, 4, 24, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        border: 1px solid rgba(169, 4, 24, 0.3);
    }

    .live-indicator i {
        color: #a90418;
        font-size: 0.7rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .separator {
        color: rgba(255, 255, 255, 0.6);
        font-weight: 300;
    }

    .results-count {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        margin: 0.5rem 0 0 0;
        font-weight: 400;
    }

    .header-right .results-info {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    /* Enhanced Articles Container */
    .enhanced-articles-container {
        position: relative;
        padding: 2rem;
        background: #ffffff;
        max-width: 100%;
        overflow: hidden;
    }

    .articles-list {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    /* Enhanced Article Item */
    .enhanced-article-item {
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 200px;
        display: flex;
        flex-direction: column;
    }

    .enhanced-article-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: #e9ecef;
        transition: all 0.3s ease;
    }

    .enhanced-article-item:hover {
        border-color: #011a2d;
        box-shadow: 0 8px 25px rgba(1, 26, 45, 0.12);
        transform: translateY(-2px);
    }

    .enhanced-article-item:hover::before {
        background: #a90418;
        width: 6px;
    }

    /* Article Layout */
    .article-layout {
        display: flex;
        gap: 1.5rem;
        align-items: flex-start;
        height: 100%;
    }

    .article-image-container {
        position: relative;
        flex-shrink: 0;
        width: 160px;
        height: 100px;
        border-radius: 6px;
        overflow: hidden;
        background: #f8f9fa;
    }

    .article-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .enhanced-article-item:hover .article-image {
        transform: scale(1.05);
    }

    .featured-badge {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: #a90418;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    /* Article Content Area */
    .article-content-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        justify-content: space-between;
    }

    .article-meta-row {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .asset-category {
        background: #011a2d;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .asset-category.stocks { background: #1f77b4; }
    .asset-category.forex { background: #ff7f0e; }
    .asset-category.crypto { background: #2ca02c; }
    .asset-category.crude_oil { background: #d62728; }
    .asset-category.gold { background: #ff9500; }
    .asset-category.commodities { background: #9467bd; }

    .meta-separator {
        color: #dee2e6;
        font-weight: 300;
    }

    .publish-time {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .view-count {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: #6c757d;
        font-size: 0.875rem;
    }

    .view-count i {
        font-size: 0.75rem;
    }

    /* Enhanced Article Title */
    .enhanced-article-title {
        margin: 0.25rem 0;
        font-size: 1.2rem;
        font-weight: 700;
        line-height: 1.3;
        font-family: 'Georgia', serif;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 3.2rem;
    }

    .title-link {
        color: #011a2d;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .title-link:hover {
        color: #a90418;
        text-decoration: none;
    }

    /* Enhanced Article Excerpt */
    .enhanced-article-excerpt {
        color: #495057;
        line-height: 1.6;
        font-size: 1.05rem;
        margin: 0;
        flex: 1;
        margin-right: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 3.4rem;
    }

    .article-excerpt-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        gap: 1rem;
    }

    /* Enhanced Article Footer */
    .enhanced-article-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f8f9fa;
    }

    .author-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .author-avatar-small {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #011a2d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .author-name {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .article-actions {
        display: flex;
        align-items: center;
    }

    .read-more-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: #a90418;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border: 1px solid #a90418;
        border-radius: 6px;
        background: transparent;
        transition: all 0.3s ease;
    }

    .read-more-btn:hover {
        background: #a90418;
        color: white;
        text-decoration: none;
        transform: translateX(3px);
    }

    .read-more-btn.external {
        border-color: #6c757d;
        color: #6c757d;
    }

    .read-more-btn.external:hover {
        background: #6c757d;
        color: white;
    }

    /* Enhanced Loading Spinner */
    .enhanced-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        padding: 2rem;
    }

    .spinner-ring {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #011a2d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .loading-text {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Enhanced Responsive Design */
    @media (max-width: 992px) {
        .header-content {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .enhanced-news-header {
            padding: 1.5rem;
        }

        .header-left .section-title {
            font-size: 1.75rem;
        }

        .article-layout {
            gap: 1rem;
        }

        .article-image-container {
            width: 160px;
            height: 110px;
        }
    }

    @media (max-width: 768px) {
        .enhanced-articles-container {
            padding: 1rem;
        }

        .enhanced-article-item {
            padding: 1rem;
        }

        .article-layout {
            flex-direction: column;
            gap: 1rem;
        }

        .article-image-container {
            width: 100%;
            height: 200px;
        }

        .enhanced-article-title {
            font-size: 1rem;
        }

        .enhanced-article-footer {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .article-actions {
            width: 100%;
        }

        .read-more-btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .enhanced-news-header {
            padding: 1rem;
        }

        .header-left .section-title {
            font-size: 1.5rem;
        }

        .section-subtitle {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .enhanced-article-title {
            font-size: 0.95rem;
        }

        .article-meta-row {
            flex-wrap: wrap;
            gap: 0.25rem;
        }
    }

    .news-header {
        border-bottom: 3px solid #011a2d;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }

    .news-header h2 {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.75rem;
        margin: 0;
    }

    .news-header .subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin-top: 0.5rem;
    }

    .article-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        position: relative;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .article-item:last-child {
        margin-bottom: 0;
    }

    .article-item:hover {
        background: white;
        border-color: #011a2d;
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(1, 26, 45, 0.15);
    }

    .article-content {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }

    .article-thumbnail {
        width: 180px;
        height: 120px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
        position: relative;
        background: #f8f9fa;
    }

    .article-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .article-item:hover .article-thumbnail img {
        transform: scale(1.05);
    }

    .article-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .article-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .asset-type-badge {
        background: #011a2d;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .article-time {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .featured-indicator {
        background: #a90418;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .article-title {
        font-size: 1.375rem;
        font-weight: 700;
        color: #011a2d;
        line-height: 1.3;
        margin: 0;
        font-family: 'Georgia', serif;
        transition: color 0.3s ease;
    }

    .article-item:hover .article-title {
        color: #a90418;
    }

    .article-excerpt {
        color: #495057;
        line-height: 1.6;
        font-size: 1rem;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .article-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f8f9fa;
    }

    .article-author {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
        font-size: 0.875rem;
    }

    .author-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #011a2d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .learn-more-link {
        color: #a90418;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border: 1px solid #a90418;
        border-radius: 6px;
        background: transparent;
    }

    .learn-more-link:hover {
        background: #a90418;
        color: white;
        text-decoration: none;
        transform: translateX(3px);
    }

    /* Sidebar Styles */
    .sidebar {
        padding: 2rem 0;
    }

    .sidebar-widget {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .widget-header {
        background: #011a2d;
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 700;
        font-size: 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .widget-content {
        padding: 1.5rem;
    }

    .widget-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: #a90418;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        margin-top: 1rem;
        transition: all 0.3s ease;
    }

    .widget-link:hover {
        color: #8b0314;
        text-decoration: none;
        transform: translateX(3px);
    }

    .chart-container {
        min-height: 400px;
        height: 400px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        background: #f8f9fa;
    }

    .chart-container .tradingview-widget-container {
        height: 100% !important;
        width: 100% !important;
    }

    .chart-container .tradingview-widget-container__widget {
        height: 100% !important;
        width: 100% !important;
    }

    .calendar-container {
        min-height: 500px;
        height: 500px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        background: #f8f9fa;
    }

    .calendar-container .tradingview-widget-container {
        height: 100% !important;
        width: 100% !important;
    }

    /* Pagination */
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        padding: 1rem 0 0 0;
    }

    .pagination-btn {
        background: white;
        border: 2px solid #e9ecef;
        color: #6c757d;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .pagination-btn:hover {
        background: #011a2d;
        border-color: #011a2d;
        color: white;
        text-decoration: none;
    }

    .pagination-btn.active {
        background: #011a2d;
        border-color: #011a2d;
        color: white;
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Loading */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        border-radius: 12px;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #011a2d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 992px) {
        .filter-buttons {
            justify-content: flex-start;
            gap: 0.75rem;
        }

        .filter-btn {
            min-width: 120px;
            padding: 0.875rem 1.5rem;
        }

        .news-feed {
            padding: 2rem 0;
        }

        .article-content {
            gap: 1.5rem;
        }

        .article-thumbnail {
            width: 160px;
            height: 100px;
        }
    }

    @media (max-width: 768px) {
        .filter-buttons {
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .filter-btn {
            flex: 1 1 calc(50% - 0.25rem);
            min-width: auto;
            padding: 0.75rem 1rem;
            justify-content: center;
        }

        .enhanced-article-item {
            height: auto;
            min-height: 180px;
        }

        .article-excerpt-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .enhanced-article-excerpt {
            margin-right: 0;
        }

        .news-header h2 {
            font-size: 1.5rem;
        }

        .article-content {
            flex-direction: column;
            gap: 1rem;
        }

        .article-thumbnail {
            width: 100%;
            height: 200px;
        }

        .article-title {
            font-size: 1.25rem;
        }

        .article-excerpt {
            -webkit-line-clamp: 2;
        }

        .article-footer {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .learn-more-link {
            align-self: flex-end;
        }

        .sidebar {
            padding: 1rem 0;
        }

        .widget-content {
            padding: 1rem;
        }

        .chart-container,
        .calendar-container {
            min-height: 300px;
            height: 300px;
        }
    }

    @media (max-width: 576px) {
        .filter-section {
            padding: 1rem 0;
        }

        .news-feed {
            padding: 1.5rem 0;
        }

        .news-header {
            margin-bottom: 1.5rem;
        }

        .news-header h2 {
            font-size: 1.375rem;
        }

        .article-item {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .article-item:hover {
            transform: translateY(-2px);
        }

        .article-thumbnail {
            height: 180px;
        }

        .article-title {
            font-size: 1.125rem;
            line-height: 1.4;
        }

        .article-meta {
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .asset-type-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.6rem;
        }

        .learn-more-link {
            padding: 0.5rem 0.875rem;
            font-size: 0.875rem;
        }
    }
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 1088
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 1089
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
<script>
\$(document).ready(function() {
    let currentFilter = '';
    let currentPage = ";
        // line 1093
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1093, $this->source); })()), "currentPage", [], "any", false, false, false, 1093), "html", null, true);
        yield ";
    let isLoading = false;

    // Initialize current filter from URL
    const urlParams = new URLSearchParams(window.location.search);
    currentFilter = urlParams.get('asset_type') || '';

    // Filter functionality with URL-based navigation
    \$('.filter-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const \$clickedBtn = \$(this);
        const filter = \$clickedBtn.data('filter');

        // Handle filter selection with toggle behavior
        if (currentFilter === filter) {
            // Toggle off - redirect to base URL without parameters
            window.location.href = '";
        // line 1111
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "';
        } else {
            // Select new filter - redirect with asset_type parameter
            window.location.href = '";
        // line 1114
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis");
        yield "?asset_type=' + filter;
        }

        return false;
    });

    // Show filter feedback
    function showFilterFeedback(message) {
        // Remove existing feedback
        \$('.filter-feedback').remove();

        // Create feedback element
        const feedback = \$(`
            <div class=\"filter-feedback alert alert-info d-flex align-items-center\" style=\"
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 250px;
                background: #011a2d;
                color: white;
                border: none;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(1, 26, 45, 0.3);
            \">
                <i class=\"fas fa-filter me-2\"></i>
                \${message}
            </div>
        `);

        \$('body').append(feedback);

        // Auto remove after 3 seconds
        setTimeout(() => {
            feedback.fadeOut(() => feedback.remove());
        }, 3000);
    }

    // Pagination functionality
    \$(document).on('click', '.pagination-btn[data-page]', function(e) {
        e.preventDefault();
        e.stopPropagation();

        if (isLoading) return false;

        const page = parseInt(\$(this).data('page'));
        if (page === currentPage) return false;

        currentPage = page;
        loadArticles();

        // Scroll to top of articles
        \$('html, body').animate({
            scrollTop: \$('#articlesContainer').offset().top - 100
        }, 500);

        return false;
    });

    // Error message function
    function showErrorMessage(message) {
        const alertDiv = \$('<div class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">')
            .html(`<i class=\"fas fa-exclamation-triangle me-2\"></i>\${message}<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>`)
            .css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: 9999,
                minWidth: '300px'
            });

        \$('body').append(alertDiv);

        setTimeout(() => {
            alertDiv.fadeOut(() => alertDiv.remove());
        }, 5000);
    }

    function loadArticles() {
        if (isLoading) return Promise.resolve();

        isLoading = true;
        \$('#loadingOverlay').removeClass('d-none');

        const params = new URLSearchParams({
            page: currentPage
        });

        if (currentFilter) {
            params.append('asset_type', currentFilter);
        }

        const url = '";
        // line 1206
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("api_market_analysis_filter");
        yield "?' + params.toString();

        return fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: \${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateArticlesGrid(data.articles);
                updatePagination(data.pagination);
                updateResultsCount(data.pagination);

                // Update URL without page reload (remove hash)
                const newUrl = new URL(window.location.origin + window.location.pathname);
                if (currentFilter) {
                    newUrl.searchParams.set('asset_type', currentFilter);
                }
                if (currentPage > 1) {
                    newUrl.searchParams.set('page', currentPage);
                }
                window.history.replaceState({}, '', newUrl.toString());
            } else {
                console.error('Error loading articles:', data.message);
                showErrorMessage('Failed to load articles. Please try again.');
            }
        })
        .catch(error => {
            console.error('AJAX Error:', error);
            showErrorMessage('Network error. Please check your connection and try again.');
        })
        .finally(() => {
            isLoading = false;
            \$('#loadingOverlay').addClass('d-none');
        });
    }

    function updateArticlesGrid(articles) {
        const grid = \$('#articlesGrid');
        grid.empty();

        articles.forEach(article => {
            const articleHtml = createArticleItem(article);
            grid.append(articleHtml);
        });

        // Animate in new articles
        grid.find('.enhanced-article-item').each(function(index) {
            \$(this).css('opacity', '0').delay(index * 100).animate({opacity: 1}, 300);
        });
    }

    function createArticleItem(article) {
        const featuredBadge = '';

        // Remove author section - no longer needed

        const linkUrl = article.detailUrl || '#';
        const linkTarget = '';
        const linkIcon = 'fas fa-arrow-right';
        const linkClass = 'read-more-btn';

        const viewCount = article.views > 0 ?
            `<span class=\"meta-separator\">•</span>
             <span class=\"view-count\">
                <i class=\"fas fa-eye\"></i>
                \${article.views}
             </span>` : '';

        return `
            <article class=\"enhanced-article-item\" data-article-id=\"\${article.id}\">
                <div class=\"article-layout\">
                    <div class=\"article-image-container\">
                        <img src=\"\${article.thumbnailUrl}\" alt=\"\${article.title}\" class=\"article-image\" loading=\"lazy\">
                        \${featuredBadge}
                    </div>
                    <div class=\"article-content-area\">
                        <div class=\"article-meta-row\">
                            <span class=\"asset-category \${article.assetType}\">\${article.assetTypeLabel}</span>
                            <span class=\"meta-separator\">•</span>
                            <span class=\"publish-time\">\${article.publishDate}</span>
                            \${viewCount}
                        </div>
                        <h3 class=\"enhanced-article-title\">
                            <a href=\"\${linkUrl}\" class=\"title-link\">\${article.title}</a>
                        </h3>
                        <div class=\"article-excerpt-row\">
                            <p class=\"enhanced-article-excerpt\">\${article.excerpt.split('.')[0]}.</p>
                            <div class=\"article-actions\">
                                <a href=\"\${linkUrl}\" \${linkTarget} class=\"\${linkClass}\">
                                    <span>Read Analysis</span>
                                    <i class=\"\${linkIcon}\"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        `;
    }

    function updatePagination(pagination) {
        const wrapper = \$('#paginationWrapper');
        if (pagination.totalPages <= 1) {
            wrapper.hide();
            return;
        }

        wrapper.show();
        wrapper.empty();

        // Previous button
        if (pagination.hasPrev) {
            wrapper.append(`<a href=\"#\" class=\"pagination-btn\" data-page=\"\${pagination.currentPage - 1}\"><i class=\"fas fa-chevron-left me-2\"></i>Previous</a>`);
        }

        // Page numbers
        for (let page = 1; page <= pagination.totalPages; page++) {
            if (page === pagination.currentPage) {
                wrapper.append(`<span class=\"pagination-btn active\">\${page}</span>`);
            } else if (page <= 3 || page > pagination.totalPages - 3 || (page >= pagination.currentPage - 1 && page <= pagination.currentPage + 1)) {
                wrapper.append(`<a href=\"#\" class=\"pagination-btn\" data-page=\"\${page}\">\${page}</a>`);
            } else if (page === 4 && pagination.currentPage > 5) {
                wrapper.append(`<span class=\"pagination-btn\">...</span>`);
            } else if (page === pagination.totalPages - 3 && pagination.currentPage < pagination.totalPages - 4) {
                wrapper.append(`<span class=\"pagination-btn\">...</span>`);
            }
        }

        // Next button
        if (pagination.hasNext) {
            wrapper.append(`<a href=\"#\" class=\"pagination-btn\" data-page=\"\${pagination.currentPage + 1}\">Next<i class=\"fas fa-chevron-right ms-2\"></i></a>`);
        }
    }

    function updateResultsCount(pagination) {
        const start = (pagination.currentPage - 1) * 4 + 1;
        const end = Math.min(pagination.currentPage * 4, pagination.totalArticles);
        \$('#resultsCount').text(`\${start}-\${end} of \${pagination.totalArticles} articles`);
    }


});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 1361
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 1362
        yield "<div class=\"main-content\">
    <!-- Filter Section -->
    <section class=\"filter-section\">
        <div class=\"container\">
            <div class=\"filter-buttons\" id=\"filterButtons\">
                ";
        // line 1367
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["assetTypesWithCounts"]) || array_key_exists("assetTypesWithCounts", $context) ? $context["assetTypesWithCounts"] : (function () { throw new RuntimeError('Variable "assetTypesWithCounts" does not exist.', 1367, $this->source); })()));
        foreach ($context['_seq'] as $context["assetType"] => $context["data"]) {
            // line 1368
            yield "                    <a href=\"#\" class=\"filter-btn";
            if (($context["assetType"] == (isset($context["currentAssetType"]) || array_key_exists("currentAssetType", $context) ? $context["currentAssetType"] : (function () { throw new RuntimeError('Variable "currentAssetType" does not exist.', 1368, $this->source); })()))) {
                yield " active";
            }
            yield "\" data-filter=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["assetType"], "html", null, true);
            yield "\">
                        ";
            // line 1369
            if (($context["assetType"] == "stocks")) {
                // line 1370
                yield "                            <i class=\"fas fa-chart-bar me-2\"></i>
                        ";
            } elseif ((            // line 1371
$context["assetType"] == "forex")) {
                // line 1372
                yield "                            <i class=\"fas fa-exchange-alt me-2\"></i>
                        ";
            } elseif ((            // line 1373
$context["assetType"] == "crypto")) {
                // line 1374
                yield "                            <i class=\"fab fa-bitcoin me-2\"></i>
                        ";
            } elseif ((            // line 1375
$context["assetType"] == "crude_oil")) {
                // line 1376
                yield "                            <i class=\"fas fa-oil-can me-2\"></i>
                        ";
            } elseif ((            // line 1377
$context["assetType"] == "gold")) {
                // line 1378
                yield "                            <i class=\"fas fa-coins me-2\"></i>
                        ";
            } elseif ((            // line 1379
$context["assetType"] == "commodities")) {
                // line 1380
                yield "                            <i class=\"fas fa-seedling me-2\"></i>
                        ";
            }
            // line 1382
            yield "                        ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["data"], "label", [], "any", false, false, false, 1382), "html", null, true);
            yield "
                    </a>
                ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['assetType'], $context['data'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 1385
        yield "            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class=\"container\">
        <div class=\"row\">
            <!-- News Feed Section (Left Column) -->
            <div class=\"col-lg-8\">
                <section class=\"enhanced-news-feed\">
                    <!-- Section Header -->
                    <div class=\"widget-header\">
                        <i class=\"fas fa-chart-line me-2\"></i>Latest Market Analysis
                    </div>

                    <!-- Articles Container -->
                    <div class=\"enhanced-articles-container\" id=\"articlesContainer\">
                        <div class=\"articles-list\" id=\"articlesGrid\">
                            ";
        // line 1403
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["articles"]) || array_key_exists("articles", $context) ? $context["articles"] : (function () { throw new RuntimeError('Variable "articles" does not exist.', 1403, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["article"]) {
            // line 1404
            yield "                                <article class=\"enhanced-article-item\" data-article-id=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "id", [], "any", false, false, false, 1404), "html", null, true);
            yield "\">
                                    <div class=\"article-layout\">
                                        <!-- Article Thumbnail -->
                                        <div class=\"article-image-container\">
                                            <img src=\"";
            // line 1408
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "thumbnailUrl", [], "any", false, false, false, 1408), "html", null, true);
            yield "\"
                                                 alt=\"";
            // line 1409
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "title", [], "any", false, false, false, 1409), "html", null, true);
            yield "\"
                                                 class=\"article-image\"
                                                 loading=\"lazy\">

                                        </div>

                                        <!-- Article Content -->
                                        <div class=\"article-content-area\">
                                            <!-- Meta Information -->
                                            <div class=\"article-meta-row\">
                                                <span class=\"asset-category ";
            // line 1419
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "assetType", [], "any", false, false, false, 1419), "html", null, true);
            yield "\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "assetTypeLabel", [], "any", false, false, false, 1419), "html", null, true);
            yield "</span>
                                                <span class=\"meta-separator\">•</span>
                                                <span class=\"publish-time\">";
            // line 1421
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "relativeTime", [], "any", false, false, false, 1421), "html", null, true);
            yield "</span>
                                            </div>

                                            <!-- Article Title -->
                                            <h3 class=\"enhanced-article-title\">
                                                <a href=\"";
            // line 1426
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, $context["article"], "slug", [], "any", false, false, false, 1426)]), "html", null, true);
            yield "\" class=\"title-link\">
                                                    ";
            // line 1427
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "title", [], "any", false, false, false, 1427), "html", null, true);
            yield "
                                                </a>
                                            </h3>

                                            <!-- Article Excerpt and Action -->
                                            <div class=\"article-excerpt-row\">
                                                <p class=\"enhanced-article-excerpt\">";
            // line 1433
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::first($this->env->getCharset(), Twig\Extension\CoreExtension::split($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["article"], "shortExcerpt", [80], "method", false, false, false, 1433), ".")), "html", null, true);
            yield ".</p>
                                                <div class=\"article-actions\">
                                                    <a href=\"";
            // line 1435
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_market_analysis_show_seo", ["slug" => CoreExtension::getAttribute($this->env, $this->source, $context["article"], "slug", [], "any", false, false, false, 1435)]), "html", null, true);
            yield "\" class=\"read-more-btn\">
                                                        <span>Read Analysis</span>
                                                        <i class=\"fas fa-arrow-right\"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['article'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 1445
        yield "                        </div>

                        <!-- Loading Overlay -->
                        <div class=\"loading-overlay d-none\" id=\"loadingOverlay\">
                            <div class=\"enhanced-spinner\">
                                <div class=\"spinner-ring\"></div>
                                <span class=\"loading-text\">Loading articles...</span>
                            </div>
                        </div>
                    </div>
            
            <!-- Pagination -->
            ";
        // line 1457
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1457, $this->source); })()), "totalPages", [], "any", false, false, false, 1457) > 1)) {
            // line 1458
            yield "                <div class=\"pagination-wrapper\" id=\"paginationWrapper\">
                    ";
            // line 1459
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1459, $this->source); })()), "hasPrev", [], "any", false, false, false, 1459)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 1460
                yield "                        <a href=\"#\" class=\"pagination-btn\" data-page=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1460, $this->source); })()), "currentPage", [], "any", false, false, false, 1460) - 1), "html", null, true);
                yield "\">
                            <i class=\"fas fa-chevron-left me-2\"></i>Previous
                        </a>
                    ";
            }
            // line 1464
            yield "                    
                    ";
            // line 1465
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1465, $this->source); })()), "totalPages", [], "any", false, false, false, 1465)));
            foreach ($context['_seq'] as $context["_key"] => $context["page"]) {
                // line 1466
                yield "                        ";
                if (($context["page"] == CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1466, $this->source); })()), "currentPage", [], "any", false, false, false, 1466))) {
                    // line 1467
                    yield "                            <span class=\"pagination-btn active\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                    yield "</span>
                        ";
                } elseif ((((                // line 1468
$context["page"] <= 3) || ($context["page"] > (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1468, $this->source); })()), "totalPages", [], "any", false, false, false, 1468) - 3))) || (($context["page"] >= (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1468, $this->source); })()), "currentPage", [], "any", false, false, false, 1468) - 1)) && ($context["page"] <= (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1468, $this->source); })()), "currentPage", [], "any", false, false, false, 1468) + 1))))) {
                    // line 1469
                    yield "                            <a href=\"#\" class=\"pagination-btn\" data-page=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                    yield "\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["page"], "html", null, true);
                    yield "</a>
                        ";
                } elseif (((                // line 1470
$context["page"] == 4) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1470, $this->source); })()), "currentPage", [], "any", false, false, false, 1470) > 5))) {
                    // line 1471
                    yield "                            <span class=\"pagination-btn\">...</span>
                        ";
                } elseif (((                // line 1472
$context["page"] == (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1472, $this->source); })()), "totalPages", [], "any", false, false, false, 1472) - 3)) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1472, $this->source); })()), "currentPage", [], "any", false, false, false, 1472) < (CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1472, $this->source); })()), "totalPages", [], "any", false, false, false, 1472) - 4)))) {
                    // line 1473
                    yield "                            <span class=\"pagination-btn\">...</span>
                        ";
                }
                // line 1475
                yield "                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['page'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 1476
            yield "                    
                    ";
            // line 1477
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1477, $this->source); })()), "hasNext", [], "any", false, false, false, 1477)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 1478
                yield "                        <a href=\"#\" class=\"pagination-btn\" data-page=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, (isset($context["pagination"]) || array_key_exists("pagination", $context) ? $context["pagination"] : (function () { throw new RuntimeError('Variable "pagination" does not exist.', 1478, $this->source); })()), "currentPage", [], "any", false, false, false, 1478) + 1), "html", null, true);
                yield "\">
                            Next<i class=\"fas fa-chevron-right ms-2\"></i>
                        </a>
                    ";
            }
            // line 1482
            yield "                </div>
            ";
        }
        // line 1484
        yield "                </section>
            </div>

            <!-- Sidebar (Right Column) -->
            <div class=\"col-lg-4\">
                <aside class=\"sidebar\">
                    <!-- Live Charts Widget -->
                    <div class=\"sidebar-widget\">
                        <div class=\"widget-header\">
                            <i class=\"fas fa-chart-line me-2\"></i>Live Charts
                        </div>
                        <div class=\"widget-content\">
                            <div class=\"chart-container\">
                                <!-- TradingView Widget BEGIN -->
                                <div class=\"tradingview-widget-container\">
                                  <div class=\"tradingview-widget-container__widget\"></div>
                                  <div class=\"tradingview-widget-copyright\"><a href=\"https://www.tradingview.com/\" rel=\"noopener nofollow\" target=\"_blank\"><span class=\"blue-text\">Track all markets on TradingView</span></a></div>
                                  <script type=\"text/javascript\" src=\"https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js\" async>
                                  {
                                  \"width\": \"100%\",
                                  \"height\": \"370\",
                                  \"symbol\": \"MARKETSCOM:OIL\",
                                  \"interval\": \"D\",
                                  \"timezone\": \"Etc/UTC\",
                                  \"theme\": \"light\",
                                  \"style\": \"1\",
                                  \"locale\": \"en\",
                                  \"toolbar_bg\": \"#f1f3f6\",
                                  \"enable_publishing\": false,
                                  \"allow_symbol_change\": true,
                                  \"container_id\": \"tradingview_chart\",
                                  \"hide_side_toolbar\": false,
                                  \"hide_top_toolbar\": false,
                                  \"hide_legend\": false,
                                  \"save_image\": false,
                                  \"calendar\": false,
                                  \"hide_volume\": false,
                                  \"support_host\": \"https://www.tradingview.com\"
                                }
                                  </script>
                                </div>
                                <!-- TradingView Widget END -->
                            </div>
                            <a href=\"#\" class=\"widget-link\">
                                <i class=\"fas fa-external-link-alt\"></i>View Full Screen Chart
                            </a>
                        </div>
                    </div>

                    <!-- Economic Calendar Widget -->
                    <div class=\"sidebar-widget\">
                        <div class=\"widget-header\">
                            <i class=\"fas fa-calendar-alt me-2\"></i>Economic Calendar
                        </div>
                        <div class=\"widget-content\">
                            <div class=\"calendar-container\">
                                <!-- TradingView Widget BEGIN -->
                                <div class=\"tradingview-widget-container\">
                                  <div class=\"tradingview-widget-container__widget\"></div>
                                  <div class=\"tradingview-widget-copyright\"><a href=\"https://www.tradingview.com/\" rel=\"noopener nofollow\" target=\"_blank\"><span class=\"blue-text\">Track all markets on TradingView</span></a></div>
                                  <script type=\"text/javascript\" src=\"https://s3.tradingview.com/external-embedding/embed-widget-events.js\" async>
                                  {
                                  \"colorTheme\": \"light\",
                                  \"isTransparent\": false,
                                  \"width\": \"100%\",
                                  \"height\": \"470\",
                                  \"locale\": \"en\",
                                  \"countryFilter\": \"ar,au,br,ca,cn,fr,de,in,id,it,jp,kr,mx,ru,sa,za,tr,gb,us,eu\",
                                  \"importanceFilter\": \"-1,0,1\"
                                }
                                  </script>
                                </div>
                                <!-- TradingView Widget END -->
                            </div>
                            <a href=\"#\" class=\"widget-link\">
                                <i class=\"fas fa-external-link-alt\"></i>View Full Calendar
                            </a>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </div>
</section>
</div>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "market_analysis/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1782 => 1484,  1778 => 1482,  1770 => 1478,  1768 => 1477,  1765 => 1476,  1759 => 1475,  1755 => 1473,  1753 => 1472,  1750 => 1471,  1748 => 1470,  1741 => 1469,  1739 => 1468,  1734 => 1467,  1731 => 1466,  1727 => 1465,  1724 => 1464,  1716 => 1460,  1714 => 1459,  1711 => 1458,  1709 => 1457,  1695 => 1445,  1679 => 1435,  1674 => 1433,  1665 => 1427,  1661 => 1426,  1653 => 1421,  1646 => 1419,  1633 => 1409,  1629 => 1408,  1621 => 1404,  1617 => 1403,  1597 => 1385,  1587 => 1382,  1583 => 1380,  1581 => 1379,  1578 => 1378,  1576 => 1377,  1573 => 1376,  1571 => 1375,  1568 => 1374,  1566 => 1373,  1563 => 1372,  1561 => 1371,  1558 => 1370,  1556 => 1369,  1547 => 1368,  1543 => 1367,  1536 => 1362,  1523 => 1361,  1358 => 1206,  1263 => 1114,  1257 => 1111,  1236 => 1093,  1229 => 1089,  1216 => 1088,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Market Analysis - Capitol Academy{% endblock %}

{% block meta_description %}Stay ahead of the markets with Capitol Academy's comprehensive market analysis. Get expert insights on stocks, forex, crypto, commodities, and more.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    /* Market Analysis Page Styles */
    .main-content {
        background: #f8f9fa;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(1, 26, 45, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(169, 4, 24, 0.02) 0%, transparent 50%);
        min-height: 100vh;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        padding: 2rem 0;
        border-bottom: 1px solid #e9ecef;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .filter-buttons {
        display: flex;
        justify-content: space-between;
        gap: 0.5rem;
        flex-wrap: nowrap;
        width: 100%;
    }

    .filter-btn {
        background: white;
        border: 2px solid #e9ecef;
        color: #6c757d;
        padding: 1rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        flex: 1;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        white-space: nowrap;
    }

    .filter-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .filter-btn:hover::before {
        left: 100%;
    }

    .filter-btn:hover {
        background: #f8f9fa;
        border-color: #011a2d;
        color: #011a2d;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(1, 26, 45, 0.2);
        text-decoration: none;
    }

    .filter-btn.active {
        background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%);
        border-color: #011a2d;
        color: white;
        box-shadow: 0 6px 25px rgba(1, 26, 45, 0.4);
        transform: translateY(-1px);
    }

    .filter-btn.loading {
        pointer-events: none;
        opacity: 0.7;
    }



    .filter-btn.active .badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .filter-loading {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #011a2d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 0.5rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Enhanced Professional News Feed Styles */
    .enhanced-news-feed {
        background: #ffffff;
        border-radius: 12px;
        margin: 2rem 0;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
        border: 1px solid #e9ecef;
        overflow: hidden;
        max-width: 100%;
        width: 100%;
        margin-right: 1rem; /* Add right margin to align with sidebar */
    }

    /* Column Alignment for Professional Layout */
    .col-lg-8 {
        padding-right: 0.75rem;
    }

    .col-lg-4 {
        padding-left: 0.75rem;
    }

    .enhanced-news-header {
        background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%);
        color: white;
        padding: 2rem;
        border-bottom: 3px solid #a90418;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 2rem;
    }

    .header-left .section-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 0.75rem 0;
        color: white;
        font-family: 'Georgia', serif;
        letter-spacing: -0.02em;
    }

    .section-subtitle {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 0.95rem;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
    }

    .live-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(169, 4, 24, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        border: 1px solid rgba(169, 4, 24, 0.3);
    }

    .live-indicator i {
        color: #a90418;
        font-size: 0.7rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .separator {
        color: rgba(255, 255, 255, 0.6);
        font-weight: 300;
    }

    .results-count {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        margin: 0.5rem 0 0 0;
        font-weight: 400;
    }

    .header-right .results-info {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    /* Enhanced Articles Container */
    .enhanced-articles-container {
        position: relative;
        padding: 2rem;
        background: #ffffff;
        max-width: 100%;
        overflow: hidden;
    }

    .articles-list {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    /* Enhanced Article Item */
    .enhanced-article-item {
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 200px;
        display: flex;
        flex-direction: column;
    }

    .enhanced-article-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: #e9ecef;
        transition: all 0.3s ease;
    }

    .enhanced-article-item:hover {
        border-color: #011a2d;
        box-shadow: 0 8px 25px rgba(1, 26, 45, 0.12);
        transform: translateY(-2px);
    }

    .enhanced-article-item:hover::before {
        background: #a90418;
        width: 6px;
    }

    /* Article Layout */
    .article-layout {
        display: flex;
        gap: 1.5rem;
        align-items: flex-start;
        height: 100%;
    }

    .article-image-container {
        position: relative;
        flex-shrink: 0;
        width: 160px;
        height: 100px;
        border-radius: 6px;
        overflow: hidden;
        background: #f8f9fa;
    }

    .article-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .enhanced-article-item:hover .article-image {
        transform: scale(1.05);
    }

    .featured-badge {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: #a90418;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    /* Article Content Area */
    .article-content-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        justify-content: space-between;
    }

    .article-meta-row {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .asset-category {
        background: #011a2d;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .asset-category.stocks { background: #1f77b4; }
    .asset-category.forex { background: #ff7f0e; }
    .asset-category.crypto { background: #2ca02c; }
    .asset-category.crude_oil { background: #d62728; }
    .asset-category.gold { background: #ff9500; }
    .asset-category.commodities { background: #9467bd; }

    .meta-separator {
        color: #dee2e6;
        font-weight: 300;
    }

    .publish-time {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .view-count {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: #6c757d;
        font-size: 0.875rem;
    }

    .view-count i {
        font-size: 0.75rem;
    }

    /* Enhanced Article Title */
    .enhanced-article-title {
        margin: 0.25rem 0;
        font-size: 1.2rem;
        font-weight: 700;
        line-height: 1.3;
        font-family: 'Georgia', serif;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 3.2rem;
    }

    .title-link {
        color: #011a2d;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .title-link:hover {
        color: #a90418;
        text-decoration: none;
    }

    /* Enhanced Article Excerpt */
    .enhanced-article-excerpt {
        color: #495057;
        line-height: 1.6;
        font-size: 1.05rem;
        margin: 0;
        flex: 1;
        margin-right: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 3.4rem;
    }

    .article-excerpt-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        gap: 1rem;
    }

    /* Enhanced Article Footer */
    .enhanced-article-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f8f9fa;
    }

    .author-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .author-avatar-small {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #011a2d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .author-name {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .article-actions {
        display: flex;
        align-items: center;
    }

    .read-more-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: #a90418;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border: 1px solid #a90418;
        border-radius: 6px;
        background: transparent;
        transition: all 0.3s ease;
    }

    .read-more-btn:hover {
        background: #a90418;
        color: white;
        text-decoration: none;
        transform: translateX(3px);
    }

    .read-more-btn.external {
        border-color: #6c757d;
        color: #6c757d;
    }

    .read-more-btn.external:hover {
        background: #6c757d;
        color: white;
    }

    /* Enhanced Loading Spinner */
    .enhanced-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        padding: 2rem;
    }

    .spinner-ring {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #011a2d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .loading-text {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Enhanced Responsive Design */
    @media (max-width: 992px) {
        .header-content {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .enhanced-news-header {
            padding: 1.5rem;
        }

        .header-left .section-title {
            font-size: 1.75rem;
        }

        .article-layout {
            gap: 1rem;
        }

        .article-image-container {
            width: 160px;
            height: 110px;
        }
    }

    @media (max-width: 768px) {
        .enhanced-articles-container {
            padding: 1rem;
        }

        .enhanced-article-item {
            padding: 1rem;
        }

        .article-layout {
            flex-direction: column;
            gap: 1rem;
        }

        .article-image-container {
            width: 100%;
            height: 200px;
        }

        .enhanced-article-title {
            font-size: 1rem;
        }

        .enhanced-article-footer {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .article-actions {
            width: 100%;
        }

        .read-more-btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .enhanced-news-header {
            padding: 1rem;
        }

        .header-left .section-title {
            font-size: 1.5rem;
        }

        .section-subtitle {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .enhanced-article-title {
            font-size: 0.95rem;
        }

        .article-meta-row {
            flex-wrap: wrap;
            gap: 0.25rem;
        }
    }

    .news-header {
        border-bottom: 3px solid #011a2d;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }

    .news-header h2 {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.75rem;
        margin: 0;
    }

    .news-header .subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin-top: 0.5rem;
    }

    .article-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        position: relative;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .article-item:last-child {
        margin-bottom: 0;
    }

    .article-item:hover {
        background: white;
        border-color: #011a2d;
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(1, 26, 45, 0.15);
    }

    .article-content {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }

    .article-thumbnail {
        width: 180px;
        height: 120px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;
        position: relative;
        background: #f8f9fa;
    }

    .article-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .article-item:hover .article-thumbnail img {
        transform: scale(1.05);
    }

    .article-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .article-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .asset-type-badge {
        background: #011a2d;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .article-time {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .featured-indicator {
        background: #a90418;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .article-title {
        font-size: 1.375rem;
        font-weight: 700;
        color: #011a2d;
        line-height: 1.3;
        margin: 0;
        font-family: 'Georgia', serif;
        transition: color 0.3s ease;
    }

    .article-item:hover .article-title {
        color: #a90418;
    }

    .article-excerpt {
        color: #495057;
        line-height: 1.6;
        font-size: 1rem;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .article-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f8f9fa;
    }

    .article-author {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
        font-size: 0.875rem;
    }

    .author-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #011a2d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .learn-more-link {
        color: #a90418;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border: 1px solid #a90418;
        border-radius: 6px;
        background: transparent;
    }

    .learn-more-link:hover {
        background: #a90418;
        color: white;
        text-decoration: none;
        transform: translateX(3px);
    }

    /* Sidebar Styles */
    .sidebar {
        padding: 2rem 0;
    }

    .sidebar-widget {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .widget-header {
        background: #011a2d;
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 700;
        font-size: 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .widget-content {
        padding: 1.5rem;
    }

    .widget-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: #a90418;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        margin-top: 1rem;
        transition: all 0.3s ease;
    }

    .widget-link:hover {
        color: #8b0314;
        text-decoration: none;
        transform: translateX(3px);
    }

    .chart-container {
        min-height: 400px;
        height: 400px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        background: #f8f9fa;
    }

    .chart-container .tradingview-widget-container {
        height: 100% !important;
        width: 100% !important;
    }

    .chart-container .tradingview-widget-container__widget {
        height: 100% !important;
        width: 100% !important;
    }

    .calendar-container {
        min-height: 500px;
        height: 500px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        background: #f8f9fa;
    }

    .calendar-container .tradingview-widget-container {
        height: 100% !important;
        width: 100% !important;
    }

    /* Pagination */
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        padding: 1rem 0 0 0;
    }

    .pagination-btn {
        background: white;
        border: 2px solid #e9ecef;
        color: #6c757d;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .pagination-btn:hover {
        background: #011a2d;
        border-color: #011a2d;
        color: white;
        text-decoration: none;
    }

    .pagination-btn.active {
        background: #011a2d;
        border-color: #011a2d;
        color: white;
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Loading */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        border-radius: 12px;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #011a2d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 992px) {
        .filter-buttons {
            justify-content: flex-start;
            gap: 0.75rem;
        }

        .filter-btn {
            min-width: 120px;
            padding: 0.875rem 1.5rem;
        }

        .news-feed {
            padding: 2rem 0;
        }

        .article-content {
            gap: 1.5rem;
        }

        .article-thumbnail {
            width: 160px;
            height: 100px;
        }
    }

    @media (max-width: 768px) {
        .filter-buttons {
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .filter-btn {
            flex: 1 1 calc(50% - 0.25rem);
            min-width: auto;
            padding: 0.75rem 1rem;
            justify-content: center;
        }

        .enhanced-article-item {
            height: auto;
            min-height: 180px;
        }

        .article-excerpt-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .enhanced-article-excerpt {
            margin-right: 0;
        }

        .news-header h2 {
            font-size: 1.5rem;
        }

        .article-content {
            flex-direction: column;
            gap: 1rem;
        }

        .article-thumbnail {
            width: 100%;
            height: 200px;
        }

        .article-title {
            font-size: 1.25rem;
        }

        .article-excerpt {
            -webkit-line-clamp: 2;
        }

        .article-footer {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .learn-more-link {
            align-self: flex-end;
        }

        .sidebar {
            padding: 1rem 0;
        }

        .widget-content {
            padding: 1rem;
        }

        .chart-container,
        .calendar-container {
            min-height: 300px;
            height: 300px;
        }
    }

    @media (max-width: 576px) {
        .filter-section {
            padding: 1rem 0;
        }

        .news-feed {
            padding: 1.5rem 0;
        }

        .news-header {
            margin-bottom: 1.5rem;
        }

        .news-header h2 {
            font-size: 1.375rem;
        }

        .article-item {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .article-item:hover {
            transform: translateY(-2px);
        }

        .article-thumbnail {
            height: 180px;
        }

        .article-title {
            font-size: 1.125rem;
            line-height: 1.4;
        }

        .article-meta {
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .asset-type-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.6rem;
        }

        .learn-more-link {
            padding: 0.5rem 0.875rem;
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
\$(document).ready(function() {
    let currentFilter = '';
    let currentPage = {{ pagination.currentPage }};
    let isLoading = false;

    // Initialize current filter from URL
    const urlParams = new URLSearchParams(window.location.search);
    currentFilter = urlParams.get('asset_type') || '';

    // Filter functionality with URL-based navigation
    \$('.filter-btn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const \$clickedBtn = \$(this);
        const filter = \$clickedBtn.data('filter');

        // Handle filter selection with toggle behavior
        if (currentFilter === filter) {
            // Toggle off - redirect to base URL without parameters
            window.location.href = '{{ path('app_market_analysis') }}';
        } else {
            // Select new filter - redirect with asset_type parameter
            window.location.href = '{{ path('app_market_analysis') }}?asset_type=' + filter;
        }

        return false;
    });

    // Show filter feedback
    function showFilterFeedback(message) {
        // Remove existing feedback
        \$('.filter-feedback').remove();

        // Create feedback element
        const feedback = \$(`
            <div class=\"filter-feedback alert alert-info d-flex align-items-center\" style=\"
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 250px;
                background: #011a2d;
                color: white;
                border: none;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(1, 26, 45, 0.3);
            \">
                <i class=\"fas fa-filter me-2\"></i>
                \${message}
            </div>
        `);

        \$('body').append(feedback);

        // Auto remove after 3 seconds
        setTimeout(() => {
            feedback.fadeOut(() => feedback.remove());
        }, 3000);
    }

    // Pagination functionality
    \$(document).on('click', '.pagination-btn[data-page]', function(e) {
        e.preventDefault();
        e.stopPropagation();

        if (isLoading) return false;

        const page = parseInt(\$(this).data('page'));
        if (page === currentPage) return false;

        currentPage = page;
        loadArticles();

        // Scroll to top of articles
        \$('html, body').animate({
            scrollTop: \$('#articlesContainer').offset().top - 100
        }, 500);

        return false;
    });

    // Error message function
    function showErrorMessage(message) {
        const alertDiv = \$('<div class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">')
            .html(`<i class=\"fas fa-exclamation-triangle me-2\"></i>\${message}<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>`)
            .css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: 9999,
                minWidth: '300px'
            });

        \$('body').append(alertDiv);

        setTimeout(() => {
            alertDiv.fadeOut(() => alertDiv.remove());
        }, 5000);
    }

    function loadArticles() {
        if (isLoading) return Promise.resolve();

        isLoading = true;
        \$('#loadingOverlay').removeClass('d-none');

        const params = new URLSearchParams({
            page: currentPage
        });

        if (currentFilter) {
            params.append('asset_type', currentFilter);
        }

        const url = '{{ path('api_market_analysis_filter') }}?' + params.toString();

        return fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: \${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateArticlesGrid(data.articles);
                updatePagination(data.pagination);
                updateResultsCount(data.pagination);

                // Update URL without page reload (remove hash)
                const newUrl = new URL(window.location.origin + window.location.pathname);
                if (currentFilter) {
                    newUrl.searchParams.set('asset_type', currentFilter);
                }
                if (currentPage > 1) {
                    newUrl.searchParams.set('page', currentPage);
                }
                window.history.replaceState({}, '', newUrl.toString());
            } else {
                console.error('Error loading articles:', data.message);
                showErrorMessage('Failed to load articles. Please try again.');
            }
        })
        .catch(error => {
            console.error('AJAX Error:', error);
            showErrorMessage('Network error. Please check your connection and try again.');
        })
        .finally(() => {
            isLoading = false;
            \$('#loadingOverlay').addClass('d-none');
        });
    }

    function updateArticlesGrid(articles) {
        const grid = \$('#articlesGrid');
        grid.empty();

        articles.forEach(article => {
            const articleHtml = createArticleItem(article);
            grid.append(articleHtml);
        });

        // Animate in new articles
        grid.find('.enhanced-article-item').each(function(index) {
            \$(this).css('opacity', '0').delay(index * 100).animate({opacity: 1}, 300);
        });
    }

    function createArticleItem(article) {
        const featuredBadge = '';

        // Remove author section - no longer needed

        const linkUrl = article.detailUrl || '#';
        const linkTarget = '';
        const linkIcon = 'fas fa-arrow-right';
        const linkClass = 'read-more-btn';

        const viewCount = article.views > 0 ?
            `<span class=\"meta-separator\">•</span>
             <span class=\"view-count\">
                <i class=\"fas fa-eye\"></i>
                \${article.views}
             </span>` : '';

        return `
            <article class=\"enhanced-article-item\" data-article-id=\"\${article.id}\">
                <div class=\"article-layout\">
                    <div class=\"article-image-container\">
                        <img src=\"\${article.thumbnailUrl}\" alt=\"\${article.title}\" class=\"article-image\" loading=\"lazy\">
                        \${featuredBadge}
                    </div>
                    <div class=\"article-content-area\">
                        <div class=\"article-meta-row\">
                            <span class=\"asset-category \${article.assetType}\">\${article.assetTypeLabel}</span>
                            <span class=\"meta-separator\">•</span>
                            <span class=\"publish-time\">\${article.publishDate}</span>
                            \${viewCount}
                        </div>
                        <h3 class=\"enhanced-article-title\">
                            <a href=\"\${linkUrl}\" class=\"title-link\">\${article.title}</a>
                        </h3>
                        <div class=\"article-excerpt-row\">
                            <p class=\"enhanced-article-excerpt\">\${article.excerpt.split('.')[0]}.</p>
                            <div class=\"article-actions\">
                                <a href=\"\${linkUrl}\" \${linkTarget} class=\"\${linkClass}\">
                                    <span>Read Analysis</span>
                                    <i class=\"\${linkIcon}\"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        `;
    }

    function updatePagination(pagination) {
        const wrapper = \$('#paginationWrapper');
        if (pagination.totalPages <= 1) {
            wrapper.hide();
            return;
        }

        wrapper.show();
        wrapper.empty();

        // Previous button
        if (pagination.hasPrev) {
            wrapper.append(`<a href=\"#\" class=\"pagination-btn\" data-page=\"\${pagination.currentPage - 1}\"><i class=\"fas fa-chevron-left me-2\"></i>Previous</a>`);
        }

        // Page numbers
        for (let page = 1; page <= pagination.totalPages; page++) {
            if (page === pagination.currentPage) {
                wrapper.append(`<span class=\"pagination-btn active\">\${page}</span>`);
            } else if (page <= 3 || page > pagination.totalPages - 3 || (page >= pagination.currentPage - 1 && page <= pagination.currentPage + 1)) {
                wrapper.append(`<a href=\"#\" class=\"pagination-btn\" data-page=\"\${page}\">\${page}</a>`);
            } else if (page === 4 && pagination.currentPage > 5) {
                wrapper.append(`<span class=\"pagination-btn\">...</span>`);
            } else if (page === pagination.totalPages - 3 && pagination.currentPage < pagination.totalPages - 4) {
                wrapper.append(`<span class=\"pagination-btn\">...</span>`);
            }
        }

        // Next button
        if (pagination.hasNext) {
            wrapper.append(`<a href=\"#\" class=\"pagination-btn\" data-page=\"\${pagination.currentPage + 1}\">Next<i class=\"fas fa-chevron-right ms-2\"></i></a>`);
        }
    }

    function updateResultsCount(pagination) {
        const start = (pagination.currentPage - 1) * 4 + 1;
        const end = Math.min(pagination.currentPage * 4, pagination.totalArticles);
        \$('#resultsCount').text(`\${start}-\${end} of \${pagination.totalArticles} articles`);
    }


});
</script>
{% endblock %}

{% block body %}
<div class=\"main-content\">
    <!-- Filter Section -->
    <section class=\"filter-section\">
        <div class=\"container\">
            <div class=\"filter-buttons\" id=\"filterButtons\">
                {% for assetType, data in assetTypesWithCounts %}
                    <a href=\"#\" class=\"filter-btn{% if assetType == currentAssetType %} active{% endif %}\" data-filter=\"{{ assetType }}\">
                        {% if assetType == 'stocks' %}
                            <i class=\"fas fa-chart-bar me-2\"></i>
                        {% elseif assetType == 'forex' %}
                            <i class=\"fas fa-exchange-alt me-2\"></i>
                        {% elseif assetType == 'crypto' %}
                            <i class=\"fab fa-bitcoin me-2\"></i>
                        {% elseif assetType == 'crude_oil' %}
                            <i class=\"fas fa-oil-can me-2\"></i>
                        {% elseif assetType == 'gold' %}
                            <i class=\"fas fa-coins me-2\"></i>
                        {% elseif assetType == 'commodities' %}
                            <i class=\"fas fa-seedling me-2\"></i>
                        {% endif %}
                        {{ data.label }}
                    </a>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class=\"container\">
        <div class=\"row\">
            <!-- News Feed Section (Left Column) -->
            <div class=\"col-lg-8\">
                <section class=\"enhanced-news-feed\">
                    <!-- Section Header -->
                    <div class=\"widget-header\">
                        <i class=\"fas fa-chart-line me-2\"></i>Latest Market Analysis
                    </div>

                    <!-- Articles Container -->
                    <div class=\"enhanced-articles-container\" id=\"articlesContainer\">
                        <div class=\"articles-list\" id=\"articlesGrid\">
                            {% for article in articles %}
                                <article class=\"enhanced-article-item\" data-article-id=\"{{ article.id }}\">
                                    <div class=\"article-layout\">
                                        <!-- Article Thumbnail -->
                                        <div class=\"article-image-container\">
                                            <img src=\"{{ article.thumbnailUrl }}\"
                                                 alt=\"{{ article.title }}\"
                                                 class=\"article-image\"
                                                 loading=\"lazy\">

                                        </div>

                                        <!-- Article Content -->
                                        <div class=\"article-content-area\">
                                            <!-- Meta Information -->
                                            <div class=\"article-meta-row\">
                                                <span class=\"asset-category {{ article.assetType }}\">{{ article.assetTypeLabel }}</span>
                                                <span class=\"meta-separator\">•</span>
                                                <span class=\"publish-time\">{{ article.relativeTime }}</span>
                                            </div>

                                            <!-- Article Title -->
                                            <h3 class=\"enhanced-article-title\">
                                                <a href=\"{{ path('app_market_analysis_show_seo', {'slug': article.slug}) }}\" class=\"title-link\">
                                                    {{ article.title }}
                                                </a>
                                            </h3>

                                            <!-- Article Excerpt and Action -->
                                            <div class=\"article-excerpt-row\">
                                                <p class=\"enhanced-article-excerpt\">{{ article.shortExcerpt(80)|split('.')|first }}.</p>
                                                <div class=\"article-actions\">
                                                    <a href=\"{{ path('app_market_analysis_show_seo', {'slug': article.slug}) }}\" class=\"read-more-btn\">
                                                        <span>Read Analysis</span>
                                                        <i class=\"fas fa-arrow-right\"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            {% endfor %}
                        </div>

                        <!-- Loading Overlay -->
                        <div class=\"loading-overlay d-none\" id=\"loadingOverlay\">
                            <div class=\"enhanced-spinner\">
                                <div class=\"spinner-ring\"></div>
                                <span class=\"loading-text\">Loading articles...</span>
                            </div>
                        </div>
                    </div>
            
            <!-- Pagination -->
            {% if pagination.totalPages > 1 %}
                <div class=\"pagination-wrapper\" id=\"paginationWrapper\">
                    {% if pagination.hasPrev %}
                        <a href=\"#\" class=\"pagination-btn\" data-page=\"{{ pagination.currentPage - 1 }}\">
                            <i class=\"fas fa-chevron-left me-2\"></i>Previous
                        </a>
                    {% endif %}
                    
                    {% for page in 1..pagination.totalPages %}
                        {% if page == pagination.currentPage %}
                            <span class=\"pagination-btn active\">{{ page }}</span>
                        {% elseif page <= 3 or page > pagination.totalPages - 3 or (page >= pagination.currentPage - 1 and page <= pagination.currentPage + 1) %}
                            <a href=\"#\" class=\"pagination-btn\" data-page=\"{{ page }}\">{{ page }}</a>
                        {% elseif page == 4 and pagination.currentPage > 5 %}
                            <span class=\"pagination-btn\">...</span>
                        {% elseif page == pagination.totalPages - 3 and pagination.currentPage < pagination.totalPages - 4 %}
                            <span class=\"pagination-btn\">...</span>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.hasNext %}
                        <a href=\"#\" class=\"pagination-btn\" data-page=\"{{ pagination.currentPage + 1 }}\">
                            Next<i class=\"fas fa-chevron-right ms-2\"></i>
                        </a>
                    {% endif %}
                </div>
            {% endif %}
                </section>
            </div>

            <!-- Sidebar (Right Column) -->
            <div class=\"col-lg-4\">
                <aside class=\"sidebar\">
                    <!-- Live Charts Widget -->
                    <div class=\"sidebar-widget\">
                        <div class=\"widget-header\">
                            <i class=\"fas fa-chart-line me-2\"></i>Live Charts
                        </div>
                        <div class=\"widget-content\">
                            <div class=\"chart-container\">
                                <!-- TradingView Widget BEGIN -->
                                <div class=\"tradingview-widget-container\">
                                  <div class=\"tradingview-widget-container__widget\"></div>
                                  <div class=\"tradingview-widget-copyright\"><a href=\"https://www.tradingview.com/\" rel=\"noopener nofollow\" target=\"_blank\"><span class=\"blue-text\">Track all markets on TradingView</span></a></div>
                                  <script type=\"text/javascript\" src=\"https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js\" async>
                                  {
                                  \"width\": \"100%\",
                                  \"height\": \"370\",
                                  \"symbol\": \"MARKETSCOM:OIL\",
                                  \"interval\": \"D\",
                                  \"timezone\": \"Etc/UTC\",
                                  \"theme\": \"light\",
                                  \"style\": \"1\",
                                  \"locale\": \"en\",
                                  \"toolbar_bg\": \"#f1f3f6\",
                                  \"enable_publishing\": false,
                                  \"allow_symbol_change\": true,
                                  \"container_id\": \"tradingview_chart\",
                                  \"hide_side_toolbar\": false,
                                  \"hide_top_toolbar\": false,
                                  \"hide_legend\": false,
                                  \"save_image\": false,
                                  \"calendar\": false,
                                  \"hide_volume\": false,
                                  \"support_host\": \"https://www.tradingview.com\"
                                }
                                  </script>
                                </div>
                                <!-- TradingView Widget END -->
                            </div>
                            <a href=\"#\" class=\"widget-link\">
                                <i class=\"fas fa-external-link-alt\"></i>View Full Screen Chart
                            </a>
                        </div>
                    </div>

                    <!-- Economic Calendar Widget -->
                    <div class=\"sidebar-widget\">
                        <div class=\"widget-header\">
                            <i class=\"fas fa-calendar-alt me-2\"></i>Economic Calendar
                        </div>
                        <div class=\"widget-content\">
                            <div class=\"calendar-container\">
                                <!-- TradingView Widget BEGIN -->
                                <div class=\"tradingview-widget-container\">
                                  <div class=\"tradingview-widget-container__widget\"></div>
                                  <div class=\"tradingview-widget-copyright\"><a href=\"https://www.tradingview.com/\" rel=\"noopener nofollow\" target=\"_blank\"><span class=\"blue-text\">Track all markets on TradingView</span></a></div>
                                  <script type=\"text/javascript\" src=\"https://s3.tradingview.com/external-embedding/embed-widget-events.js\" async>
                                  {
                                  \"colorTheme\": \"light\",
                                  \"isTransparent\": false,
                                  \"width\": \"100%\",
                                  \"height\": \"470\",
                                  \"locale\": \"en\",
                                  \"countryFilter\": \"ar,au,br,ca,cn,fr,de,in,id,it,jp,kr,mx,ru,sa,za,tr,gb,us,eu\",
                                  \"importanceFilter\": \"-1,0,1\"
                                }
                                  </script>
                                </div>
                                <!-- TradingView Widget END -->
                            </div>
                            <a href=\"#\" class=\"widget-link\">
                                <i class=\"fas fa-external-link-alt\"></i>View Full Calendar
                            </a>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </div>
</section>
</div>

{% endblock %}
", "market_analysis/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\market_analysis\\index.html.twig");
    }
}
