<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/admins/view.html.twig */
class __TwigTemplate_fdb74c759e9fabd70ce5693ab1354f4f extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/view.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/admins/view.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "View Administrator - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Administrator Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admins");
        yield "\">Administrators</a></li>
<li class=\"breadcrumb-item active\">";
        // line 9
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 9, $this->source); })()), "fullName", [], "any", false, false, false, 9), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        yield "<!-- Professional Header Section -->
<div class=\"row mb-4\">
    <div class=\"col-12\">
        <div class=\"card shadow-sm border-0\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
            <div class=\"card-body py-4\">
                <div class=\"row align-items-center\">
                    <div class=\"col-lg-8\">
                        <div class=\"d-flex align-items-center\">
                            ";
        // line 21
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 21, $this->source); })()), "profileImage", [], "any", false, false, false, 21)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 22
            yield "                                <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/profiles/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 22, $this->source); })()), "profileImage", [], "any", false, false, false, 22))), "html", null, true);
            yield "\" 
                                     alt=\"";
            // line 23
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 23, $this->source); })()), "fullName", [], "any", false, false, false, 23), "html", null, true);
            yield "\" 
                                     class=\"rounded-circle me-4\"
                                     style=\"width: 80px; height: 80px; object-fit: cover; border: 3px solid rgba(255,255,255,0.3);\">
                            ";
        } else {
            // line 27
            yield "                                <div class=\"rounded-circle me-4 d-flex align-items-center justify-content-center\"
                                     style=\"width: 80px; height: 80px; background: rgba(255,255,255,0.2); border: 3px solid rgba(255,255,255,0.3);\">
                                    <span class=\"fs-2 fw-bold\">";
            // line 29
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 29, $this->source); })()), "fullName", [], "any", false, false, false, 29), 0, 1)), "html", null, true);
            yield "</span>
                                </div>
                            ";
        }
        // line 32
        yield "                            <div>
                                <h1 class=\"h3 mb-2 fw-bold\">";
        // line 33
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 33, $this->source); })()), "fullName", [], "any", false, false, false, 33), "html", null, true);
        yield "</h1>
                                <p class=\"mb-0 opacity-90\">
                                    <i class=\"fas fa-user-shield me-2\"></i>Administrator Profile
                                    ";
        // line 36
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 36, $this->source); })()), "isMasterAdmin", [], "any", false, false, false, 36)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 37
            yield "                                        <span class=\"badge bg-warning text-dark ms-2\">
                                            <i class=\"fas fa-crown me-1\"></i>Master Admin
                                        </span>
                                    ";
        }
        // line 41
        yield "                                </p>
                            </div>
                        </div>
                    </div>
                    <div class=\"col-lg-4 text-lg-end\">
                        <div class=\"d-flex flex-wrap gap-2 justify-content-lg-end\">
                            ";
        // line 47
        if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 47, $this->source); })()), "isMasterAdmin", [], "any", false, false, false, 47)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 48
            yield "                                <a href=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admin_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 48, $this->source); })()), "id", [], "any", false, false, false, 48)]), "html", null, true);
            yield "\" class=\"btn btn-light btn-lg\">
                                    <i class=\"fas fa-edit me-2\"></i>Edit Admin
                                </a>
                            ";
        }
        // line 52
        yield "                            <a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admins");
        yield "\" class=\"btn btn-outline-light btn-lg\">
                                <i class=\"fas fa-arrow-left me-2\"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Information -->
<div class=\"row\">
    <div class=\"col-lg-8\">
        <!-- Basic Information -->
        <div class=\"card border-0 shadow-sm mb-4\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-info-circle me-2 text-primary\"></i>Basic Information
                </h5>
            </div>
            <div class=\"card-body\">
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Full Name</label>
                            <p class=\"mb-0\">";
        // line 78
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 78, $this->source); })()), "fullName", [], "any", false, false, false, 78), "html", null, true);
        yield "</p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Username</label>
                            <p class=\"mb-0\">";
        // line 82
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 82, $this->source); })()), "username", [], "any", false, false, false, 82), "html", null, true);
        yield "</p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Email Address</label>
                            <p class=\"mb-0\">
                                <a href=\"mailto:";
        // line 87
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 87, $this->source); })()), "email", [], "any", false, false, false, 87), "html", null, true);
        yield "\" class=\"text-decoration-none\">
                                    ";
        // line 88
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 88, $this->source); })()), "email", [], "any", false, false, false, 88), "html", null, true);
        yield "
                                </a>
                            </p>
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Account Status</label>
                            <p class=\"mb-0\">
                                ";
        // line 97
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 97, $this->source); })()), "isActive", [], "any", false, false, false, 97)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 98
            yield "                                    <span class=\"badge bg-success px-3 py-2\">
                                        <i class=\"fas fa-check-circle me-1\"></i>Active
                                    </span>
                                ";
        } else {
            // line 102
            yield "                                    <span class=\"badge bg-danger px-3 py-2\">
                                        <i class=\"fas fa-ban me-1\"></i>Blocked
                                    </span>
                                ";
        }
        // line 106
        yield "                            </p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Account Type</label>
                            <p class=\"mb-0\">
                                ";
        // line 111
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 111, $this->source); })()), "isMasterAdmin", [], "any", false, false, false, 111)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 112
            yield "                                    <span class=\"badge bg-warning text-dark px-3 py-2\">
                                        <i class=\"fas fa-crown me-1\"></i>Master Administrator
                                    </span>
                                ";
        } else {
            // line 116
            yield "                                    <span class=\"badge bg-primary px-3 py-2\">
                                        <i class=\"fas fa-user-shield me-1\"></i>Administrator
                                    </span>
                                ";
        }
        // line 120
        yield "                            </p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Created Date</label>
                            <p class=\"mb-0\">";
        // line 124
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 124, $this->source); })()), "createdAt", [], "any", false, false, false, 124), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions -->
        <div class=\"card border-0 shadow-sm mb-4\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-shield-alt me-2 text-primary\"></i>Permissions
                </h5>
            </div>
            <div class=\"card-body\">
                ";
        // line 139
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 139, $this->source); })()), "isMasterAdmin", [], "any", false, false, false, 139)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 140
            yield "                    <div class=\"alert alert-warning border-0\">
                        <i class=\"fas fa-crown me-2\"></i>
                        <strong>Master Administrator:</strong> This account has full access to all system features and cannot be restricted.
                    </div>
                ";
        } else {
            // line 145
            yield "                    ";
            $context["permissions"] = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 145, $this->source); })()), "permissions", [], "any", false, false, false, 145);
            // line 146
            yield "                    ";
            if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["permissions"]) || array_key_exists("permissions", $context) ? $context["permissions"] : (function () { throw new RuntimeError('Variable "permissions" does not exist.', 146, $this->source); })())) > 0)) {
                // line 147
                yield "                        <div class=\"row\">
                            ";
                // line 148
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable((isset($context["permissions"]) || array_key_exists("permissions", $context) ? $context["permissions"] : (function () { throw new RuntimeError('Variable "permissions" does not exist.', 148, $this->source); })()));
                foreach ($context['_seq'] as $context["_key"] => $context["permission"]) {
                    // line 149
                    yield "                                <div class=\"col-md-6 mb-2\">
                                    <span class=\"badge bg-light text-dark border px-3 py-2\">
                                        <i class=\"fas fa-check text-success me-2\"></i>";
                    // line 151
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["permission"], "html", null, true);
                    yield "
                                    </span>
                                </div>
                            ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['permission'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 155
                yield "                        </div>
                    ";
            } else {
                // line 157
                yield "                        <div class=\"alert alert-info border-0\">
                            <i class=\"fas fa-info-circle me-2\"></i>
                            No specific permissions assigned. Contact the master administrator to assign permissions.
                        </div>
                    ";
            }
            // line 162
            yield "                ";
        }
        // line 163
        yield "            </div>
        </div>
    </div>

    <div class=\"col-lg-4\">
        <!-- Quick Actions -->
        <div class=\"card border-0 shadow-sm mb-4\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-bolt me-2 text-primary\"></i>Quick Actions
                </h5>
            </div>
            <div class=\"card-body\">
                ";
        // line 176
        if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 176, $this->source); })()), "isMasterAdmin", [], "any", false, false, false, 176)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 177
            yield "                    <div class=\"d-grid gap-2\">
                        <a href=\"";
            // line 178
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admin_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 178, $this->source); })()), "id", [], "any", false, false, false, 178)]), "html", null, true);
            yield "\" class=\"btn btn-primary\">
                            <i class=\"fas fa-edit me-2\"></i>Edit Administrator
                        </a>
                        ";
            // line 181
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 181, $this->source); })()), "isActive", [], "any", false, false, false, 181)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 182
                yield "                            <button type=\"button\" class=\"btn btn-warning\" onclick=\"toggleAdminStatus(";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 182, $this->source); })()), "id", [], "any", false, false, false, 182), "html", null, true);
                yield ", '";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 182, $this->source); })()), "fullName", [], "any", false, false, false, 182), "html", null, true);
                yield "', false)\">
                                <i class=\"fas fa-lock me-2\"></i>Block Administrator
                            </button>
                        ";
            } else {
                // line 186
                yield "                            <button type=\"button\" class=\"btn btn-success\" onclick=\"toggleAdminStatus(";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 186, $this->source); })()), "id", [], "any", false, false, false, 186), "html", null, true);
                yield ", '";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 186, $this->source); })()), "fullName", [], "any", false, false, false, 186), "html", null, true);
                yield "', true)\">
                                <i class=\"fas fa-unlock me-2\"></i>Unblock Administrator
                            </button>
                        ";
            }
            // line 190
            yield "                        <button type=\"button\" class=\"btn btn-danger\" onclick=\"confirmDeleteAdmin(";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 190, $this->source); })()), "id", [], "any", false, false, false, 190), "html", null, true);
            yield ", '";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 190, $this->source); })()), "username", [], "any", false, false, false, 190), "html", null, true);
            yield "', '";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 190, $this->source); })()), "fullName", [], "any", false, false, false, 190), "html", null, true);
            yield "')\">
                            <i class=\"fas fa-trash me-2\"></i>Delete Administrator
                        </button>
                    </div>
                ";
        } else {
            // line 195
            yield "                    <div class=\"alert alert-info border-0 mb-0\">
                        <i class=\"fas fa-shield-alt me-2\"></i>
                        Master administrator account cannot be modified.
                    </div>
                ";
        }
        // line 200
        yield "            </div>
        </div>

        <!-- Account Statistics -->
        <div class=\"card border-0 shadow-sm\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-chart-bar me-2 text-primary\"></i>Account Statistics
                </h5>
            </div>
            <div class=\"card-body\">
                <div class=\"row text-center\">
                    <div class=\"col-6\">
                        <div class=\"border-end\">
                            <h4 class=\"fw-bold text-primary mb-1\">";
        // line 214
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((int) floor(((CoreExtension::getAttribute($this->env, $this->source, $this->extensions['Twig\Extension\CoreExtension']->convertDate(), "timestamp", [], "any", false, false, false, 214) - CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 214, $this->source); })()), "createdAt", [], "any", false, false, false, 214), "timestamp", [], "any", false, false, false, 214)) / 86400)), "html", null, true);
        yield "</h4>
                            <small class=\"text-muted\">Days Active</small>
                        </div>
                    </div>
                    <div class=\"col-6\">
                        <h4 class=\"fw-bold text-success mb-1\">";
        // line 219
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 219, $this->source); })()), "permissions", [], "any", false, false, false, 219)), "html", null, true);
        yield "</h4>
                        <small class=\"text-muted\">Permissions</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 229
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 230
        yield "<script>
// Toggle admin status (block/unblock)
function toggleAdminStatus(adminId, fullName, activate) {
    const action = activate ? 'activate' : 'block';
    
    // if (confirm(`Are you sure you want to \${action} \${fullName}?`)) { // REMOVED
    if (true) { // Always proceed without confirmation
        \$.ajax({
            url: `/admin/admin/\${adminId}/toggle-status`,
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    // showAdminNotification(response.message, 'success'); // REMOVED
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    // showAdminNotification(response.message, 'error'); // REMOVED
                }
            },
            error: function() {
                // showAdminNotification('An error occurred while updating the administrator status.', 'error'); // REMOVED
            }
        });
    }
}

// Delete admin confirmation
function confirmDeleteAdmin(adminId, username, fullName) {
    // if (confirm(`Are you sure you want to delete the administrator account for \${fullName}? This action cannot be undone.`)) { // REMOVED
    if (true) { // Always proceed without confirmation
        \$.ajax({
            url: `/admin/admin/\${adminId}/delete`,
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    // showAdminNotification(response.message, 'success'); // REMOVED
                    setTimeout(() => {
                        window.location.href = '";
        // line 274
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admins");
        yield "';
                    }, 1000);
                } else {
                    // showAdminNotification(response.message, 'error'); // REMOVED
                }
            },
            error: function() {
                // showAdminNotification('An error occurred while deleting the administrator.', 'error'); // REMOVED
            }
        });
    }
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/admins/view.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  567 => 274,  521 => 230,  508 => 229,  488 => 219,  480 => 214,  464 => 200,  457 => 195,  444 => 190,  434 => 186,  424 => 182,  422 => 181,  416 => 178,  413 => 177,  411 => 176,  396 => 163,  393 => 162,  386 => 157,  382 => 155,  372 => 151,  368 => 149,  364 => 148,  361 => 147,  358 => 146,  355 => 145,  348 => 140,  346 => 139,  328 => 124,  322 => 120,  316 => 116,  310 => 112,  308 => 111,  301 => 106,  295 => 102,  289 => 98,  287 => 97,  275 => 88,  271 => 87,  263 => 82,  256 => 78,  226 => 52,  218 => 48,  216 => 47,  208 => 41,  202 => 37,  200 => 36,  194 => 33,  191 => 32,  185 => 29,  181 => 27,  174 => 23,  169 => 22,  167 => 21,  157 => 13,  144 => 12,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}View Administrator - Capitol Academy Admin{% endblock %}

{% block page_title %}Administrator Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_admins') }}\">Administrators</a></li>
<li class=\"breadcrumb-item active\">{{ admin.fullName }}</li>
{% endblock %}

{% block content %}
<!-- Professional Header Section -->
<div class=\"row mb-4\">
    <div class=\"col-12\">
        <div class=\"card shadow-sm border-0\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
            <div class=\"card-body py-4\">
                <div class=\"row align-items-center\">
                    <div class=\"col-lg-8\">
                        <div class=\"d-flex align-items-center\">
                            {% if admin.profileImage %}
                                <img src=\"{{ asset('uploads/profiles/' ~ admin.profileImage) }}\" 
                                     alt=\"{{ admin.fullName }}\" 
                                     class=\"rounded-circle me-4\"
                                     style=\"width: 80px; height: 80px; object-fit: cover; border: 3px solid rgba(255,255,255,0.3);\">
                            {% else %}
                                <div class=\"rounded-circle me-4 d-flex align-items-center justify-content-center\"
                                     style=\"width: 80px; height: 80px; background: rgba(255,255,255,0.2); border: 3px solid rgba(255,255,255,0.3);\">
                                    <span class=\"fs-2 fw-bold\">{{ admin.fullName|slice(0,1)|upper }}</span>
                                </div>
                            {% endif %}
                            <div>
                                <h1 class=\"h3 mb-2 fw-bold\">{{ admin.fullName }}</h1>
                                <p class=\"mb-0 opacity-90\">
                                    <i class=\"fas fa-user-shield me-2\"></i>Administrator Profile
                                    {% if admin.isMasterAdmin %}
                                        <span class=\"badge bg-warning text-dark ms-2\">
                                            <i class=\"fas fa-crown me-1\"></i>Master Admin
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class=\"col-lg-4 text-lg-end\">
                        <div class=\"d-flex flex-wrap gap-2 justify-content-lg-end\">
                            {% if not admin.isMasterAdmin %}
                                <a href=\"{{ path('admin_admin_edit', {'id': admin.id}) }}\" class=\"btn btn-light btn-lg\">
                                    <i class=\"fas fa-edit me-2\"></i>Edit Admin
                                </a>
                            {% endif %}
                            <a href=\"{{ path('admin_admins') }}\" class=\"btn btn-outline-light btn-lg\">
                                <i class=\"fas fa-arrow-left me-2\"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Information -->
<div class=\"row\">
    <div class=\"col-lg-8\">
        <!-- Basic Information -->
        <div class=\"card border-0 shadow-sm mb-4\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-info-circle me-2 text-primary\"></i>Basic Information
                </h5>
            </div>
            <div class=\"card-body\">
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Full Name</label>
                            <p class=\"mb-0\">{{ admin.fullName }}</p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Username</label>
                            <p class=\"mb-0\">{{ admin.username }}</p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Email Address</label>
                            <p class=\"mb-0\">
                                <a href=\"mailto:{{ admin.email }}\" class=\"text-decoration-none\">
                                    {{ admin.email }}
                                </a>
                            </p>
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Account Status</label>
                            <p class=\"mb-0\">
                                {% if admin.isActive %}
                                    <span class=\"badge bg-success px-3 py-2\">
                                        <i class=\"fas fa-check-circle me-1\"></i>Active
                                    </span>
                                {% else %}
                                    <span class=\"badge bg-danger px-3 py-2\">
                                        <i class=\"fas fa-ban me-1\"></i>Blocked
                                    </span>
                                {% endif %}
                            </p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Account Type</label>
                            <p class=\"mb-0\">
                                {% if admin.isMasterAdmin %}
                                    <span class=\"badge bg-warning text-dark px-3 py-2\">
                                        <i class=\"fas fa-crown me-1\"></i>Master Administrator
                                    </span>
                                {% else %}
                                    <span class=\"badge bg-primary px-3 py-2\">
                                        <i class=\"fas fa-user-shield me-1\"></i>Administrator
                                    </span>
                                {% endif %}
                            </p>
                        </div>
                        <div class=\"mb-3\">
                            <label class=\"form-label fw-bold text-muted\">Created Date</label>
                            <p class=\"mb-0\">{{ admin.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions -->
        <div class=\"card border-0 shadow-sm mb-4\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-shield-alt me-2 text-primary\"></i>Permissions
                </h5>
            </div>
            <div class=\"card-body\">
                {% if admin.isMasterAdmin %}
                    <div class=\"alert alert-warning border-0\">
                        <i class=\"fas fa-crown me-2\"></i>
                        <strong>Master Administrator:</strong> This account has full access to all system features and cannot be restricted.
                    </div>
                {% else %}
                    {% set permissions = admin.permissions %}
                    {% if permissions|length > 0 %}
                        <div class=\"row\">
                            {% for permission in permissions %}
                                <div class=\"col-md-6 mb-2\">
                                    <span class=\"badge bg-light text-dark border px-3 py-2\">
                                        <i class=\"fas fa-check text-success me-2\"></i>{{ permission }}
                                    </span>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class=\"alert alert-info border-0\">
                            <i class=\"fas fa-info-circle me-2\"></i>
                            No specific permissions assigned. Contact the master administrator to assign permissions.
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    <div class=\"col-lg-4\">
        <!-- Quick Actions -->
        <div class=\"card border-0 shadow-sm mb-4\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-bolt me-2 text-primary\"></i>Quick Actions
                </h5>
            </div>
            <div class=\"card-body\">
                {% if not admin.isMasterAdmin %}
                    <div class=\"d-grid gap-2\">
                        <a href=\"{{ path('admin_admin_edit', {'id': admin.id}) }}\" class=\"btn btn-primary\">
                            <i class=\"fas fa-edit me-2\"></i>Edit Administrator
                        </a>
                        {% if admin.isActive %}
                            <button type=\"button\" class=\"btn btn-warning\" onclick=\"toggleAdminStatus({{ admin.id }}, '{{ admin.fullName }}', false)\">
                                <i class=\"fas fa-lock me-2\"></i>Block Administrator
                            </button>
                        {% else %}
                            <button type=\"button\" class=\"btn btn-success\" onclick=\"toggleAdminStatus({{ admin.id }}, '{{ admin.fullName }}', true)\">
                                <i class=\"fas fa-unlock me-2\"></i>Unblock Administrator
                            </button>
                        {% endif %}
                        <button type=\"button\" class=\"btn btn-danger\" onclick=\"confirmDeleteAdmin({{ admin.id }}, '{{ admin.username }}', '{{ admin.fullName }}')\">
                            <i class=\"fas fa-trash me-2\"></i>Delete Administrator
                        </button>
                    </div>
                {% else %}
                    <div class=\"alert alert-info border-0 mb-0\">
                        <i class=\"fas fa-shield-alt me-2\"></i>
                        Master administrator account cannot be modified.
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Account Statistics -->
        <div class=\"card border-0 shadow-sm\">
            <div class=\"card-header bg-white border-bottom py-3\">
                <h5 class=\"mb-0 fw-bold text-dark\">
                    <i class=\"fas fa-chart-bar me-2 text-primary\"></i>Account Statistics
                </h5>
            </div>
            <div class=\"card-body\">
                <div class=\"row text-center\">
                    <div class=\"col-6\">
                        <div class=\"border-end\">
                            <h4 class=\"fw-bold text-primary mb-1\">{{ (date().timestamp - admin.createdAt.timestamp) // 86400 }}</h4>
                            <small class=\"text-muted\">Days Active</small>
                        </div>
                    </div>
                    <div class=\"col-6\">
                        <h4 class=\"fw-bold text-success mb-1\">{{ admin.permissions|length }}</h4>
                        <small class=\"text-muted\">Permissions</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
// Toggle admin status (block/unblock)
function toggleAdminStatus(adminId, fullName, activate) {
    const action = activate ? 'activate' : 'block';
    
    // if (confirm(`Are you sure you want to \${action} \${fullName}?`)) { // REMOVED
    if (true) { // Always proceed without confirmation
        \$.ajax({
            url: `/admin/admin/\${adminId}/toggle-status`,
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    // showAdminNotification(response.message, 'success'); // REMOVED
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    // showAdminNotification(response.message, 'error'); // REMOVED
                }
            },
            error: function() {
                // showAdminNotification('An error occurred while updating the administrator status.', 'error'); // REMOVED
            }
        });
    }
}

// Delete admin confirmation
function confirmDeleteAdmin(adminId, username, fullName) {
    // if (confirm(`Are you sure you want to delete the administrator account for \${fullName}? This action cannot be undone.`)) { // REMOVED
    if (true) { // Always proceed without confirmation
        \$.ajax({
            url: `/admin/admin/\${adminId}/delete`,
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    // showAdminNotification(response.message, 'success'); // REMOVED
                    setTimeout(() => {
                        window.location.href = '{{ path('admin_admins') }}';
                    }, 1000);
                } else {
                    // showAdminNotification(response.message, 'error'); // REMOVED
                }
            },
            error: function() {
                // showAdminNotification('An error occurred while deleting the administrator.', 'error'); // REMOVED
            }
        });
    }
}
</script>
{% endblock %}
", "admin/admins/view.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\admins\\view.html.twig");
    }
}
