<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* user/home.html.twig */
class __TwigTemplate_d713e49ebefb5e18f3894fec5da59871 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "user/home.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "user/home.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Welcome - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "<div class=\"container-fluid\">
    <!-- User Welcome Section -->
    <section class=\"py-5\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">
                        Hello ";
        // line 13
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 13, $this->source); })()), "firstName", [], "any", false, false, false, 13), "html", null, true);
        yield " ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 13, $this->source); })()), "lastName", [], "any", false, false, false, 13), "html", null, true);
        yield "!
                    </h1>
                    <p class=\"lead mb-4\">
                        Welcome back to Capitol Academy. Continue your trading journey with our professional courses.
                    </p>
                    <div class=\"d-flex flex-wrap gap-3\">
                        <a href=\"";
        // line 19
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-light btn-lg\">
                            <i class=\"fas fa-graduation-cap me-2\"></i>
                            Browse Courses
                        </a>
                        <a href=\"";
        // line 23
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_profile");
        yield "\" class=\"btn btn-outline-light btn-lg\">
                            <i class=\"fas fa-user me-2\"></i>
                            My Profile
                        </a>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <div class=\"user-avatar-large\">
                        <img src=\"https://via.placeholder.com/200x200/dc3545/ffffff?text=";
        // line 31
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 31, $this->source); })()), "firstName", [], "any", false, false, false, 31)), "html", null, true);
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 31, $this->source); })()), "lastName", [], "any", false, false, false, 31)), "html", null, true);
        yield "\" 
                             alt=\"User Avatar\" 
                             class=\"rounded-circle img-fluid shadow-lg\"
                             style=\"max-width: 200px; border: 5px solid rgba(255,255,255,0.3);\">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- User Dashboard Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- User Information Card -->
                <div class=\"col-lg-4 mb-4\">
                    <div class=\"card h-100 shadow-sm\">
                        <div class=\"card-header bg-primary text-white\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-user me-2\"></i>
                                Your Information
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"mb-3\">
                                <strong>Name:</strong><br>
                                ";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 57, $this->source); })()), "fullName", [], "any", false, false, false, 57), "html", null, true);
        yield "
                            </div>
                            <div class=\"mb-3\">
                                <strong>Email:</strong><br>
                                <a href=\"mailto:";
        // line 61
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 61, $this->source); })()), "email", [], "any", false, false, false, 61), "html", null, true);
        yield "\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 61, $this->source); })()), "email", [], "any", false, false, false, 61), "html", null, true);
        yield "</a>
                            </div>
                            ";
        // line 63
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 63, $this->source); })()), "country", [], "any", false, false, false, 63)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 64
            yield "                            <div class=\"mb-3\">
                                <strong>Country:</strong><br>
                                <span class=\"badge bg-info\">";
            // line 66
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 66, $this->source); })()), "country", [], "any", false, false, false, 66), "html", null, true);
            yield "</span>
                            </div>
                            ";
        }
        // line 69
        yield "                            ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 69, $this->source); })()), "phone", [], "any", false, false, false, 69)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 70
            yield "                            <div class=\"mb-3\">
                                <strong>Phone:</strong><br>
                                <a href=\"tel:";
            // line 72
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 72, $this->source); })()), "phone", [], "any", false, false, false, 72), "html", null, true);
            yield "\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 72, $this->source); })()), "phone", [], "any", false, false, false, 72), "html", null, true);
            yield "</a>
                            </div>
                            ";
        }
        // line 75
        yield "                            <div class=\"mb-3\">
                                <strong>Member Since:</strong><br>
                                <small class=\"text-muted\">";
        // line 77
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 77, $this->source); })()), "createdAt", [], "any", false, false, false, 77), "F j, Y"), "html", null, true);
        yield "</small>
                            </div>
                            <div class=\"mb-3\">
                                <strong>Account Status:</strong><br>
                                ";
        // line 81
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 81, $this->source); })()), "isVerified", [], "any", false, false, false, 81)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 82
            yield "                                    <span class=\"badge bg-success\">Verified</span>
                                ";
        } else {
            // line 84
            yield "                                    <span class=\"badge bg-warning\">Pending Verification</span>
                                ";
        }
        // line 86
        yield "                            </div>
                        </div>
                        <div class=\"card-footer\">
                            <a href=\"";
        // line 89
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_profile");
        yield "\" class=\"btn btn-outline-primary w-100\">
                                <i class=\"fas fa-edit me-2\"></i>
                                Edit Profile
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class=\"col-lg-4 mb-4\">
                    <div class=\"card h-100 shadow-sm\">
                        <div class=\"card-header bg-success text-white\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-bolt me-2\"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"d-grid gap-3\">
                                <a href=\"";
        // line 108
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>
                                    Browse All Courses
                                </a>
                                <a href=\"";
        // line 112
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" class=\"btn btn-outline-info\">
                                    <i class=\"fas fa-envelope me-2\"></i>
                                    Contact Support
                                </a>
                                <a href=\"";
        // line 116
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_about");
        yield "\" class=\"btn btn-outline-secondary\">
                                    <i class=\"fas fa-info-circle me-2\"></i>
                                    About Capitol Academy
                                </a>
                                <a href=\"";
        // line 120
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_logout");
        yield "\" class=\"btn btn-outline-danger\">
                                    <i class=\"fas fa-sign-out-alt me-2\"></i>
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Card -->
                <div class=\"col-lg-4 mb-4\">
                    <div class=\"card h-100 shadow-sm\">
                        <div class=\"card-header bg-warning text-dark\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-clock me-2\"></i>
                                Recent Activity
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"timeline\">
                                <div class=\"timeline-item\">
                                    <div class=\"timeline-marker bg-success\"></div>
                                    <div class=\"timeline-content\">
                                        <h6 class=\"timeline-title\">Account Created</h6>
                                        <p class=\"timeline-text\">
                                            You joined Capitol Academy
                                        </p>
                                        <small class=\"text-muted\">";
        // line 147
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 147, $this->source); })()), "createdAt", [], "any", false, false, false, 147), "M d, Y"), "html", null, true);
        yield "</small>
                                    </div>
                                </div>
                                ";
        // line 150
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 150, $this->source); })()), "isVerified", [], "any", false, false, false, 150)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 151
            yield "                                <div class=\"timeline-item\">
                                    <div class=\"timeline-marker bg-info\"></div>
                                    <div class=\"timeline-content\">
                                        <h6 class=\"timeline-title\">Account Verified</h6>
                                        <p class=\"timeline-text\">
                                            Your account was verified
                                        </p>
                                        <small class=\"text-muted\">";
            // line 158
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["user"]) || array_key_exists("user", $context) ? $context["user"] : (function () { throw new RuntimeError('Variable "user" does not exist.', 158, $this->source); })()), "updatedAt", [], "any", false, false, false, 158), "M d, Y"), "html", null, true);
            yield "</small>
                                    </div>
                                </div>
                                ";
        }
        // line 162
        yield "                                <div class=\"timeline-item\">
                                    <div class=\"timeline-marker bg-primary\"></div>
                                    <div class=\"timeline-content\">
                                        <h6 class=\"timeline-title\">Last Login</h6>
                                        <p class=\"timeline-text\">
                                            You logged in to your account
                                        </p>
                                        <small class=\"text-muted\">Today</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Courses Section -->
    <section class=\"py-5 bg-light\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <h2 class=\"text-center mb-5\">Continue Your Trading Journey</h2>
                    <div class=\"text-center\">
                        <a href=\"";
        // line 187
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-primary btn-lg\">
                            <i class=\"fas fa-graduation-cap me-2\"></i>
                            Explore Our Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.85rem;
    margin-bottom: 5px;
    color: #6c757d;
}

.user-avatar-large img {
    transition: transform 0.3s ease;
}

.user-avatar-large img:hover {
    transform: scale(1.05);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "user/home.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  359 => 187,  332 => 162,  325 => 158,  316 => 151,  314 => 150,  308 => 147,  278 => 120,  271 => 116,  264 => 112,  257 => 108,  235 => 89,  230 => 86,  226 => 84,  222 => 82,  220 => 81,  213 => 77,  209 => 75,  201 => 72,  197 => 70,  194 => 69,  188 => 66,  184 => 64,  182 => 63,  175 => 61,  168 => 57,  138 => 31,  127 => 23,  120 => 19,  109 => 13,  100 => 6,  87 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Welcome - Capitol Academy{% endblock %}

{% block body %}
<div class=\"container-fluid\">
    <!-- User Welcome Section -->
    <section class=\"py-5\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">
                        Hello {{ user.firstName }} {{ user.lastName }}!
                    </h1>
                    <p class=\"lead mb-4\">
                        Welcome back to Capitol Academy. Continue your trading journey with our professional courses.
                    </p>
                    <div class=\"d-flex flex-wrap gap-3\">
                        <a href=\"{{ path('app_courses') }}\" class=\"btn btn-light btn-lg\">
                            <i class=\"fas fa-graduation-cap me-2\"></i>
                            Browse Courses
                        </a>
                        <a href=\"{{ path('app_user_profile') }}\" class=\"btn btn-outline-light btn-lg\">
                            <i class=\"fas fa-user me-2\"></i>
                            My Profile
                        </a>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <div class=\"user-avatar-large\">
                        <img src=\"https://via.placeholder.com/200x200/dc3545/ffffff?text={{ user.firstName|first }}{{ user.lastName|first }}\" 
                             alt=\"User Avatar\" 
                             class=\"rounded-circle img-fluid shadow-lg\"
                             style=\"max-width: 200px; border: 5px solid rgba(255,255,255,0.3);\">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- User Dashboard Content -->
    <section class=\"py-5\">
        <div class=\"container\">
            <div class=\"row\">
                <!-- User Information Card -->
                <div class=\"col-lg-4 mb-4\">
                    <div class=\"card h-100 shadow-sm\">
                        <div class=\"card-header bg-primary text-white\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-user me-2\"></i>
                                Your Information
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"mb-3\">
                                <strong>Name:</strong><br>
                                {{ user.fullName }}
                            </div>
                            <div class=\"mb-3\">
                                <strong>Email:</strong><br>
                                <a href=\"mailto:{{ user.email }}\">{{ user.email }}</a>
                            </div>
                            {% if user.country %}
                            <div class=\"mb-3\">
                                <strong>Country:</strong><br>
                                <span class=\"badge bg-info\">{{ user.country }}</span>
                            </div>
                            {% endif %}
                            {% if user.phone %}
                            <div class=\"mb-3\">
                                <strong>Phone:</strong><br>
                                <a href=\"tel:{{ user.phone }}\">{{ user.phone }}</a>
                            </div>
                            {% endif %}
                            <div class=\"mb-3\">
                                <strong>Member Since:</strong><br>
                                <small class=\"text-muted\">{{ user.createdAt|date('F j, Y') }}</small>
                            </div>
                            <div class=\"mb-3\">
                                <strong>Account Status:</strong><br>
                                {% if user.isVerified %}
                                    <span class=\"badge bg-success\">Verified</span>
                                {% else %}
                                    <span class=\"badge bg-warning\">Pending Verification</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class=\"card-footer\">
                            <a href=\"{{ path('app_user_profile') }}\" class=\"btn btn-outline-primary w-100\">
                                <i class=\"fas fa-edit me-2\"></i>
                                Edit Profile
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class=\"col-lg-4 mb-4\">
                    <div class=\"card h-100 shadow-sm\">
                        <div class=\"card-header bg-success text-white\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-bolt me-2\"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"d-grid gap-3\">
                                <a href=\"{{ path('app_courses') }}\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>
                                    Browse All Courses
                                </a>
                                <a href=\"{{ path('app_contact') }}\" class=\"btn btn-outline-info\">
                                    <i class=\"fas fa-envelope me-2\"></i>
                                    Contact Support
                                </a>
                                <a href=\"{{ path('app_about') }}\" class=\"btn btn-outline-secondary\">
                                    <i class=\"fas fa-info-circle me-2\"></i>
                                    About Capitol Academy
                                </a>
                                <a href=\"{{ path('app_logout') }}\" class=\"btn btn-outline-danger\">
                                    <i class=\"fas fa-sign-out-alt me-2\"></i>
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Card -->
                <div class=\"col-lg-4 mb-4\">
                    <div class=\"card h-100 shadow-sm\">
                        <div class=\"card-header bg-warning text-dark\">
                            <h5 class=\"card-title mb-0\">
                                <i class=\"fas fa-clock me-2\"></i>
                                Recent Activity
                            </h5>
                        </div>
                        <div class=\"card-body\">
                            <div class=\"timeline\">
                                <div class=\"timeline-item\">
                                    <div class=\"timeline-marker bg-success\"></div>
                                    <div class=\"timeline-content\">
                                        <h6 class=\"timeline-title\">Account Created</h6>
                                        <p class=\"timeline-text\">
                                            You joined Capitol Academy
                                        </p>
                                        <small class=\"text-muted\">{{ user.createdAt|date('M d, Y') }}</small>
                                    </div>
                                </div>
                                {% if user.isVerified %}
                                <div class=\"timeline-item\">
                                    <div class=\"timeline-marker bg-info\"></div>
                                    <div class=\"timeline-content\">
                                        <h6 class=\"timeline-title\">Account Verified</h6>
                                        <p class=\"timeline-text\">
                                            Your account was verified
                                        </p>
                                        <small class=\"text-muted\">{{ user.updatedAt|date('M d, Y') }}</small>
                                    </div>
                                </div>
                                {% endif %}
                                <div class=\"timeline-item\">
                                    <div class=\"timeline-marker bg-primary\"></div>
                                    <div class=\"timeline-content\">
                                        <h6 class=\"timeline-title\">Last Login</h6>
                                        <p class=\"timeline-text\">
                                            You logged in to your account
                                        </p>
                                        <small class=\"text-muted\">Today</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Courses Section -->
    <section class=\"py-5 bg-light\">
        <div class=\"container\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <h2 class=\"text-center mb-5\">Continue Your Trading Journey</h2>
                    <div class=\"text-center\">
                        <a href=\"{{ path('app_courses') }}\" class=\"btn btn-primary btn-lg\">
                            <i class=\"fas fa-graduation-cap me-2\"></i>
                            Explore Our Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.85rem;
    margin-bottom: 5px;
    color: #6c757d;
}

.user-avatar-large img {
    transition: transform 0.3s ease;
}

.user-avatar-large img:hover {
    transform: scale(1.05);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}
</style>
{% endblock %}
", "user/home.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\user\\home.html.twig");
    }
}
