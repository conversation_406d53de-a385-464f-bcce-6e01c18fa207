<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/preview.html.twig */
class __TwigTemplate_1d26feb97aa7245c285189a0c3404e5e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/preview.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/preview.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Onsite Course Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Onsite Course Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Onsite Course Details: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 37, $this->source); })()), "code", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Onsite Course Button -->
                        <a href=\"";
        // line 43
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 43, $this->source); })()), "code", [], "any", false, false, false, 43)]), "html", null, true);
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #007bff; color: white; border: 2px solid #007bff; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#0056b3';\"
                           onmouseout=\"this.style.background='#007bff';\">
                            <i class=\"fas fa-edit me-2\"></i>
                            Edit
                        </a>

                        <!-- Toggle Status Button -->
                        <button type=\"button\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: ";
        // line 55
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 55, $this->source); })()), "isActive", [], "any", false, false, false, 55)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"));
        yield "; color: white; border: 2px solid ";
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 55, $this->source); })()), "isActive", [], "any", false, false, false, 55)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"));
        yield "; transition: all 0.3s ease;\"
                                onclick=\"toggleOnsiteCourseStatus(";
        // line 56
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 56, $this->source); })()), "id", [], "any", false, false, false, 56), "html", null, true);
        yield ", '";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 56, $this->source); })()), "title", [], "any", false, false, false, 56), "html", null, true);
        yield "', ";
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 56, $this->source); })()), "isActive", [], "any", false, false, false, 56)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("true") : ("false"));
        yield ")\">
                            <i class=\"fas fa-";
        // line 57
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 57, $this->source); })()), "isActive", [], "any", false, false, false, 57)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"));
        yield " me-2\"></i>
                            ";
        // line 58
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 58, $this->source); })()), "isActive", [], "any", false, false, false, 58)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"));
        yield "
                        </button>

                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 62
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <!-- Left Column - Course Information -->
                <div class=\"col-lg-8\">
                    <div class=\"row\">
                        <!-- Basic Information -->
                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-info-circle me-2\"></i>Basic Information
                            </h4>
                            <div class=\"table-responsive\">
                                <table class=\"table table-borderless\">
                                    <tr>
                                        <td class=\"fw-bold\" style=\"width: 150px;\">Course Code:</td>
                                        <td><span class=\"badge bg-primary fs-6\">";
        // line 89
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 89, $this->source); })()), "code", [], "any", false, false, false, 89), "html", null, true);
        yield "</span></td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Title:</td>
                                        <td class=\"fs-5 fw-semibold\">";
        // line 93
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 93, $this->source); })()), "title", [], "any", false, false, false, 93), "html", null, true);
        yield "</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Category:</td>
                                        <td>";
        // line 97
        yield ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 97, $this->source); })()), "category", [], "any", false, false, false, 97)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 97, $this->source); })()), "category", [], "any", false, false, false, 97), "html", null, true)) : ("<span class=\"text-muted\">Not specified</span>"));
        yield "</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Level:</td>
                                        <td>";
        // line 101
        yield ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 101, $this->source); })()), "level", [], "any", false, false, false, 101)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 101, $this->source); })()), "level", [], "any", false, false, false, 101), "html", null, true)) : ("<span class=\"text-muted\">Not specified</span>"));
        yield "</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Duration:</td>
                                        <td>";
        // line 105
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 105, $this->source); })()), "duration", [], "any", false, false, false, 105)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 105, $this->source); })()), "duration", [], "any", false, false, false, 105) . " minutes"), "html", null, true)) : ("<span class=\"text-muted\">Not specified</span>"));
        yield "</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Price:</td>
                                        <td class=\"fs-5 text-success fw-bold\">\$";
        // line 109
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 109, $this->source); })()), "price", [], "any", false, false, false, 109), "html", null, true);
        yield "</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Status:</td>
                                        <td>
                                            ";
        // line 114
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 114, $this->source); })()), "isActive", [], "any", false, false, false, 114)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 115
            yield "                                                <span class=\"badge bg-success fs-6\">Active</span>
                                            ";
        } else {
            // line 117
            yield "                                                <span class=\"badge bg-secondary fs-6\">Inactive</span>
                                            ";
        }
        // line 119
        yield "                                        </td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Modules:</td>
                                        <td>
                                            ";
        // line 124
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 124, $this->source); })()), "hasModules", [], "any", false, false, false, 124)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 125
            yield "                                                <span class=\"badge bg-info fs-6\">Enabled</span>
                                            ";
        } else {
            // line 127
            yield "                                                <span class=\"badge bg-secondary fs-6\">Disabled</span>
                                            ";
        }
        // line 129
        yield "                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Description -->
                        ";
        // line 136
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 136, $this->source); })()), "description", [], "any", false, false, false, 136)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 137
            yield "                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-align-left me-2\"></i>Description
                            </h4>
                            <div class=\"p-3 bg-light rounded\">
                                ";
            // line 142
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 142, $this->source); })()), "description", [], "any", false, false, false, 142), "html", null, true));
            yield "
                            </div>
                        </div>
                        ";
        }
        // line 146
        yield "
                        <!-- Learning Outcomes -->
                        ";
        // line 148
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 148, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 148) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 148, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 148)) > 0))) {
            // line 149
            yield "                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-bullseye me-2\"></i>Learning Outcomes
                            </h4>
                            <ul class=\"list-group list-group-flush\">
                                ";
            // line 154
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 154, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 154));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 155
                yield "                                    <li class=\"list-group-item d-flex align-items-start\">
                                        <i class=\"fas fa-check-circle text-success me-2 mt-1\"></i>
                                        ";
                // line 157
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "
                                    </li>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 160
            yield "                            </ul>
                        </div>
                        ";
        }
        // line 163
        yield "
                        <!-- Features -->
                        ";
        // line 165
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 165, $this->source); })()), "features", [], "any", false, false, false, 165) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 165, $this->source); })()), "features", [], "any", false, false, false, 165)) > 0))) {
            // line 166
            yield "                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-star me-2\"></i>Features
                            </h4>
                            <ul class=\"list-group list-group-flush\">
                                ";
            // line 171
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 171, $this->source); })()), "features", [], "any", false, false, false, 171));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 172
                yield "                                    <li class=\"list-group-item d-flex align-items-start\">
                                        <i class=\"fas fa-star text-warning me-2 mt-1\"></i>
                                        ";
                // line 174
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "
                                    </li>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 177
            yield "                            </ul>
                        </div>
                        ";
        }
        // line 180
        yield "                    </div>
                </div>

                <!-- Right Column - Images and Statistics -->
                <div class=\"col-lg-4\">
                    <!-- Thumbnail Image -->
                    ";
        // line 186
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 186, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 186)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 187
            yield "                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-image me-2\"></i>Thumbnail Image
                        </h5>
                        <img src=\"";
            // line 191
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 191, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 191), "html", null, true);
            yield "\" alt=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 191, $this->source); })()), "title", [], "any", false, false, false, 191), "html", null, true);
            yield "\" class=\"img-fluid rounded shadow\">
                    </div>
                    ";
        }
        // line 194
        yield "
                    <!-- Banner Image -->
                    ";
        // line 196
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 196, $this->source); })()), "bannerImage", [], "any", false, false, false, 196)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 197
            yield "                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-panorama me-2\"></i>Banner Image
                        </h5>
                        <img src=\"/uploads/courses/";
            // line 201
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 201, $this->source); })()), "bannerImage", [], "any", false, false, false, 201), "html", null, true);
            yield "\" alt=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 201, $this->source); })()), "title", [], "any", false, false, false, 201), "html", null, true);
            yield "\" class=\"img-fluid rounded shadow\">
                    </div>
                    ";
        }
        // line 204
        yield "
                    <!-- Statistics -->
                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-chart-bar me-2\"></i>Statistics
                        </h5>
                        <div class=\"card\">
                            <div class=\"card-body\">
                                <div class=\"row text-center\">
                                    <div class=\"col-6 mb-3\">
                                        <div class=\"text-primary fs-4 fw-bold\">";
        // line 214
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 214, $this->source); })()), "viewCount", [], "any", false, false, false, 214), "html", null, true);
        yield "</div>
                                        <small class=\"text-muted\">Views</small>
                                    </div>
                                    <div class=\"col-6 mb-3\">
                                        <div class=\"text-success fs-4 fw-bold\">";
        // line 218
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 218, $this->source); })()), "enrolledCount", [], "any", false, false, false, 218), "html", null, true);
        yield "</div>
                                        <small class=\"text-muted\">Enrolled</small>
                                    </div>
                                    <div class=\"col-6\">
                                        <div class=\"text-info fs-4 fw-bold\">";
        // line 222
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 222, $this->source); })()), "activeEnrollments", [], "any", false, false, false, 222), "html", null, true);
        yield "</div>
                                        <small class=\"text-muted\">Active</small>
                                    </div>
                                    <div class=\"col-6\">
                                        <div class=\"text-warning fs-4 fw-bold\">";
        // line 226
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 226, $this->source); })()), "completedCount", [], "any", false, false, false, 226), "html", null, true);
        yield "</div>
                                        <small class=\"text-muted\">Completed</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-clock me-2\"></i>Timestamps
                        </h5>
                        <div class=\"card\">
                            <div class=\"card-body\">
                                <p class=\"mb-2\">
                                    <strong>Created:</strong><br>
                                    <small class=\"text-muted\">";
        // line 243
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 243, $this->source); })()), "createdAt", [], "any", false, false, false, 243), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</small>
                                </p>
                                <p class=\"mb-0\">
                                    <strong>Last Updated:</strong><br>
                                    <small class=\"text-muted\">";
        // line 247
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 247, $this->source); })()), "updatedAt", [], "any", false, false, false, 247), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleOnsiteCourseStatus(courseId, courseTitle, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to \${action} the onsite course \"\${courseTitle}\"?`)) {
        fetch(`/admin/onsite-courses/\${courseId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                redirect_to: 'preview'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the onsite course status.');
        });
    }
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/preview.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  559 => 247,  552 => 243,  532 => 226,  525 => 222,  518 => 218,  511 => 214,  499 => 204,  491 => 201,  485 => 197,  483 => 196,  479 => 194,  471 => 191,  465 => 187,  463 => 186,  455 => 180,  450 => 177,  441 => 174,  437 => 172,  433 => 171,  426 => 166,  424 => 165,  420 => 163,  415 => 160,  406 => 157,  402 => 155,  398 => 154,  391 => 149,  389 => 148,  385 => 146,  378 => 142,  371 => 137,  369 => 136,  360 => 129,  356 => 127,  352 => 125,  350 => 124,  343 => 119,  339 => 117,  335 => 115,  333 => 114,  325 => 109,  318 => 105,  311 => 101,  304 => 97,  297 => 93,  290 => 89,  260 => 62,  253 => 58,  249 => 57,  241 => 56,  235 => 55,  220 => 43,  211 => 37,  201 => 29,  191 => 25,  188 => 24,  184 => 23,  181 => 22,  171 => 18,  168 => 17,  164 => 16,  160 => 14,  147 => 13,  134 => 10,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Onsite Course Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Onsite Course Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">{{ course.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Onsite Course Details: {{ course.code }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Onsite Course Button -->
                        <a href=\"{{ path('admin_onsite_course_edit', {'code': course.code}) }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #007bff; color: white; border: 2px solid #007bff; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#0056b3';\"
                           onmouseout=\"this.style.background='#007bff';\">
                            <i class=\"fas fa-edit me-2\"></i>
                            Edit
                        </a>

                        <!-- Toggle Status Button -->
                        <button type=\"button\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: {{ course.isActive ? '#6c757d' : '#28a745' }}; color: white; border: 2px solid {{ course.isActive ? '#6c757d' : '#28a745' }}; transition: all 0.3s ease;\"
                                onclick=\"toggleOnsiteCourseStatus({{ course.id }}, '{{ course.title }}', {{ course.isActive ? 'true' : 'false' }})\">
                            <i class=\"fas fa-{{ course.isActive ? 'pause' : 'play' }} me-2\"></i>
                            {{ course.isActive ? 'Deactivate' : 'Activate' }}
                        </button>

                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <!-- Left Column - Course Information -->
                <div class=\"col-lg-8\">
                    <div class=\"row\">
                        <!-- Basic Information -->
                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-info-circle me-2\"></i>Basic Information
                            </h4>
                            <div class=\"table-responsive\">
                                <table class=\"table table-borderless\">
                                    <tr>
                                        <td class=\"fw-bold\" style=\"width: 150px;\">Course Code:</td>
                                        <td><span class=\"badge bg-primary fs-6\">{{ course.code }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Title:</td>
                                        <td class=\"fs-5 fw-semibold\">{{ course.title }}</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Category:</td>
                                        <td>{{ course.category ?: '<span class=\"text-muted\">Not specified</span>' }}</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Level:</td>
                                        <td>{{ course.level ?: '<span class=\"text-muted\">Not specified</span>' }}</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Duration:</td>
                                        <td>{{ course.duration ? (course.duration ~ ' minutes') : '<span class=\"text-muted\">Not specified</span>' }}</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Price:</td>
                                        <td class=\"fs-5 text-success fw-bold\">\${{ course.price }}</td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Status:</td>
                                        <td>
                                            {% if course.isActive %}
                                                <span class=\"badge bg-success fs-6\">Active</span>
                                            {% else %}
                                                <span class=\"badge bg-secondary fs-6\">Inactive</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class=\"fw-bold\">Modules:</td>
                                        <td>
                                            {% if course.hasModules %}
                                                <span class=\"badge bg-info fs-6\">Enabled</span>
                                            {% else %}
                                                <span class=\"badge bg-secondary fs-6\">Disabled</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Description -->
                        {% if course.description %}
                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-align-left me-2\"></i>Description
                            </h4>
                            <div class=\"p-3 bg-light rounded\">
                                {{ course.description|nl2br }}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Learning Outcomes -->
                        {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-bullseye me-2\"></i>Learning Outcomes
                            </h4>
                            <ul class=\"list-group list-group-flush\">
                                {% for outcome in course.learningOutcomes %}
                                    <li class=\"list-group-item d-flex align-items-start\">
                                        <i class=\"fas fa-check-circle text-success me-2 mt-1\"></i>
                                        {{ outcome }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- Features -->
                        {% if course.features and course.features|length > 0 %}
                        <div class=\"col-12 mb-4\">
                            <h4 class=\"text-primary mb-3\">
                                <i class=\"fas fa-star me-2\"></i>Features
                            </h4>
                            <ul class=\"list-group list-group-flush\">
                                {% for feature in course.features %}
                                    <li class=\"list-group-item d-flex align-items-start\">
                                        <i class=\"fas fa-star text-warning me-2 mt-1\"></i>
                                        {{ feature }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Right Column - Images and Statistics -->
                <div class=\"col-lg-4\">
                    <!-- Thumbnail Image -->
                    {% if course.thumbnailImage %}
                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-image me-2\"></i>Thumbnail Image
                        </h5>
                        <img src=\"{{ course.thumbnailUrl }}\" alt=\"{{ course.title }}\" class=\"img-fluid rounded shadow\">
                    </div>
                    {% endif %}

                    <!-- Banner Image -->
                    {% if course.bannerImage %}
                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-panorama me-2\"></i>Banner Image
                        </h5>
                        <img src=\"/uploads/courses/{{ course.bannerImage }}\" alt=\"{{ course.title }}\" class=\"img-fluid rounded shadow\">
                    </div>
                    {% endif %}

                    <!-- Statistics -->
                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-chart-bar me-2\"></i>Statistics
                        </h5>
                        <div class=\"card\">
                            <div class=\"card-body\">
                                <div class=\"row text-center\">
                                    <div class=\"col-6 mb-3\">
                                        <div class=\"text-primary fs-4 fw-bold\">{{ course.viewCount }}</div>
                                        <small class=\"text-muted\">Views</small>
                                    </div>
                                    <div class=\"col-6 mb-3\">
                                        <div class=\"text-success fs-4 fw-bold\">{{ course.enrolledCount }}</div>
                                        <small class=\"text-muted\">Enrolled</small>
                                    </div>
                                    <div class=\"col-6\">
                                        <div class=\"text-info fs-4 fw-bold\">{{ course.activeEnrollments }}</div>
                                        <small class=\"text-muted\">Active</small>
                                    </div>
                                    <div class=\"col-6\">
                                        <div class=\"text-warning fs-4 fw-bold\">{{ course.completedCount }}</div>
                                        <small class=\"text-muted\">Completed</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class=\"mb-4\">
                        <h5 class=\"text-primary mb-3\">
                            <i class=\"fas fa-clock me-2\"></i>Timestamps
                        </h5>
                        <div class=\"card\">
                            <div class=\"card-body\">
                                <p class=\"mb-2\">
                                    <strong>Created:</strong><br>
                                    <small class=\"text-muted\">{{ course.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}</small>
                                </p>
                                <p class=\"mb-0\">
                                    <strong>Last Updated:</strong><br>
                                    <small class=\"text-muted\">{{ course.updatedAt|date('F j, Y \\\\a\\\\t g:i A') }}</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleOnsiteCourseStatus(courseId, courseTitle, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    if (confirm(`Are you sure you want to \${action} the onsite course \"\${courseTitle}\"?`)) {
        fetch(`/admin/onsite-courses/\${courseId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                redirect_to: 'preview'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the onsite course status.');
        });
    }
}
</script>
{% endblock %}
", "admin/onsite_courses/preview.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\preview.html.twig");
    }
}
