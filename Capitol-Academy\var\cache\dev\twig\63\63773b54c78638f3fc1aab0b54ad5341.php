<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/create.html.twig */
class __TwigTemplate_ac43359ba18ef43c44d76997ebf51848 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/create.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/create.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Onsite Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create New Onsite Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Create Onsite Course</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Onsite Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"onsite-course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("onsite_course_create"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Code <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"code\"
                                           name=\"code\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Title <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"title\"
                                           name=\"title\"
                                           placeholder=\"Enter onsite course title\"
                                           required
                                           maxlength=\"255\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group mb-3\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Description
                            </label>
                            <textarea class=\"form-control\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Enter onsite course description\"
                                      maxlength=\"2000\"></textarea>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Category <span class=\"text-danger\">*</span>
                                    </label>
                                    <select class=\"form-select\" id=\"category\" name=\"category\" required>
                                        <option value=\"\">Select category</option>
                                        ";
        // line 129
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 129, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 130
            yield "                                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 130), "html", null, true);
            yield "\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 130), "html", null, true);
            yield "</option>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 132
        yield "                                    </select>
                                    <div class=\"invalid-feedback\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Difficulty Level
                                    </label>
                                    <select class=\"form-select\" id=\"level\" name=\"level\">
                                        <option value=\"\">Select level</option>
                                        <option value=\"Beginner\">Beginner</option>
                                        <option value=\"Intermediate\">Intermediate</option>
                                        <option value=\"Advanced\">Advanced</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Duration and Price Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"duration\" class=\"form-label\">
                                        <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Duration (minutes)
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"duration\"
                                           name=\"duration\"
                                           placeholder=\"Enter duration in minutes\"
                                           min=\"0\">
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"price\" class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Price (USD) <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"price\"
                                           name=\"price\"
                                           placeholder=\"0.00\"
                                           step=\"0.01\"
                                           min=\"0\"
                                           required>
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid price.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Uploads Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"thumbnail_image\" class=\"form-label\">
                                        <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Thumbnail Image
                                    </label>
                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"thumbnail_image\"
                                           name=\"thumbnail_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a thumbnail image (300x200px recommended). Max size: 5MB</div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"banner_image\" class=\"form-label\">
                                        <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Banner Image
                                    </label>
                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"banner_image\"
                                           name=\"banner_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a banner image (1200x400px recommended). Max size: 10MB</div>
                                </div>
                            </div>
                        </div>

                        <!-- Modules Checkbox -->
                        <div class=\"form-group mb-3\">
                            <div class=\"form-check\">
                                <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\">
                                <label class=\"form-check-label\" for=\"has_modules\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Enable Modules
                                </label>
                                <div class=\"form-text\">Check this to enable modular structure for this onsite course</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Footer -->
            <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"";
        // line 244
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\" class=\"btn btn-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <button type=\"submit\" class=\"btn btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; padding: 0.75rem 2rem;\">
                        <i class=\"fas fa-save me-2\"></i>Create Onsite Course
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/create.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  435 => 244,  321 => 132,  310 => 130,  306 => 129,  231 => 57,  214 => 43,  198 => 29,  188 => 25,  185 => 24,  181 => 23,  178 => 22,  168 => 18,  165 => 17,  161 => 16,  157 => 14,  144 => 13,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Onsite Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Onsite Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Create Onsite Course</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Onsite Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"onsite-course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('onsite_course_create') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Code <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"code\"
                                           name=\"code\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Onsite Course Title <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control\"
                                           id=\"title\"
                                           name=\"title\"
                                           placeholder=\"Enter onsite course title\"
                                           required
                                           maxlength=\"255\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid onsite course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group mb-3\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Description
                            </label>
                            <textarea class=\"form-control\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Enter onsite course description\"
                                      maxlength=\"2000\"></textarea>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Category <span class=\"text-danger\">*</span>
                                    </label>
                                    <select class=\"form-select\" id=\"category\" name=\"category\" required>
                                        <option value=\"\">Select category</option>
                                        {% for category in categories %}
                                            <option value=\"{{ category.name }}\">{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class=\"invalid-feedback\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Difficulty Level
                                    </label>
                                    <select class=\"form-select\" id=\"level\" name=\"level\">
                                        <option value=\"\">Select level</option>
                                        <option value=\"Beginner\">Beginner</option>
                                        <option value=\"Intermediate\">Intermediate</option>
                                        <option value=\"Advanced\">Advanced</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Duration and Price Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"duration\" class=\"form-label\">
                                        <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Duration (minutes)
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"duration\"
                                           name=\"duration\"
                                           placeholder=\"Enter duration in minutes\"
                                           min=\"0\">
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"price\" class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Price (USD) <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"number\"
                                           class=\"form-control\"
                                           id=\"price\"
                                           name=\"price\"
                                           placeholder=\"0.00\"
                                           step=\"0.01\"
                                           min=\"0\"
                                           required>
                                    <div class=\"invalid-feedback\">
                                        Please provide a valid price.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Uploads Row -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"thumbnail_image\" class=\"form-label\">
                                        <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Thumbnail Image
                                    </label>
                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"thumbnail_image\"
                                           name=\"thumbnail_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a thumbnail image (300x200px recommended). Max size: 5MB</div>
                                </div>
                            </div>

                            <div class=\"col-md-6\">
                                <div class=\"form-group mb-3\">
                                    <label for=\"banner_image\" class=\"form-label\">
                                        <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        Banner Image
                                    </label>
                                    <input type=\"file\"
                                           class=\"form-control\"
                                           id=\"banner_image\"
                                           name=\"banner_image\"
                                           accept=\"image/*\">
                                    <div class=\"form-text\">Upload a banner image (1200x400px recommended). Max size: 10MB</div>
                                </div>
                            </div>
                        </div>

                        <!-- Modules Checkbox -->
                        <div class=\"form-group mb-3\">
                            <div class=\"form-check\">
                                <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\">
                                <label class=\"form-check-label\" for=\"has_modules\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Enable Modules
                                </label>
                                <div class=\"form-text\">Check this to enable modular structure for this onsite course</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Footer -->
            <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"{{ path('admin_onsite_courses') }}\" class=\"btn btn-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <button type=\"submit\" class=\"btn btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; padding: 0.75rem 2rem;\">
                        <i class=\"fas fa-save me-2\"></i>Create Onsite Course
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
", "admin/onsite_courses/create.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\create.html.twig");
    }
}
