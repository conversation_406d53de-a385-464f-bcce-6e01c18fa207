<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/partners/show.html.twig */
class __TwigTemplate_2d1ce62d68fbb85e3331784891571175 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/show.html.twig"));

        // line 1
        yield from $this->load("admin/partners/show.html.twig", 1, "757052144")->unwrap()->yield(CoreExtension::merge($context, ["entity_name" => "Partner", "entity_title" => CoreExtension::getAttribute($this->env, $this->source,         // line 3
(isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 3, $this->source); })()), "name", [], "any", false, false, false, 3), "entity_code" => CoreExtension::getAttribute($this->env, $this->source,         // line 4
(isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 4, $this->source); })()), "name", [], "any", false, false, false, 4), "entity_icon" => "fas fa-handshake", "breadcrumb_items" => [["path" => "admin_dashboard", "title" => "Home"], ["path" => "admin_partners", "title" => "Partners"], ["title" => CoreExtension::getAttribute($this->env, $this->source,         // line 9
(isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 9, $this->source); })()), "name", [], "any", false, false, false, 9), "active" => true]], "edit_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_partner_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source,         // line 11
(isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 11, $this->source); })()), "id", [], "any", false, false, false, 11)]), "back_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_partners"), "print_function" => "printPartnerDetails"]));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/partners/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  52 => 11,  51 => 9,  50 => 4,  49 => 3,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Partner',
    'entity_title': partner.name,
    'entity_code': partner.name,
    'entity_icon': 'fas fa-handshake',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_partners', 'title': 'Partners'},
        {'title': partner.name, 'active': true}
    ],
    'edit_path': path('admin_partner_edit', {'id': partner.id}),
    'back_path': path('admin_partners'),
    'print_function': 'printPartnerDetails'
} %}

{% block preview_content %}
            <!-- Line 1: Partner name and website URL (same line) -->
            <div class=\"row print-two-column clearfix mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-handshake text-primary mr-1\"></i>
                            Partner Name
                        </label>
                        <div class=\"enhanced-display-field\">
                            {{ partner.name }}
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-globe text-primary mr-1\"></i>
                            Website URL
                        </label>
                        <div class=\"enhanced-display-field\">
                            {% if partner.websiteUrl %}
                                <a href=\"{{ partner.websiteUrl }}\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                                    {{ partner.websiteUrl }}
                                    <i class=\"fas fa-external-link-alt ml-1\" style=\"font-size: 0.8rem;\"></i>
                                </a>
                            {% else %}
                                <span class=\"text-muted\">No website URL provided</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 2: Display order and status (same line) -->
            <div class=\"row print-two-column clearfix mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                            Display Order
                        </label>
                        <div class=\"enhanced-display-field\">
                            {{ partner.displayOrder }}
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                            Status
                        </label>
                        <div class=\"enhanced-display-field\">
                            {% if partner.isActive %}
                                <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                    <i class=\"fas fa-check-circle mr-1\"></i>Active
                                </span>
                            {% else %}
                                <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                    <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 3: Description (full width) -->
            {% if partner.description %}
            <div class=\"form-group print-full-width mb-4\">
                <label class=\"form-label\">
                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                    Description
                </label>
                <div class=\"enhanced-display-field\" style=\"min-height: 100px; white-space: pre-wrap;\">
                    {{ partner.description }}
                </div>
            </div>
            {% endif %}

            <!-- Line 4: Logo (centered display) -->
            {% if partner.logoPath %}
            <div class=\"form-group print-full-width mb-4\">
                <label class=\"form-label\">
                    <i class=\"fas fa-image text-primary mr-1\"></i>
                    Partner Logo
                </label>
                <div class=\"logo-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 2rem;\">
                    <div class=\"logo-display text-center\">
                        <img src=\"{{ partner.logoUrl }}\" 
                             alt=\"{{ partner.name }}\" 
                             style=\"max-width: 400px; max-height: 250px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); object-fit: contain;\"
                             onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                    </div>
                </div>
            </div>
            {% endif %}
{% endblock %}

{% block print_body %}
    <!-- Print-specific content for partner details -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Partner Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Partner Name:</div>
                <div class=\"print-info-value\">{{ partner.name }}</div>
            </div>
            {% if partner.websiteUrl %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Website URL:</div>
                <div class=\"print-info-value\">{{ partner.websiteUrl }}</div>
            </div>
            {% endif %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Display Order:</div>
                <div class=\"print-info-value\">{{ partner.displayOrder }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Status:</div>
                <div class=\"print-info-value\">{{ partner.isActive ? 'Active' : 'Inactive' }}</div>
            </div>
            {% if partner.description %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Description:</div>
                <div class=\"print-info-value\">{{ partner.description }}</div>
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
{% endembed %}
", "admin/partners/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\partners\\show.html.twig");
    }
}


/* admin/partners/show.html.twig */
class __TwigTemplate_2d1ce62d68fbb85e3331784891571175___757052144 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'preview_content' => [$this, 'block_preview_content'],
            'print_body' => [$this, 'block_print_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "components/admin_preview_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/partners/show.html.twig"));

        $this->parent = $this->load("components/admin_preview_layout.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 16
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_preview_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        // line 17
        yield "            <!-- Line 1: Partner name and website URL (same line) -->
            <div class=\"row print-two-column clearfix mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-handshake text-primary mr-1\"></i>
                            Partner Name
                        </label>
                        <div class=\"enhanced-display-field\">
                            ";
        // line 26
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 26, $this->source); })()), "name", [], "any", false, false, false, 26), "html", null, true);
        yield "
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-globe text-primary mr-1\"></i>
                            Website URL
                        </label>
                        <div class=\"enhanced-display-field\">
                            ";
        // line 37
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 37, $this->source); })()), "websiteUrl", [], "any", false, false, false, 37)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 38
            yield "                                <a href=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 38, $this->source); })()), "websiteUrl", [], "any", false, false, false, 38), "html", null, true);
            yield "\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                                    ";
            // line 39
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 39, $this->source); })()), "websiteUrl", [], "any", false, false, false, 39), "html", null, true);
            yield "
                                    <i class=\"fas fa-external-link-alt ml-1\" style=\"font-size: 0.8rem;\"></i>
                                </a>
                            ";
        } else {
            // line 43
            yield "                                <span class=\"text-muted\">No website URL provided</span>
                            ";
        }
        // line 45
        yield "                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 2: Display order and status (same line) -->
            <div class=\"row print-two-column clearfix mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                            Display Order
                        </label>
                        <div class=\"enhanced-display-field\">
                            ";
        // line 59
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 59, $this->source); })()), "displayOrder", [], "any", false, false, false, 59), "html", null, true);
        yield "
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                            Status
                        </label>
                        <div class=\"enhanced-display-field\">
                            ";
        // line 70
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 70, $this->source); })()), "isActive", [], "any", false, false, false, 70)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 71
            yield "                                <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                    <i class=\"fas fa-check-circle mr-1\"></i>Active
                                </span>
                            ";
        } else {
            // line 75
            yield "                                <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                    <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                </span>
                            ";
        }
        // line 79
        yield "                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 3: Description (full width) -->
            ";
        // line 85
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 85, $this->source); })()), "description", [], "any", false, false, false, 85)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 86
            yield "            <div class=\"form-group print-full-width mb-4\">
                <label class=\"form-label\">
                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                    Description
                </label>
                <div class=\"enhanced-display-field\" style=\"min-height: 100px; white-space: pre-wrap;\">
                    ";
            // line 92
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 92, $this->source); })()), "description", [], "any", false, false, false, 92), "html", null, true);
            yield "
                </div>
            </div>
            ";
        }
        // line 96
        yield "
            <!-- Line 4: Logo (centered display) -->
            ";
        // line 98
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 98, $this->source); })()), "logoPath", [], "any", false, false, false, 98)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 99
            yield "            <div class=\"form-group print-full-width mb-4\">
                <label class=\"form-label\">
                    <i class=\"fas fa-image text-primary mr-1\"></i>
                    Partner Logo
                </label>
                <div class=\"logo-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 2rem;\">
                    <div class=\"logo-display text-center\">
                        <img src=\"";
            // line 106
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 106, $this->source); })()), "logoUrl", [], "any", false, false, false, 106), "html", null, true);
            yield "\" 
                             alt=\"";
            // line 107
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 107, $this->source); })()), "name", [], "any", false, false, false, 107), "html", null, true);
            yield "\" 
                             style=\"max-width: 400px; max-height: 250px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); object-fit: contain;\"
                             onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                    </div>
                </div>
            </div>
            ";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 116
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_print_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        // line 117
        yield "    <!-- Print-specific content for partner details -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Partner Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Partner Name:</div>
                <div class=\"print-info-value\">";
        // line 123
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 123, $this->source); })()), "name", [], "any", false, false, false, 123), "html", null, true);
        yield "</div>
            </div>
            ";
        // line 125
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 125, $this->source); })()), "websiteUrl", [], "any", false, false, false, 125)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 126
            yield "            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Website URL:</div>
                <div class=\"print-info-value\">";
            // line 128
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 128, $this->source); })()), "websiteUrl", [], "any", false, false, false, 128), "html", null, true);
            yield "</div>
            </div>
            ";
        }
        // line 131
        yield "            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Display Order:</div>
                <div class=\"print-info-value\">";
        // line 133
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 133, $this->source); })()), "displayOrder", [], "any", false, false, false, 133), "html", null, true);
        yield "</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Status:</div>
                <div class=\"print-info-value\">";
        // line 137
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 137, $this->source); })()), "isActive", [], "any", false, false, false, 137)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Active") : ("Inactive"));
        yield "</div>
            </div>
            ";
        // line 139
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 139, $this->source); })()), "description", [], "any", false, false, false, 139)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 140
            yield "            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Description:</div>
                <div class=\"print-info-value\">";
            // line 142
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["partner"]) || array_key_exists("partner", $context) ? $context["partner"] : (function () { throw new RuntimeError('Variable "partner" does not exist.', 142, $this->source); })()), "description", [], "any", false, false, false, 142), "html", null, true);
            yield "</div>
            </div>
            ";
        }
        // line 145
        yield "        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/partners/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  517 => 145,  511 => 142,  507 => 140,  505 => 139,  500 => 137,  493 => 133,  489 => 131,  483 => 128,  479 => 126,  477 => 125,  472 => 123,  464 => 117,  451 => 116,  432 => 107,  428 => 106,  419 => 99,  417 => 98,  413 => 96,  406 => 92,  398 => 86,  396 => 85,  388 => 79,  382 => 75,  376 => 71,  374 => 70,  360 => 59,  344 => 45,  340 => 43,  333 => 39,  328 => 38,  326 => 37,  312 => 26,  301 => 17,  288 => 16,  265 => 1,  52 => 11,  51 => 9,  50 => 4,  49 => 3,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Partner',
    'entity_title': partner.name,
    'entity_code': partner.name,
    'entity_icon': 'fas fa-handshake',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_partners', 'title': 'Partners'},
        {'title': partner.name, 'active': true}
    ],
    'edit_path': path('admin_partner_edit', {'id': partner.id}),
    'back_path': path('admin_partners'),
    'print_function': 'printPartnerDetails'
} %}

{% block preview_content %}
            <!-- Line 1: Partner name and website URL (same line) -->
            <div class=\"row print-two-column clearfix mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-handshake text-primary mr-1\"></i>
                            Partner Name
                        </label>
                        <div class=\"enhanced-display-field\">
                            {{ partner.name }}
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-globe text-primary mr-1\"></i>
                            Website URL
                        </label>
                        <div class=\"enhanced-display-field\">
                            {% if partner.websiteUrl %}
                                <a href=\"{{ partner.websiteUrl }}\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                                    {{ partner.websiteUrl }}
                                    <i class=\"fas fa-external-link-alt ml-1\" style=\"font-size: 0.8rem;\"></i>
                                </a>
                            {% else %}
                                <span class=\"text-muted\">No website URL provided</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 2: Display order and status (same line) -->
            <div class=\"row print-two-column clearfix mb-4\">
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                            Display Order
                        </label>
                        <div class=\"enhanced-display-field\">
                            {{ partner.displayOrder }}
                        </div>
                    </div>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                            Status
                        </label>
                        <div class=\"enhanced-display-field\">
                            {% if partner.isActive %}
                                <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                    <i class=\"fas fa-check-circle mr-1\"></i>Active
                                </span>
                            {% else %}
                                <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                    <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 3: Description (full width) -->
            {% if partner.description %}
            <div class=\"form-group print-full-width mb-4\">
                <label class=\"form-label\">
                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                    Description
                </label>
                <div class=\"enhanced-display-field\" style=\"min-height: 100px; white-space: pre-wrap;\">
                    {{ partner.description }}
                </div>
            </div>
            {% endif %}

            <!-- Line 4: Logo (centered display) -->
            {% if partner.logoPath %}
            <div class=\"form-group print-full-width mb-4\">
                <label class=\"form-label\">
                    <i class=\"fas fa-image text-primary mr-1\"></i>
                    Partner Logo
                </label>
                <div class=\"logo-container\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 2rem;\">
                    <div class=\"logo-display text-center\">
                        <img src=\"{{ partner.logoUrl }}\" 
                             alt=\"{{ partner.name }}\" 
                             style=\"max-width: 400px; max-height: 250px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); object-fit: contain;\"
                             onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                    </div>
                </div>
            </div>
            {% endif %}
{% endblock %}

{% block print_body %}
    <!-- Print-specific content for partner details -->
    <div class=\"print-section\">
        <div class=\"print-section-title\">Partner Information</div>
        <div class=\"print-info-grid\">
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Partner Name:</div>
                <div class=\"print-info-value\">{{ partner.name }}</div>
            </div>
            {% if partner.websiteUrl %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Website URL:</div>
                <div class=\"print-info-value\">{{ partner.websiteUrl }}</div>
            </div>
            {% endif %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Display Order:</div>
                <div class=\"print-info-value\">{{ partner.displayOrder }}</div>
            </div>
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Status:</div>
                <div class=\"print-info-value\">{{ partner.isActive ? 'Active' : 'Inactive' }}</div>
            </div>
            {% if partner.description %}
            <div class=\"print-info-row\">
                <div class=\"print-info-label\">Description:</div>
                <div class=\"print-info-value\">{{ partner.description }}</div>
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
{% endembed %}
", "admin/partners/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\partners\\show.html.twig");
    }
}
