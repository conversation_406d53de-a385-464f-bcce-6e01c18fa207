<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/profile/index.html.twig */
class __TwigTemplate_4d84ab4c6200d892413d91ff7341480e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/profile/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/profile/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Admin Profile - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Admin Profile";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Dashboard</a></li>
<li class=\"breadcrumb-item active\">Profile</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        yield "<div class=\"container-fluid p-0\">
    <!-- Professional Profile Header -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border-radius: 8px;\">
                <div class=\"card-body p-4\">
                    <div class=\"d-flex align-items-center\">
                        <div class=\"flex-shrink-0 me-4\">
                            <img src=\"";
        // line 21
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 21, $this->source); })()), "profileImageUrl", [], "any", false, false, false, 21), "html", null, true);
        yield "\"
                                 alt=\"Admin Profile\"
                                 class=\"rounded-circle shadow-lg\"
                                 style=\"width: 100px; height: 100px; object-fit: cover; border: 4px solid rgba(255,255,255,0.3);\">
                        </div>
                        <div class=\"flex-grow-1\">
                            <h2 class=\"mb-2 font-weight-bold\">";
        // line 27
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 27, $this->source); })()), "fullName", [], "any", false, false, false, 27), "html", null, true);
        yield "</h2>
                            <p class=\"mb-1\" style=\"font-size: 1.1rem; opacity: 0.9;\">
                                <i class=\"fas fa-envelope me-2\"></i>";
        // line 29
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 29, $this->source); })()), "email", [], "any", false, false, false, 29), "html", null, true);
        yield "
                            </p>
                            <p class=\"mb-0\" style=\"opacity: 0.8;\">
                                <i class=\"fas fa-user-shield me-2\"></i>Capitol Academy Administrator
                            </p>
                        </div>
                        <div class=\"flex-shrink-0\">
                            <button type=\"button\" class=\"btn btn-light\" onclick=\"scrollToSettings()\">
                                <i class=\"fas fa-cog me-2\"></i>Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Personal Information Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-user-circle me-2 text-primary\"></i>
                        Personal Information
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"row g-4\">
                        <div class=\"col-md-4\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Full Name</label>
                                <p class=\"h6 mb-0\">";
        // line 61
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 61, $this->source); })()), "fullName", [], "any", false, false, false, 61), "html", null, true);
        yield "</p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Username</label>
                                <p class=\"h6 mb-0\">";
        // line 67
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 67, $this->source); })()), "username", [], "any", false, false, false, 67), "html", null, true);
        yield "</p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Email Address</label>
                                <p class=\"h6 mb-0\">";
        // line 73
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 73, $this->source); })()), "email", [], "any", false, false, false, 73), "html", null, true);
        yield "</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Status Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-shield-alt me-2 text-success\"></i>
                        Account Status
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"row g-4\">
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Status</label>
                                <p class=\"h6 mb-0\">
                                    ";
        // line 98
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 98, $this->source); })()), "isActive", [], "any", false, false, false, 98)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 99
            yield "                                        <span class=\"badge bg-success\">
                                            <i class=\"fas fa-check-circle me-1\"></i>Active
                                        </span>
                                    ";
        } else {
            // line 103
            yield "                                        <span class=\"badge bg-danger\">
                                            <i class=\"fas fa-times-circle me-1\"></i>Inactive
                                        </span>
                                    ";
        }
        // line 107
        yield "                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Account Created</label>
                                <p class=\"h6 mb-0\">";
        // line 113
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 113, $this->source); })()), "createdAt", [], "any", false, false, false, 113), "M j, Y"), "html", null, true);
        yield "</p>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Last Login</label>
                                <p class=\"h6 mb-0\">
                                    ";
        // line 120
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 120, $this->source); })()), "lastLoginAt", [], "any", false, false, false, 120)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 121
            yield "                                        ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 121, $this->source); })()), "lastLoginAt", [], "any", false, false, false, 121), "M j, Y g:i A"), "html", null, true);
            yield "
                                    ";
        } else {
            // line 123
            yield "                                        <span class=\"text-muted\">Never</span>
                                    ";
        }
        // line 125
        yield "                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">IP Address</label>
                                <p class=\"h6 mb-0\">
                                    ";
        // line 132
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 132, $this->source); })()), "ipAddress", [], "any", false, false, false, 132)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 133
            yield "                                        <code class=\"bg-light text-dark px-2 py-1 rounded\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 133, $this->source); })()), "ipAddress", [], "any", false, false, false, 133), "html", null, true);
            yield "</code>
                                    ";
        } else {
            // line 135
            yield "                                        <span class=\"text-muted\">Not recorded</span>
                                    ";
        }
        // line 137
        yield "                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Permissions Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-key me-2 text-warning\"></i>
                        Admin Permissions
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    ";
        // line 157
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 157, $this->source); })()), "isMasterAdmin", [], "any", false, false, false, 157)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 158
            yield "                        <div class=\"alert alert-info mb-4\">
                            <i class=\"fas fa-crown me-2\"></i>
                            <strong>Master Administrator:</strong> You have full access to all system features and settings.
                        </div>
                    ";
        }
        // line 163
        yield "                    
                    <div class=\"row g-4\">
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">Course Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    ";
        // line 169
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 169, $this->source); })()), "hasPermission", ["courses.read"], "method", false, false, false, 169)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 170
            yield "                                        <span class=\"badge bg-success\">View Courses</span>
                                    ";
        }
        // line 172
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 172, $this->source); })()), "hasPermission", ["courses.add"], "method", false, false, false, 172)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 173
            yield "                                        <span class=\"badge bg-success\">Add Courses</span>
                                    ";
        }
        // line 175
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 175, $this->source); })()), "hasPermission", ["courses.edit"], "method", false, false, false, 175)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 176
            yield "                                        <span class=\"badge bg-success\">Edit Courses</span>
                                    ";
        }
        // line 178
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 178, $this->source); })()), "hasPermission", ["courses.delete"], "method", false, false, false, 178)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 179
            yield "                                        <span class=\"badge bg-success\">Delete Courses</span>
                                    ";
        }
        // line 181
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">User Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    ";
        // line 188
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 188, $this->source); })()), "hasPermission", ["users.read"], "method", false, false, false, 188)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 189
            yield "                                        <span class=\"badge bg-info\">View Users</span>
                                    ";
        }
        // line 191
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 191, $this->source); })()), "hasPermission", ["users.edit"], "method", false, false, false, 191)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 192
            yield "                                        <span class=\"badge bg-info\">Edit Users</span>
                                    ";
        }
        // line 194
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 194, $this->source); })()), "hasPermission", ["users.delete"], "method", false, false, false, 194)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 195
            yield "                                        <span class=\"badge bg-info\">Delete Users</span>
                                    ";
        }
        // line 197
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">Contact Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    ";
        // line 204
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 204, $this->source); })()), "hasPermission", ["contacts.read"], "method", false, false, false, 204)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 205
            yield "                                        <span class=\"badge bg-warning\">View Contacts</span>
                                    ";
        }
        // line 207
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 207, $this->source); })()), "hasPermission", ["contacts.edit"], "method", false, false, false, 207)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 208
            yield "                                        <span class=\"badge bg-warning\">Edit Contacts</span>
                                    ";
        }
        // line 210
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 210, $this->source); })()), "hasPermission", ["contacts.delete"], "method", false, false, false, 210)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 211
            yield "                                        <span class=\"badge bg-warning\">Delete Contacts</span>
                                    ";
        }
        // line 213
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">Admin Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    ";
        // line 220
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 220, $this->source); })()), "hasPermission", ["admin.read"], "method", false, false, false, 220)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 221
            yield "                                        <span class=\"badge bg-danger\">View Admins</span>
                                    ";
        }
        // line 223
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 223, $this->source); })()), "hasPermission", ["admin.add"], "method", false, false, false, 223)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 224
            yield "                                        <span class=\"badge bg-danger\">Add Admins</span>
                                    ";
        }
        // line 226
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 226, $this->source); })()), "hasPermission", ["admin.edit"], "method", false, false, false, 226)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 227
            yield "                                        <span class=\"badge bg-danger\">Edit Admins</span>
                                    ";
        }
        // line 229
        yield "                                    ";
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 229, $this->source); })()), "hasPermission", ["admin.delete"], "method", false, false, false, 229)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 230
            yield "                                        <span class=\"badge bg-danger\">Delete Admins</span>
                                    ";
        }
        // line 232
        yield "                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-bolt me-2 text-primary\"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"d-flex flex-wrap gap-2\">
                        <button type=\"button\" class=\"btn btn-primary\" onclick=\"scrollToSettings()\">
                            <i class=\"fas fa-cog me-2\"></i>Profile Settings
                        </button>
                        <a href=\"";
        // line 256
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\" class=\"btn btn-secondary\">
                            <i class=\"fas fa-tachometer-alt me-2\"></i>Dashboard
                        </a>
                        <a href=\"";
        // line 259
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\" class=\"btn btn-success\">
                            <i class=\"fas fa-graduation-cap me-2\"></i>Manage Courses
                        </a>
                        <a href=\"";
        // line 262
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_users");
        yield "\" class=\"btn btn-info\">
                            <i class=\"fas fa-users me-2\"></i>Manage Users
                        </a>
                        <a href=\"";
        // line 265
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_contacts");
        yield "\" class=\"btn btn-warning\">
                            <i class=\"fas fa-envelope me-2\"></i>Manage Contacts
                        </a>
                        <a href=\"";
        // line 268
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_add_admin");
        yield "\" class=\"btn\"
                           style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border: none; border-radius: 6px;\"
                           onmouseover=\"this.style.background='linear-gradient(135deg, #2a5298 0%, #1e3c72 100%)'\"
                           onmouseout=\"this.style.background='linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'\">
                            <i class=\"fas fa-user-plus me-2\"></i>Add New Admin
                        </a>
                        <a href=\"";
        // line 274
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_admins");
        yield "\" class=\"btn\"
                           style=\"background: #2a5298; color: white; border: none; border-radius: 6px;\"
                           onmouseover=\"this.style.background='#1e3c72'\"
                           onmouseout=\"this.style.background='#2a5298'\">
                            <i class=\"fas fa-users-cog me-2\"></i>Manage Admins
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Settings Section -->
    <div class=\"row\" id=\"profile-settings-section\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; color: #343a40; border-radius: 8px 8px 0 0; border-bottom: 1px solid #dee2e6;\">
                    <h5 class=\"mb-0 font-weight-bold\">
                        <i class=\"fas fa-cog me-2\" style=\"color: #1e3c72;\"></i>
                        Profile Settings
                    </h5>
                </div>
                <div class=\"card-body p-0\">
                    <!-- Profile Image Upload Section -->
                    <div class=\"p-4 border-bottom\">
                        <h6 class=\"text-dark mb-3\">
                            <i class=\"fas fa-camera me-2 text-success\"></i>
                            Profile Image
                        </h6>
                        <form method=\"post\" action=\"";
        // line 303
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_profile");
        yield "\" enctype=\"multipart/form-data\">
                            <input type=\"hidden\" name=\"action\" value=\"upload_profile_image\">

                            <div class=\"row\">
                                <div class=\"col-md-8\">
                                    <div class=\"mb-3\">
                                        <label for=\"profile_image_file\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-upload me-1\"></i>
                                            Upload New Profile Image
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control\"
                                               id=\"profile_image_file\"
                                               name=\"profile_image_file\"
                                               accept=\"image/jpeg,image/png,image/jpg\"
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"form-text text-muted\">
                                            <i class=\"fas fa-info-circle me-1\"></i>
                                            JPEG/PNG only, max 2MB. Recommended: 400x400px square image.
                                        </div>
                                    </div>

                                    <div class=\"image-preview mt-3\" id=\"image-preview\" style=\"display: none;\">
                                        <label class=\"form-label font-weight-medium\">Preview:</label>
                                        <div>
                                            <img src=\"\" alt=\"Preview\" class=\"rounded-circle shadow\" style=\"width: 100px; height: 100px; object-fit: cover; border: 3px solid #1e3c72;\">
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"d-flex justify-content-center\">
                                        <button type=\"submit\" class=\"btn btn-success btn-lg\">
                                            <i class=\"fas fa-save me-2\"></i>
                                            Update Image
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Change Password Section -->
                    <div class=\"p-4 border-bottom\">
                        <h6 class=\"text-dark mb-3\">
                            <i class=\"fas fa-lock me-2 text-warning\"></i>
                            Change Password
                        </h6>
                        <form method=\"post\" action=\"";
        // line 350
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_profile");
        yield "\" class=\"needs-validation\" novalidate>
                            <input type=\"hidden\" name=\"action\" value=\"change_password\">

                            <div class=\"row g-4\">
                                <div class=\"col-md-4\">
                                    <div class=\"mb-3\">
                                        <label for=\"current_password\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-key me-1\"></i>
                                            Current Password <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"password\"
                                               class=\"form-control\"
                                               id=\"current_password\"
                                               name=\"current_password\"
                                               required
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter your current password.
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"mb-3\">
                                        <label for=\"new_password\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-lock me-1\"></i>
                                            New Password <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"password\"
                                               class=\"form-control\"
                                               id=\"new_password\"
                                               name=\"new_password\"
                                               minlength=\"6\"
                                               required
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"invalid-feedback\">
                                            Password must be at least 6 characters long.
                                        </div>
                                        <div class=\"form-text text-muted\">
                                            <i class=\"fas fa-info-circle me-1\"></i>
                                            Password must be at least 6 characters long.
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"mb-3\">
                                        <label for=\"confirm_password\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-lock me-1\"></i>
                                            Confirm New Password <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"password\"
                                               class=\"form-control\"
                                               id=\"confirm_password\"
                                               name=\"confirm_password\"
                                               minlength=\"6\"
                                               required
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"invalid-feedback\">
                                            Please confirm your new password.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"d-flex justify-content-center mt-3\">
                                        <button type=\"submit\" class=\"btn btn-warning btn-lg\">
                                            <i class=\"fas fa-save me-2\"></i>
                                            Change Password
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

";
        // line 431
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        // line 491
        yield "
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 431
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 432
        yield "<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Password confirmation validation
    \$('#confirm_password').on('input', function() {
        var newPassword = \$('#new_password').val();
        var confirmPassword = \$(this).val();

        if (newPassword !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Image preview functionality
    \$('#profile_image_file').on('change', function() {
        previewImage(this, '#image-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }
});

// Smooth scroll to settings section
function scrollToSettings() {
    document.getElementById('profile-settings-section').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/profile/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  736 => 432,  723 => 431,  711 => 491,  709 => 431,  625 => 350,  575 => 303,  543 => 274,  534 => 268,  528 => 265,  522 => 262,  516 => 259,  510 => 256,  484 => 232,  480 => 230,  477 => 229,  473 => 227,  470 => 226,  466 => 224,  463 => 223,  459 => 221,  457 => 220,  448 => 213,  444 => 211,  441 => 210,  437 => 208,  434 => 207,  430 => 205,  428 => 204,  419 => 197,  415 => 195,  412 => 194,  408 => 192,  405 => 191,  401 => 189,  399 => 188,  390 => 181,  386 => 179,  383 => 178,  379 => 176,  376 => 175,  372 => 173,  369 => 172,  365 => 170,  363 => 169,  355 => 163,  348 => 158,  346 => 157,  324 => 137,  320 => 135,  314 => 133,  312 => 132,  303 => 125,  299 => 123,  293 => 121,  291 => 120,  281 => 113,  273 => 107,  267 => 103,  261 => 99,  259 => 98,  231 => 73,  222 => 67,  213 => 61,  178 => 29,  173 => 27,  164 => 21,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Admin Profile - Capitol Academy Admin{% endblock %}

{% block page_title %}Admin Profile{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Dashboard</a></li>
<li class=\"breadcrumb-item active\">Profile</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid p-0\">
    <!-- Professional Profile Header -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border-radius: 8px;\">
                <div class=\"card-body p-4\">
                    <div class=\"d-flex align-items-center\">
                        <div class=\"flex-shrink-0 me-4\">
                            <img src=\"{{ admin.profileImageUrl }}\"
                                 alt=\"Admin Profile\"
                                 class=\"rounded-circle shadow-lg\"
                                 style=\"width: 100px; height: 100px; object-fit: cover; border: 4px solid rgba(255,255,255,0.3);\">
                        </div>
                        <div class=\"flex-grow-1\">
                            <h2 class=\"mb-2 font-weight-bold\">{{ admin.fullName }}</h2>
                            <p class=\"mb-1\" style=\"font-size: 1.1rem; opacity: 0.9;\">
                                <i class=\"fas fa-envelope me-2\"></i>{{ admin.email }}
                            </p>
                            <p class=\"mb-0\" style=\"opacity: 0.8;\">
                                <i class=\"fas fa-user-shield me-2\"></i>Capitol Academy Administrator
                            </p>
                        </div>
                        <div class=\"flex-shrink-0\">
                            <button type=\"button\" class=\"btn btn-light\" onclick=\"scrollToSettings()\">
                                <i class=\"fas fa-cog me-2\"></i>Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Personal Information Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-user-circle me-2 text-primary\"></i>
                        Personal Information
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"row g-4\">
                        <div class=\"col-md-4\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Full Name</label>
                                <p class=\"h6 mb-0\">{{ admin.fullName }}</p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Username</label>
                                <p class=\"h6 mb-0\">{{ admin.username }}</p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Email Address</label>
                                <p class=\"h6 mb-0\">{{ admin.email }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Status Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-shield-alt me-2 text-success\"></i>
                        Account Status
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"row g-4\">
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Status</label>
                                <p class=\"h6 mb-0\">
                                    {% if admin.isActive %}
                                        <span class=\"badge bg-success\">
                                            <i class=\"fas fa-check-circle me-1\"></i>Active
                                        </span>
                                    {% else %}
                                        <span class=\"badge bg-danger\">
                                            <i class=\"fas fa-times-circle me-1\"></i>Inactive
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Account Created</label>
                                <p class=\"h6 mb-0\">{{ admin.createdAt|date('M j, Y') }}</p>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">Last Login</label>
                                <p class=\"h6 mb-0\">
                                    {% if admin.lastLoginAt %}
                                        {{ admin.lastLoginAt|date('M j, Y g:i A') }}
                                    {% else %}
                                        <span class=\"text-muted\">Never</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"info-group\">
                                <label class=\"text-muted small font-weight-bold mb-1\">IP Address</label>
                                <p class=\"h6 mb-0\">
                                    {% if admin.ipAddress %}
                                        <code class=\"bg-light text-dark px-2 py-1 rounded\">{{ admin.ipAddress }}</code>
                                    {% else %}
                                        <span class=\"text-muted\">Not recorded</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Permissions Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-key me-2 text-warning\"></i>
                        Admin Permissions
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    {% if admin.isMasterAdmin %}
                        <div class=\"alert alert-info mb-4\">
                            <i class=\"fas fa-crown me-2\"></i>
                            <strong>Master Administrator:</strong> You have full access to all system features and settings.
                        </div>
                    {% endif %}
                    
                    <div class=\"row g-4\">
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">Course Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    {% if admin.hasPermission('courses.read') %}
                                        <span class=\"badge bg-success\">View Courses</span>
                                    {% endif %}
                                    {% if admin.hasPermission('courses.add') %}
                                        <span class=\"badge bg-success\">Add Courses</span>
                                    {% endif %}
                                    {% if admin.hasPermission('courses.edit') %}
                                        <span class=\"badge bg-success\">Edit Courses</span>
                                    {% endif %}
                                    {% if admin.hasPermission('courses.delete') %}
                                        <span class=\"badge bg-success\">Delete Courses</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">User Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    {% if admin.hasPermission('users.read') %}
                                        <span class=\"badge bg-info\">View Users</span>
                                    {% endif %}
                                    {% if admin.hasPermission('users.edit') %}
                                        <span class=\"badge bg-info\">Edit Users</span>
                                    {% endif %}
                                    {% if admin.hasPermission('users.delete') %}
                                        <span class=\"badge bg-info\">Delete Users</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">Contact Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    {% if admin.hasPermission('contacts.read') %}
                                        <span class=\"badge bg-warning\">View Contacts</span>
                                    {% endif %}
                                    {% if admin.hasPermission('contacts.edit') %}
                                        <span class=\"badge bg-warning\">Edit Contacts</span>
                                    {% endif %}
                                    {% if admin.hasPermission('contacts.delete') %}
                                        <span class=\"badge bg-warning\">Delete Contacts</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-3\">
                            <div class=\"permission-group\">
                                <h6 class=\"text-muted mb-2\">Admin Management</h6>
                                <div class=\"d-flex flex-column gap-1\">
                                    {% if admin.hasPermission('admin.read') %}
                                        <span class=\"badge bg-danger\">View Admins</span>
                                    {% endif %}
                                    {% if admin.hasPermission('admin.add') %}
                                        <span class=\"badge bg-danger\">Add Admins</span>
                                    {% endif %}
                                    {% if admin.hasPermission('admin.edit') %}
                                        <span class=\"badge bg-danger\">Edit Admins</span>
                                    {% endif %}
                                    {% if admin.hasPermission('admin.delete') %}
                                        <span class=\"badge bg-danger\">Delete Admins</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-bolt me-2 text-primary\"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"d-flex flex-wrap gap-2\">
                        <button type=\"button\" class=\"btn btn-primary\" onclick=\"scrollToSettings()\">
                            <i class=\"fas fa-cog me-2\"></i>Profile Settings
                        </button>
                        <a href=\"{{ path('admin_dashboard') }}\" class=\"btn btn-secondary\">
                            <i class=\"fas fa-tachometer-alt me-2\"></i>Dashboard
                        </a>
                        <a href=\"{{ path('admin_courses') }}\" class=\"btn btn-success\">
                            <i class=\"fas fa-graduation-cap me-2\"></i>Manage Courses
                        </a>
                        <a href=\"{{ path('admin_users') }}\" class=\"btn btn-info\">
                            <i class=\"fas fa-users me-2\"></i>Manage Users
                        </a>
                        <a href=\"{{ path('admin_contacts') }}\" class=\"btn btn-warning\">
                            <i class=\"fas fa-envelope me-2\"></i>Manage Contacts
                        </a>
                        <a href=\"{{ path('admin_add_admin') }}\" class=\"btn\"
                           style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border: none; border-radius: 6px;\"
                           onmouseover=\"this.style.background='linear-gradient(135deg, #2a5298 0%, #1e3c72 100%)'\"
                           onmouseout=\"this.style.background='linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'\">
                            <i class=\"fas fa-user-plus me-2\"></i>Add New Admin
                        </a>
                        <a href=\"{{ path('admin_admins') }}\" class=\"btn\"
                           style=\"background: #2a5298; color: white; border: none; border-radius: 6px;\"
                           onmouseover=\"this.style.background='#1e3c72'\"
                           onmouseout=\"this.style.background='#2a5298'\">
                            <i class=\"fas fa-users-cog me-2\"></i>Manage Admins
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Settings Section -->
    <div class=\"row\" id=\"profile-settings-section\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; color: #343a40; border-radius: 8px 8px 0 0; border-bottom: 1px solid #dee2e6;\">
                    <h5 class=\"mb-0 font-weight-bold\">
                        <i class=\"fas fa-cog me-2\" style=\"color: #1e3c72;\"></i>
                        Profile Settings
                    </h5>
                </div>
                <div class=\"card-body p-0\">
                    <!-- Profile Image Upload Section -->
                    <div class=\"p-4 border-bottom\">
                        <h6 class=\"text-dark mb-3\">
                            <i class=\"fas fa-camera me-2 text-success\"></i>
                            Profile Image
                        </h6>
                        <form method=\"post\" action=\"{{ path('admin_profile') }}\" enctype=\"multipart/form-data\">
                            <input type=\"hidden\" name=\"action\" value=\"upload_profile_image\">

                            <div class=\"row\">
                                <div class=\"col-md-8\">
                                    <div class=\"mb-3\">
                                        <label for=\"profile_image_file\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-upload me-1\"></i>
                                            Upload New Profile Image
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control\"
                                               id=\"profile_image_file\"
                                               name=\"profile_image_file\"
                                               accept=\"image/jpeg,image/png,image/jpg\"
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"form-text text-muted\">
                                            <i class=\"fas fa-info-circle me-1\"></i>
                                            JPEG/PNG only, max 2MB. Recommended: 400x400px square image.
                                        </div>
                                    </div>

                                    <div class=\"image-preview mt-3\" id=\"image-preview\" style=\"display: none;\">
                                        <label class=\"form-label font-weight-medium\">Preview:</label>
                                        <div>
                                            <img src=\"\" alt=\"Preview\" class=\"rounded-circle shadow\" style=\"width: 100px; height: 100px; object-fit: cover; border: 3px solid #1e3c72;\">
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"d-flex justify-content-center\">
                                        <button type=\"submit\" class=\"btn btn-success btn-lg\">
                                            <i class=\"fas fa-save me-2\"></i>
                                            Update Image
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Change Password Section -->
                    <div class=\"p-4 border-bottom\">
                        <h6 class=\"text-dark mb-3\">
                            <i class=\"fas fa-lock me-2 text-warning\"></i>
                            Change Password
                        </h6>
                        <form method=\"post\" action=\"{{ path('admin_profile') }}\" class=\"needs-validation\" novalidate>
                            <input type=\"hidden\" name=\"action\" value=\"change_password\">

                            <div class=\"row g-4\">
                                <div class=\"col-md-4\">
                                    <div class=\"mb-3\">
                                        <label for=\"current_password\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-key me-1\"></i>
                                            Current Password <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"password\"
                                               class=\"form-control\"
                                               id=\"current_password\"
                                               name=\"current_password\"
                                               required
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter your current password.
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"mb-3\">
                                        <label for=\"new_password\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-lock me-1\"></i>
                                            New Password <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"password\"
                                               class=\"form-control\"
                                               id=\"new_password\"
                                               name=\"new_password\"
                                               minlength=\"6\"
                                               required
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"invalid-feedback\">
                                            Password must be at least 6 characters long.
                                        </div>
                                        <div class=\"form-text text-muted\">
                                            <i class=\"fas fa-info-circle me-1\"></i>
                                            Password must be at least 6 characters long.
                                        </div>
                                    </div>
                                </div>
                                <div class=\"col-md-4\">
                                    <div class=\"mb-3\">
                                        <label for=\"confirm_password\" class=\"form-label font-weight-medium\">
                                            <i class=\"fas fa-lock me-1\"></i>
                                            Confirm New Password <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"password\"
                                               class=\"form-control\"
                                               id=\"confirm_password\"
                                               name=\"confirm_password\"
                                               minlength=\"6\"
                                               required
                                               style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                        <div class=\"invalid-feedback\">
                                            Please confirm your new password.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class=\"row\">
                                <div class=\"col-12\">
                                    <div class=\"d-flex justify-content-center mt-3\">
                                        <button type=\"submit\" class=\"btn btn-warning btn-lg\">
                                            <i class=\"fas fa-save me-2\"></i>
                                            Change Password
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Password confirmation validation
    \$('#confirm_password').on('input', function() {
        var newPassword = \$('#new_password').val();
        var confirmPassword = \$(this).val();

        if (newPassword !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Image preview functionality
    \$('#profile_image_file').on('change', function() {
        previewImage(this, '#image-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }
});

// Smooth scroll to settings section
function scrollToSettings() {
    document.getElementById('profile-settings-section').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}
</script>
{% endblock %}

{% endblock %}
", "admin/profile/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\profile\\index.html.twig");
    }
}
