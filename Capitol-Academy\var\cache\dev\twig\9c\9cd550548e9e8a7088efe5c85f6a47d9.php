<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* security/register_new.html.twig */
class __TwigTemplate_da2962a4efa708bdfe7c247e217e9de9 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/register_new.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/register_new.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Register - Capitol Academy</title>

    <!-- Bootstrap CSS -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">

    <!-- Select2 CSS -->
    <link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
    <link href=\"https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css\" rel=\"stylesheet\" />

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">

    <!-- Favicon -->
    <link rel=\"icon\" type=\"image/png\" href=\"";
        // line 22
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .register-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .register-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .register-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.08)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .branding-content {
            position: relative;
            z-index: 2;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .logo {
            width: 85px;
            height: 85px;
            margin-bottom: 25px;
            filter: brightness(0) invert(1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .logo:hover {
            transform: scale(1.08) rotate(5deg);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .brand-title {
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
            color: white;
        }

        .brand-subtitle {
            font-size: 1rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Why Choose Section */
        .why-choose-section {
            margin-top: 30px;
        }

        .why-choose-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: white;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .why-choose-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, #ff6b6b 100%);
            border-radius: 1px;
        }

        .benefits-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .benefit-item:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .benefit-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, #ff6b6b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        .benefit-icon i {
            font-size: 1.2rem;
            color: white;
        }

        .benefit-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            margin: 0 0 5px 0;
            line-height: 1.3;
        }

        .benefit-content p {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            line-height: 1.4;
        }

        .brand-subtitle {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .features-list {
            list-style: none;
            margin-top: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.95;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            opacity: 1;
            transform: translateX(5px);
        }

        .feature-item i {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-item:hover i {
            background: var(--ca-accent-red);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        /* Right Side - Form */
        .register-form-section {
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
        }

        .form-container {
            max-width: 360px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeInUp 0.6s ease;
        }

        .header-content {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .form-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--ca-dark-gray);
            margin: 0;
            letter-spacing: -0.3px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            white-space: nowrap;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 8px;
            }

            .form-title {
                font-size: 1.8rem;
            }

            .form-subtitle {
                font-size: 0.9rem;
            }
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 28px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            border: none;
            border-bottom: 2px solid #e8ecef;
            background: transparent;
            font-size: 1.05rem;
            color: var(--ca-dark-gray);
            outline: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            height: calc(1.6em + 1.25rem + 4px);
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
            background: transparent !important;
            transform: translateY(-1px);
        }

        .floating-input:hover:not(:focus) {
            border-bottom-color: var(--ca-focus-blue-border);
        }

        .floating-input:focus + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label,
        .floating-input.has-value + .floating-label {
            top: 0;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 600;
        }

        .floating-label {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.05rem;
            font-weight: 500;
            color: var(--ca-medium-gray);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
            z-index: 1;
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30%;
            background: linear-gradient(135deg, var(--ca-focus-blue-border) 0%, rgba(26, 52, 97, 0.5) 100%);
        }

        /* Register Button */
        .register-btn {
            width: 100%;
            padding: 18px 35px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            border: none;
            border-radius: 60px;
            color: var(--ca-white);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
        }

        .register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .register-btn:hover::before {
            left: 100%;
        }

        .register-btn:hover {
            background: linear-gradient(135deg, var(--ca-accent-red-dark) 0%, var(--ca-accent-red) 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(169, 4, 24, 0.4);
        }

        .register-btn:focus {
            outline: 3px solid var(--ca-focus-blue-border);
            outline-offset: 3px;
        }

        .register-btn:active {
            transform: translateY(-1px);
        }

        .btn-icon {
            transition: transform 0.3s ease;
            font-size: 1.2rem;
        }

        .register-btn:hover .btn-icon {
            transform: translateX(8px);
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.8s;
            animation-fill-mode: both;
        }

        .back-link, .register-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            border: 2px solid;
            text-align: center;
            min-width: 140px;
        }

        .back-link {
            background: white;
            color: var(--ca-focus-blue);
            border-color: var(--ca-focus-blue);
        }

        .back-link:hover {
            background: var(--ca-focus-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        .login-text-link {
            color: var(--ca-focus-blue);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: block;
            margin-top: 15px;
        }

        .login-text-link:hover {
            color: var(--ca-accent-red);
            text-decoration: underline;
        }

        /* Alerts */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            animation: fadeInDown 0.5s ease;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-left: 4px solid #dc3545;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-left: 4px solid var(--ca-success-green);
        }

        /* Profile Picture Preview */
        .profile-picture-preview {
            display: none;
            text-align: center;
            margin-top: 15px;
            animation: fadeInUp 0.3s ease;
        }

        .profile-picture-preview img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--ca-focus-blue);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.2);
            transition: all 0.3s ease;
        }

        .profile-picture-preview img:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(1, 26, 45, 0.3);
        }

        .profile-picture-preview.show {
            display: block;
        }



        /* Country Dropdown - Floating Label Style */
        .floating-country-group {
            position: relative;
            margin-bottom: 24px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }



        /* Select2 styling to match floating labels */
        .select2-container--bootstrap-5 .select2-selection--single {
            border: none !important;
            border-bottom: 2px solid #e8ecef !important;
            border-radius: 0 !important;
            background: transparent !important;
            height: calc(1.6em + 1.25rem + 4px) !important;
            padding: 22px 0 12px 0 !important;
            font-size: 1.05rem !important;
            color: var(--ca-dark-gray) !important;
            font-weight: 500 !important;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
            border-bottom-color: var(--ca-focus-blue) !important;
            transform: translateY(-1px);
            box-shadow: none !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single:hover {
            border-bottom-color: var(--ca-focus-blue-border) !important;
        }

        .select2-container--bootstrap-5 .select2-selection__rendered {
            padding: 0 !important;
            line-height: 1.5 !important;
            color: var(--ca-dark-gray) !important;
        }

        .select2-container--bootstrap-5 .select2-selection__arrow {
            height: calc(1.6em + 1.25rem + 4px) !important;
            right: 0 !important;
        }

        /* File Input Styling */
        .profile-picture-input {
            position: relative;
            height: calc(1.6em + 1.25rem + 4px) !important;
            min-height: calc(1.6em + 1.25rem + 4px) !important;
            display: flex !important;
            align-items: center !important;
            padding-top: 22px !important;
            padding-bottom: 12px !important;
            padding-left: 0 !important;
            line-height: 1.5 !important;
        }

        .profile-picture-input::-webkit-file-upload-button {
            background: linear-gradient(135deg, var(--ca-focus-blue) 0%, var(--ca-secondary-blue) 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 12px;
            margin-top: -10px;
            margin-bottom: -10px;
        }

        .profile-picture-input::-webkit-file-upload-button:hover {
            background: linear-gradient(135deg, var(--ca-secondary-blue) 0%, var(--ca-focus-blue) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        /* Date Input Styling */
        input[type=\"date\"].floating-input {
            position: relative;
            color-scheme: light;
            cursor: pointer;
        }

        input[type=\"date\"].floating-input::-webkit-calendar-picker-indicator {
            background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23011a2d' viewBox='0 0 16 16'%3e%3cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e\");
            background-size: 1.2em 1.2em;
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            opacity: 0.8;
            transition: all 0.3s ease;
            width: 2em;
            height: 2em;
            border-radius: 4px;
        }

        input[type=\"date\"].floating-input::-webkit-calendar-picker-indicator:hover {
            opacity: 1;
            background-color: rgba(1, 26, 45, 0.1);
            transform: scale(1.05);
        }

        input[type=\"date\"].floating-input:focus::-webkit-calendar-picker-indicator {
            opacity: 1;
            background-color: rgba(1, 26, 45, 0.15);
        }

        /* Enhanced Calendar Popup Styling */
        input[type=\"date\"]::-webkit-datetime-edit {
            color: var(--ca-dark-gray);
            font-weight: 500;
        }

        input[type=\"date\"]::-webkit-datetime-edit-fields-wrapper {
            padding: 0;
        }

        input[type=\"date\"]::-webkit-datetime-edit-text {
            color: var(--ca-medium-gray);
            padding: 0 0.3em;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field,
        input[type=\"date\"]::-webkit-datetime-edit-day-field,
        input[type=\"date\"]::-webkit-datetime-edit-year-field {
            color: var(--ca-dark-gray);
            font-weight: 500;
            padding: 0 0.2em;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-day-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-year-field:focus {
            background-color: rgba(1, 26, 45, 0.1);
            color: var(--ca-focus-blue);
            outline: none;
        }

        /* Calendar Popup Enhancement (Browser-specific) */
        input[type=\"date\"]::-webkit-calendar-picker-indicator {
            filter: drop-shadow(0 2px 4px rgba(1, 26, 45, 0.2));
        }

        /* Focus state for date input */
        input[type=\"date\"].floating-input:focus {
            border-bottom-color: var(--ca-focus-blue) !important;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
        }

        /* Placeholder styling for date input */
        input[type=\"date\"].floating-input:invalid {
            color: var(--ca-medium-gray);
        }

        input[type=\"date\"].floating-input:valid {
            color: var(--ca-dark-gray);
        }

        /* Select2 Custom Styling for Country Dropdown */
        .select2-container--bootstrap-5 .select2-selection--single {
            height: calc(1.6em + 1.25rem + 4px) !important;
            min-height: calc(1.6em + 1.25rem + 4px) !important;
            border: none !important;
            border-bottom: 2px solid #e8ecef !important;
            border-radius: 0 !important;
            background: transparent !important;
            font-size: 1.05rem !important;
            color: var(--ca-dark-gray) !important;
            font-weight: 500 !important;
            padding: 22px 0 12px 0 !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding-left: 0 !important;
            padding-right: 30px !important;
            line-height: calc(1.6em + 1.25rem) !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            color: var(--ca-dark-gray) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: calc(1.6em + 1.25rem + 4px) !important;
            right: 0 !important;
            top: 0 !important;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
            border-bottom-color: var(--ca-focus-blue) !important;
            transform: translateY(-1px);
        }

        .select2-container--bootstrap-5 .select2-dropdown {
            border: 2px solid var(--ca-focus-blue) !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.15) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--ca-focus-blue) !important;
            color: white !important;
        }

        /* Enhanced Calendar Popup Styling - Custom CSS for modern browsers */
        @supports (-webkit-appearance: none) {
            /* Webkit-based browsers calendar styling */
            input[type=\"date\"]::-webkit-calendar-picker-indicator {
                background: none;
                background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23011a2d' viewBox='0 0 16 16'%3e%3cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e\");
                background-size: 1.2em 1.2em;
                background-repeat: no-repeat;
                background-position: center;
                width: 2em;
                height: 2em;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.3s ease;
                opacity: 0.8;
                margin-left: 8px;
            }

            input[type=\"date\"]::-webkit-calendar-picker-indicator:hover {
                opacity: 1;
                background-color: rgba(1, 26, 45, 0.1);
                transform: scale(1.05);
                box-shadow: 0 2px 8px rgba(1, 26, 45, 0.2);
            }

            input[type=\"date\"]::-webkit-calendar-picker-indicator:active {
                background-color: rgba(1, 26, 45, 0.2);
                transform: scale(0.98);
            }
        }

        /* Modern Calendar Popup Enhancement */
        input[type=\"date\"] {
            position: relative;
        }

        /* Custom calendar styling for better visual appeal */
        input[type=\"date\"]:focus {
            outline: none;
            border-bottom-color: var(--ca-focus-blue) !important;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15), 0 0 0 3px rgba(1, 26, 45, 0.1);
        }

        /* Date field enhancement */
        input[type=\"date\"]::-webkit-datetime-edit {
            padding: 0;
            color: var(--ca-dark-gray);
            font-weight: 500;
        }

        input[type=\"date\"]::-webkit-datetime-edit-fields-wrapper {
            padding: 0;
            display: flex;
            align-items: center;
        }

        input[type=\"date\"]::-webkit-datetime-edit-text {
            color: var(--ca-medium-gray);
            padding: 0 0.3em;
            font-weight: 400;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field,
        input[type=\"date\"]::-webkit-datetime-edit-day-field,
        input[type=\"date\"]::-webkit-datetime-edit-year-field {
            color: var(--ca-dark-gray);
            font-weight: 600;
            padding: 2px 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            background: transparent;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-day-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-year-field:focus {
            background-color: rgba(1, 26, 45, 0.1);
            color: var(--ca-focus-blue);
            outline: none;
            box-shadow: 0 0 0 2px rgba(1, 26, 45, 0.2);
        }

        /* Placeholder styling when no date is selected */
        input[type=\"date\"]:invalid::-webkit-datetime-edit {
            color: var(--ca-medium-gray);
            opacity: 0.7;
        }

        input[type=\"date\"]:valid::-webkit-datetime-edit {
            color: var(--ca-dark-gray);
            opacity: 1;
        }

        /* Animation Keyframes */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .register-wrapper {
                grid-template-columns: 1fr;
                max-width: 450px;
            }

            .register-branding {
                padding: 40px 30px;
                min-height: 400px;
            }

            .brand-logo-round {
                width: 80px;
                height: 80px;
            }

            .brand-title {
                font-size: 1.8rem;
            }

            .brand-subtitle {
                font-size: 0.9rem;
                margin-bottom: 30px;
            }

            .why-choose-title {
                font-size: 1.2rem;
                margin-bottom: 20px;
            }

            .benefit-item {
                padding: 12px;
                gap: 12px;
            }

            .benefit-icon {
                width: 35px;
                height: 35px;
            }

            .benefit-icon i {
                font-size: 1rem;
            }

            .benefit-content h4 {
                font-size: 1rem;
            }

            .benefit-content p {
                font-size: 0.85rem;
            }

            .register-form-section {
                padding: 40px 30px;
            }

            .form-title {
                font-size: 1.8rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .register-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .register-container {
                padding: 15px;
            }

            .register-branding {
                padding: 30px 20px;
                min-height: 350px;
            }

            .brand-logo-round {
                width: 70px;
                height: 70px;
            }

            .brand-title {
                font-size: 1.6rem;
            }

            .why-choose-section {
                margin-top: 20px;
            }

            .benefits-list {
                gap: 15px;
            }

            .benefit-item {
                padding: 10px;
                gap: 10px;
            }

            .register-form-section {
                padding: 30px 20px;
            }

            .floating-input {
                font-size: 1rem;
            }

            .register-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class=\"register-container\">
        <div class=\"register-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"register-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"";
        // line 1058
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                        <p class=\"brand-subtitle\">Professional Trading Education</p>
                    </div>

                    <div class=\"why-choose-section\">
                        <h3 class=\"why-choose-title\">Why Choose Capitol Academy?</h3>
                        <div class=\"benefits-list\">
                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-graduation-cap\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Expert Instructors</h4>
                                    <p>Learn from industry professionals with years of trading experience</p>
                                </div>
                            </div>

                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-chart-line\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Real Market Analysis</h4>
                                    <p>Access live market insights and professional trading strategies</p>
                                </div>
                            </div>

                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-certificate\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Professional Certification</h4>
                                    <p>Earn recognized credentials to advance your trading career</p>
                                </div>
                            </div>

                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-users\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Trading Community</h4>
                                    <p>Connect with fellow traders and share knowledge and strategies</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Registration Form -->
            <div class=\"register-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <div class=\"header-content\">
                            <h2 class=\"form-title\">Join Capitol Academy</h2>
                            <span class=\"form-subtitle\">Create your account to start learning</span>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    ";
        // line 1122
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 1122, $this->source); })()), "flashes", ["verify_email_error"], "method", false, false, false, 1122));
        foreach ($context['_seq'] as $context["_key"] => $context["flash_error"]) {
            // line 1123
            yield "                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            <span>";
            // line 1125
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["flash_error"], "html", null, true);
            yield "</span>
                        </div>
                    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['flash_error'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 1128
        yield "
                    <!-- Registration Form -->
                    ";
        // line 1130
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1130, $this->source); })()), 'form_start', ["attr" => ["method" => "post", "enctype" => "multipart/form-data"]]);
        yield "
                        <!-- First Name -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1133
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1133, $this->source); })()), "firstName", [], "any", false, false, false, 1133), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1138
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-user\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                First Name
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Last Name -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1148
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1148, $this->source); })()), "lastName", [], "any", false, false, false, 1148), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1153
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-user\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Last Name
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Date of Birth -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1163
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1163, $this->source); })()), "dateOfBirth", [], "any", false, false, false, 1163), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1168
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-calendar\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Date of Birth
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Email -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1178
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1178, $this->source); })()), "email", [], "any", false, false, false, 1178), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1183
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-envelope\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Email Address
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Phone Number -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1193
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1193, $this->source); })()), "phone", [], "any", false, false, false, 1193), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1198
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-phone\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Phone Number
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Country -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1208
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1208, $this->source); })()), "country", [], "any", false, false, false, 1208), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1213
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-globe\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Country
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Profile Picture -->
                        <div class=\"floating-label-group\" style=\"margin-top: 24px;\">
                            ";
        // line 1223
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1223, $this->source); })()), "profilePicture", [], "any", false, false, false, 1223), 'widget', ["attr" => ["class" => "floating-input profile-picture-input", "id" => "profile-picture-input"]]);
        // line 1228
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-camera\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Profile Picture
                            </label>
                            <div class=\"input-border\"></div>
                            <div class=\"profile-picture-preview\" id=\"profile-picture-preview\">
                                <img src=\"\" alt=\"Profile Preview\" id=\"profile-preview-img\">
                            </div>
                        </div>

                        <!-- Password -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1241
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1241, $this->source); })()), "plainPassword", [], "any", false, false, false, 1241), "first", [], "any", false, false, false, 1241), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1246
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Confirm Password -->
                        <div class=\"floating-label-group\">
                            ";
        // line 1256
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1256, $this->source); })()), "plainPassword", [], "any", false, false, false, 1256), "second", [], "any", false, false, false, 1256), 'widget', ["attr" => ["class" => "floating-input", "placeholder" => " "]]);
        // line 1261
        yield "
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Confirm Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Register Button -->
                        <button type=\"submit\" class=\"register-btn\">
                            <i class=\"fas fa-user-plus btn-icon\"></i>
                            <span>Create Account</span>
                        </button>
                    ";
        // line 1274
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["registrationForm"]) || array_key_exists("registrationForm", $context) ? $context["registrationForm"] : (function () { throw new RuntimeError('Variable "registrationForm" does not exist.', 1274, $this->source); })()), 'form_end');
        yield "

                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"";
        // line 1278
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\" class=\"back-link\">
                            <i class=\"fas fa-arrow-left\"></i> Back to Website
                        </a>
                        <a href=\"";
        // line 1281
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
        yield "\" class=\"login-text-link\">
                            Already have an account? Sign in here
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <!-- jQuery -->
    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>

    <!-- Select2 JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Enhanced form interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Select2 for country dropdown
            const countrySelect = document.getElementById('country-select') || document.querySelector('select[name*=\"country\"]');
            if (countrySelect) {
                \$(countrySelect).select2({
                    placeholder: 'Choose a country...',
                    allowClear: true,
                    width: '100%',
                    theme: 'bootstrap-5'
                });
            }

            // Country field now uses standard floating label functionality

            const inputs = document.querySelectorAll('.floating-input');

            inputs.forEach(input => {
                // Handle autofill detection
                const checkAutofill = () => {
                    if (input.value !== '') {
                        input.classList.add('has-value');
                    } else {
                        input.classList.remove('has-value');
                    }
                };

                input.addEventListener('input', checkAutofill);
                input.addEventListener('blur', checkAutofill);

                // Initial check
                setTimeout(checkAutofill, 100);
            });

            // Profile picture preview - try multiple possible IDs
            const profileInput = document.getElementById('profile-picture-input') ||
                                document.getElementById('registration_form_profilePicture') ||
                                document.querySelector('input[type=\"file\"]');
            const profilePreview = document.getElementById('profile-picture-preview');
            const profileImg = document.getElementById('profile-preview-img');

            if (profileInput && profilePreview && profileImg) {
                profileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            profileImg.src = e.target.result;
                            profilePreview.classList.add('show');
                        };
                        reader.readAsDataURL(file);
                    } else {
                        profilePreview.classList.remove('show');
                    }
                });
            }
        });
    </script>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "security/register_new.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1349 => 1281,  1343 => 1278,  1336 => 1274,  1321 => 1261,  1319 => 1256,  1307 => 1246,  1305 => 1241,  1290 => 1228,  1288 => 1223,  1276 => 1213,  1274 => 1208,  1262 => 1198,  1260 => 1193,  1248 => 1183,  1246 => 1178,  1234 => 1168,  1232 => 1163,  1220 => 1153,  1218 => 1148,  1206 => 1138,  1204 => 1133,  1198 => 1130,  1194 => 1128,  1185 => 1125,  1181 => 1123,  1177 => 1122,  1110 => 1058,  71 => 22,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Register - Capitol Academy</title>

    <!-- Bootstrap CSS -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">

    <!-- Select2 CSS -->
    <link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
    <link href=\"https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css\" rel=\"stylesheet\" />

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">

    <!-- Favicon -->
    <link rel=\"icon\" type=\"image/png\" href=\"{{ asset('images/logos/logo-round.png') }}\">

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .register-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .register-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .register-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.08)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .branding-content {
            position: relative;
            z-index: 2;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .logo {
            width: 85px;
            height: 85px;
            margin-bottom: 25px;
            filter: brightness(0) invert(1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .logo:hover {
            transform: scale(1.08) rotate(5deg);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .brand-title {
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
            color: white;
        }

        .brand-subtitle {
            font-size: 1rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Why Choose Section */
        .why-choose-section {
            margin-top: 30px;
        }

        .why-choose-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: white;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .why-choose-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, #ff6b6b 100%);
            border-radius: 1px;
        }

        .benefits-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .benefit-item:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .benefit-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, #ff6b6b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        .benefit-icon i {
            font-size: 1.2rem;
            color: white;
        }

        .benefit-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            margin: 0 0 5px 0;
            line-height: 1.3;
        }

        .benefit-content p {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            line-height: 1.4;
        }

        .brand-subtitle {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .features-list {
            list-style: none;
            margin-top: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.95;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            opacity: 1;
            transform: translateX(5px);
        }

        .feature-item i {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-item:hover i {
            background: var(--ca-accent-red);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        /* Right Side - Form */
        .register-form-section {
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
        }

        .form-container {
            max-width: 360px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeInUp 0.6s ease;
        }

        .header-content {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .form-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--ca-dark-gray);
            margin: 0;
            letter-spacing: -0.3px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
            white-space: nowrap;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 8px;
            }

            .form-title {
                font-size: 1.8rem;
            }

            .form-subtitle {
                font-size: 0.9rem;
            }
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 28px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            border: none;
            border-bottom: 2px solid #e8ecef;
            background: transparent;
            font-size: 1.05rem;
            color: var(--ca-dark-gray);
            outline: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            height: calc(1.6em + 1.25rem + 4px);
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
            background: transparent !important;
            transform: translateY(-1px);
        }

        .floating-input:hover:not(:focus) {
            border-bottom-color: var(--ca-focus-blue-border);
        }

        .floating-input:focus + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label,
        .floating-input.has-value + .floating-label {
            top: 0;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 600;
        }

        .floating-label {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.05rem;
            font-weight: 500;
            color: var(--ca-medium-gray);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
            z-index: 1;
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30%;
            background: linear-gradient(135deg, var(--ca-focus-blue-border) 0%, rgba(26, 52, 97, 0.5) 100%);
        }

        /* Register Button */
        .register-btn {
            width: 100%;
            padding: 18px 35px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            border: none;
            border-radius: 60px;
            color: var(--ca-white);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
        }

        .register-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .register-btn:hover::before {
            left: 100%;
        }

        .register-btn:hover {
            background: linear-gradient(135deg, var(--ca-accent-red-dark) 0%, var(--ca-accent-red) 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(169, 4, 24, 0.4);
        }

        .register-btn:focus {
            outline: 3px solid var(--ca-focus-blue-border);
            outline-offset: 3px;
        }

        .register-btn:active {
            transform: translateY(-1px);
        }

        .btn-icon {
            transition: transform 0.3s ease;
            font-size: 1.2rem;
        }

        .register-btn:hover .btn-icon {
            transform: translateX(8px);
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.8s;
            animation-fill-mode: both;
        }

        .back-link, .register-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            border: 2px solid;
            text-align: center;
            min-width: 140px;
        }

        .back-link {
            background: white;
            color: var(--ca-focus-blue);
            border-color: var(--ca-focus-blue);
        }

        .back-link:hover {
            background: var(--ca-focus-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        .login-text-link {
            color: var(--ca-focus-blue);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: block;
            margin-top: 15px;
        }

        .login-text-link:hover {
            color: var(--ca-accent-red);
            text-decoration: underline;
        }

        /* Alerts */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            animation: fadeInDown 0.5s ease;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-left: 4px solid #dc3545;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-left: 4px solid var(--ca-success-green);
        }

        /* Profile Picture Preview */
        .profile-picture-preview {
            display: none;
            text-align: center;
            margin-top: 15px;
            animation: fadeInUp 0.3s ease;
        }

        .profile-picture-preview img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--ca-focus-blue);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.2);
            transition: all 0.3s ease;
        }

        .profile-picture-preview img:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(1, 26, 45, 0.3);
        }

        .profile-picture-preview.show {
            display: block;
        }



        /* Country Dropdown - Floating Label Style */
        .floating-country-group {
            position: relative;
            margin-bottom: 24px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }



        /* Select2 styling to match floating labels */
        .select2-container--bootstrap-5 .select2-selection--single {
            border: none !important;
            border-bottom: 2px solid #e8ecef !important;
            border-radius: 0 !important;
            background: transparent !important;
            height: calc(1.6em + 1.25rem + 4px) !important;
            padding: 22px 0 12px 0 !important;
            font-size: 1.05rem !important;
            color: var(--ca-dark-gray) !important;
            font-weight: 500 !important;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
            border-bottom-color: var(--ca-focus-blue) !important;
            transform: translateY(-1px);
            box-shadow: none !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single:hover {
            border-bottom-color: var(--ca-focus-blue-border) !important;
        }

        .select2-container--bootstrap-5 .select2-selection__rendered {
            padding: 0 !important;
            line-height: 1.5 !important;
            color: var(--ca-dark-gray) !important;
        }

        .select2-container--bootstrap-5 .select2-selection__arrow {
            height: calc(1.6em + 1.25rem + 4px) !important;
            right: 0 !important;
        }

        /* File Input Styling */
        .profile-picture-input {
            position: relative;
            height: calc(1.6em + 1.25rem + 4px) !important;
            min-height: calc(1.6em + 1.25rem + 4px) !important;
            display: flex !important;
            align-items: center !important;
            padding-top: 22px !important;
            padding-bottom: 12px !important;
            padding-left: 0 !important;
            line-height: 1.5 !important;
        }

        .profile-picture-input::-webkit-file-upload-button {
            background: linear-gradient(135deg, var(--ca-focus-blue) 0%, var(--ca-secondary-blue) 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 12px;
            margin-top: -10px;
            margin-bottom: -10px;
        }

        .profile-picture-input::-webkit-file-upload-button:hover {
            background: linear-gradient(135deg, var(--ca-secondary-blue) 0%, var(--ca-focus-blue) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        /* Date Input Styling */
        input[type=\"date\"].floating-input {
            position: relative;
            color-scheme: light;
            cursor: pointer;
        }

        input[type=\"date\"].floating-input::-webkit-calendar-picker-indicator {
            background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23011a2d' viewBox='0 0 16 16'%3e%3cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e\");
            background-size: 1.2em 1.2em;
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            opacity: 0.8;
            transition: all 0.3s ease;
            width: 2em;
            height: 2em;
            border-radius: 4px;
        }

        input[type=\"date\"].floating-input::-webkit-calendar-picker-indicator:hover {
            opacity: 1;
            background-color: rgba(1, 26, 45, 0.1);
            transform: scale(1.05);
        }

        input[type=\"date\"].floating-input:focus::-webkit-calendar-picker-indicator {
            opacity: 1;
            background-color: rgba(1, 26, 45, 0.15);
        }

        /* Enhanced Calendar Popup Styling */
        input[type=\"date\"]::-webkit-datetime-edit {
            color: var(--ca-dark-gray);
            font-weight: 500;
        }

        input[type=\"date\"]::-webkit-datetime-edit-fields-wrapper {
            padding: 0;
        }

        input[type=\"date\"]::-webkit-datetime-edit-text {
            color: var(--ca-medium-gray);
            padding: 0 0.3em;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field,
        input[type=\"date\"]::-webkit-datetime-edit-day-field,
        input[type=\"date\"]::-webkit-datetime-edit-year-field {
            color: var(--ca-dark-gray);
            font-weight: 500;
            padding: 0 0.2em;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-day-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-year-field:focus {
            background-color: rgba(1, 26, 45, 0.1);
            color: var(--ca-focus-blue);
            outline: none;
        }

        /* Calendar Popup Enhancement (Browser-specific) */
        input[type=\"date\"]::-webkit-calendar-picker-indicator {
            filter: drop-shadow(0 2px 4px rgba(1, 26, 45, 0.2));
        }

        /* Focus state for date input */
        input[type=\"date\"].floating-input:focus {
            border-bottom-color: var(--ca-focus-blue) !important;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
        }

        /* Placeholder styling for date input */
        input[type=\"date\"].floating-input:invalid {
            color: var(--ca-medium-gray);
        }

        input[type=\"date\"].floating-input:valid {
            color: var(--ca-dark-gray);
        }

        /* Select2 Custom Styling for Country Dropdown */
        .select2-container--bootstrap-5 .select2-selection--single {
            height: calc(1.6em + 1.25rem + 4px) !important;
            min-height: calc(1.6em + 1.25rem + 4px) !important;
            border: none !important;
            border-bottom: 2px solid #e8ecef !important;
            border-radius: 0 !important;
            background: transparent !important;
            font-size: 1.05rem !important;
            color: var(--ca-dark-gray) !important;
            font-weight: 500 !important;
            padding: 22px 0 12px 0 !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding-left: 0 !important;
            padding-right: 30px !important;
            line-height: calc(1.6em + 1.25rem) !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            color: var(--ca-dark-gray) !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: calc(1.6em + 1.25rem + 4px) !important;
            right: 0 !important;
            top: 0 !important;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection--single {
            border-bottom-color: var(--ca-focus-blue) !important;
            transform: translateY(-1px);
        }

        .select2-container--bootstrap-5 .select2-dropdown {
            border: 2px solid var(--ca-focus-blue) !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.15) !important;
        }

        .select2-container--bootstrap-5 .select2-results__option--highlighted {
            background-color: var(--ca-focus-blue) !important;
            color: white !important;
        }

        /* Enhanced Calendar Popup Styling - Custom CSS for modern browsers */
        @supports (-webkit-appearance: none) {
            /* Webkit-based browsers calendar styling */
            input[type=\"date\"]::-webkit-calendar-picker-indicator {
                background: none;
                background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23011a2d' viewBox='0 0 16 16'%3e%3cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e\");
                background-size: 1.2em 1.2em;
                background-repeat: no-repeat;
                background-position: center;
                width: 2em;
                height: 2em;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.3s ease;
                opacity: 0.8;
                margin-left: 8px;
            }

            input[type=\"date\"]::-webkit-calendar-picker-indicator:hover {
                opacity: 1;
                background-color: rgba(1, 26, 45, 0.1);
                transform: scale(1.05);
                box-shadow: 0 2px 8px rgba(1, 26, 45, 0.2);
            }

            input[type=\"date\"]::-webkit-calendar-picker-indicator:active {
                background-color: rgba(1, 26, 45, 0.2);
                transform: scale(0.98);
            }
        }

        /* Modern Calendar Popup Enhancement */
        input[type=\"date\"] {
            position: relative;
        }

        /* Custom calendar styling for better visual appeal */
        input[type=\"date\"]:focus {
            outline: none;
            border-bottom-color: var(--ca-focus-blue) !important;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15), 0 0 0 3px rgba(1, 26, 45, 0.1);
        }

        /* Date field enhancement */
        input[type=\"date\"]::-webkit-datetime-edit {
            padding: 0;
            color: var(--ca-dark-gray);
            font-weight: 500;
        }

        input[type=\"date\"]::-webkit-datetime-edit-fields-wrapper {
            padding: 0;
            display: flex;
            align-items: center;
        }

        input[type=\"date\"]::-webkit-datetime-edit-text {
            color: var(--ca-medium-gray);
            padding: 0 0.3em;
            font-weight: 400;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field,
        input[type=\"date\"]::-webkit-datetime-edit-day-field,
        input[type=\"date\"]::-webkit-datetime-edit-year-field {
            color: var(--ca-dark-gray);
            font-weight: 600;
            padding: 2px 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            background: transparent;
        }

        input[type=\"date\"]::-webkit-datetime-edit-month-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-day-field:focus,
        input[type=\"date\"]::-webkit-datetime-edit-year-field:focus {
            background-color: rgba(1, 26, 45, 0.1);
            color: var(--ca-focus-blue);
            outline: none;
            box-shadow: 0 0 0 2px rgba(1, 26, 45, 0.2);
        }

        /* Placeholder styling when no date is selected */
        input[type=\"date\"]:invalid::-webkit-datetime-edit {
            color: var(--ca-medium-gray);
            opacity: 0.7;
        }

        input[type=\"date\"]:valid::-webkit-datetime-edit {
            color: var(--ca-dark-gray);
            opacity: 1;
        }

        /* Animation Keyframes */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .register-wrapper {
                grid-template-columns: 1fr;
                max-width: 450px;
            }

            .register-branding {
                padding: 40px 30px;
                min-height: 400px;
            }

            .brand-logo-round {
                width: 80px;
                height: 80px;
            }

            .brand-title {
                font-size: 1.8rem;
            }

            .brand-subtitle {
                font-size: 0.9rem;
                margin-bottom: 30px;
            }

            .why-choose-title {
                font-size: 1.2rem;
                margin-bottom: 20px;
            }

            .benefit-item {
                padding: 12px;
                gap: 12px;
            }

            .benefit-icon {
                width: 35px;
                height: 35px;
            }

            .benefit-icon i {
                font-size: 1rem;
            }

            .benefit-content h4 {
                font-size: 1rem;
            }

            .benefit-content p {
                font-size: 0.85rem;
            }

            .register-form-section {
                padding: 40px 30px;
            }

            .form-title {
                font-size: 1.8rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .register-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .register-container {
                padding: 15px;
            }

            .register-branding {
                padding: 30px 20px;
                min-height: 350px;
            }

            .brand-logo-round {
                width: 70px;
                height: 70px;
            }

            .brand-title {
                font-size: 1.6rem;
            }

            .why-choose-section {
                margin-top: 20px;
            }

            .benefits-list {
                gap: 15px;
            }

            .benefit-item {
                padding: 10px;
                gap: 10px;
            }

            .register-form-section {
                padding: 30px 20px;
            }

            .floating-input {
                font-size: 1rem;
            }

            .register-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class=\"register-container\">
        <div class=\"register-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"register-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"{{ asset('images/logos/logo-round.png') }}\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                        <p class=\"brand-subtitle\">Professional Trading Education</p>
                    </div>

                    <div class=\"why-choose-section\">
                        <h3 class=\"why-choose-title\">Why Choose Capitol Academy?</h3>
                        <div class=\"benefits-list\">
                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-graduation-cap\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Expert Instructors</h4>
                                    <p>Learn from industry professionals with years of trading experience</p>
                                </div>
                            </div>

                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-chart-line\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Real Market Analysis</h4>
                                    <p>Access live market insights and professional trading strategies</p>
                                </div>
                            </div>

                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-certificate\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Professional Certification</h4>
                                    <p>Earn recognized credentials to advance your trading career</p>
                                </div>
                            </div>

                            <div class=\"benefit-item\">
                                <div class=\"benefit-icon\">
                                    <i class=\"fas fa-users\"></i>
                                </div>
                                <div class=\"benefit-content\">
                                    <h4>Trading Community</h4>
                                    <p>Connect with fellow traders and share knowledge and strategies</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Registration Form -->
            <div class=\"register-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <div class=\"header-content\">
                            <h2 class=\"form-title\">Join Capitol Academy</h2>
                            <span class=\"form-subtitle\">Create your account to start learning</span>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    {% for flash_error in app.flashes('verify_email_error') %}
                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            <span>{{ flash_error }}</span>
                        </div>
                    {% endfor %}

                    <!-- Registration Form -->
                    {{ form_start(registrationForm, {'attr': {'method': 'post', 'enctype': 'multipart/form-data'}}) }}
                        <!-- First Name -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.firstName, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-user\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                First Name
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Last Name -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.lastName, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-user\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Last Name
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Date of Birth -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.dateOfBirth, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-calendar\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Date of Birth
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Email -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.email, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-envelope\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Email Address
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Phone Number -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.phone, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-phone\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Phone Number
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Country -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.country, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-globe\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Country
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Profile Picture -->
                        <div class=\"floating-label-group\" style=\"margin-top: 24px;\">
                            {{ form_widget(registrationForm.profilePicture, {
                                'attr': {
                                    'class': 'floating-input profile-picture-input',
                                    'id': 'profile-picture-input'
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-camera\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Profile Picture
                            </label>
                            <div class=\"input-border\"></div>
                            <div class=\"profile-picture-preview\" id=\"profile-picture-preview\">
                                <img src=\"\" alt=\"Profile Preview\" id=\"profile-preview-img\">
                            </div>
                        </div>

                        <!-- Password -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.plainPassword.first, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Confirm Password -->
                        <div class=\"floating-label-group\">
                            {{ form_widget(registrationForm.plainPassword.second, {
                                'attr': {
                                    'class': 'floating-input',
                                    'placeholder': ' '
                                }
                            }) }}
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Confirm Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Register Button -->
                        <button type=\"submit\" class=\"register-btn\">
                            <i class=\"fas fa-user-plus btn-icon\"></i>
                            <span>Create Account</span>
                        </button>
                    {{ form_end(registrationForm) }}

                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"{{ path('app_home') }}\" class=\"back-link\">
                            <i class=\"fas fa-arrow-left\"></i> Back to Website
                        </a>
                        <a href=\"{{ path('app_login') }}\" class=\"login-text-link\">
                            Already have an account? Sign in here
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <!-- jQuery -->
    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>

    <!-- Select2 JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Enhanced form interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Select2 for country dropdown
            const countrySelect = document.getElementById('country-select') || document.querySelector('select[name*=\"country\"]');
            if (countrySelect) {
                \$(countrySelect).select2({
                    placeholder: 'Choose a country...',
                    allowClear: true,
                    width: '100%',
                    theme: 'bootstrap-5'
                });
            }

            // Country field now uses standard floating label functionality

            const inputs = document.querySelectorAll('.floating-input');

            inputs.forEach(input => {
                // Handle autofill detection
                const checkAutofill = () => {
                    if (input.value !== '') {
                        input.classList.add('has-value');
                    } else {
                        input.classList.remove('has-value');
                    }
                };

                input.addEventListener('input', checkAutofill);
                input.addEventListener('blur', checkAutofill);

                // Initial check
                setTimeout(checkAutofill, 100);
            });

            // Profile picture preview - try multiple possible IDs
            const profileInput = document.getElementById('profile-picture-input') ||
                                document.getElementById('registration_form_profilePicture') ||
                                document.querySelector('input[type=\"file\"]');
            const profilePreview = document.getElementById('profile-picture-preview');
            const profileImg = document.getElementById('profile-preview-img');

            if (profileInput && profilePreview && profileImg) {
                profileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            profileImg.src = e.target.result;
                            profilePreview.classList.add('show');
                        };
                        reader.readAsDataURL(file);
                    } else {
                        profilePreview.classList.remove('show');
                    }
                });
            }
        });
    </script>
</body>
</html>
", "security/register_new.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\security\\register_new.html.twig");
    }
}
