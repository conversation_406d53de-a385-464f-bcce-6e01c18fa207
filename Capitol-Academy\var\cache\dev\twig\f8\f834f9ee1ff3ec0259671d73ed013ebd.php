<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* remote_course/index.html.twig */
class __TwigTemplate_cbb3b0195b18758b3e918268a3d435ca extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'body' => [$this, 'block_body'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "remote_course/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "remote_course/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Remote Courses - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Explore our comprehensive collection of remote trading courses. Learn at your own pace with expert-led video content covering technical analysis, risk management, and advanced trading strategies.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 8
        yield "    <!-- Hero Section -->
    <section class=\"hero-section bg-gradient-primary text-white py-5\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">Remote Trading Courses</h1>
                    <p class=\"lead mb-4\">Master trading from anywhere with our comprehensive video-based courses. Learn at your own pace with expert instructors and structured content.</p>
                    <div class=\"d-flex flex-wrap gap-3\">
                        <div class=\"stat-item\">
                            <span class=\"h4 fw-bold\">";
        // line 17
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 17, $this->source); })())), "html", null, true);
        yield "</span>
                            <small class=\"d-block\">Available Courses</small>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"h4 fw-bold\">";
        // line 21
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 21, $this->source); })())), "html", null, true);
        yield "</span>
                            <small class=\"d-block\">Expert Instructors</small>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"h4 fw-bold\">";
        // line 25
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 25, $this->source); })())), "html", null, true);
        yield "</span>
                            <small class=\"d-block\">Categories</small>
                        </div>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <img src=\"/images/remote-learning-hero.svg\" alt=\"Remote Learning\" class=\"img-fluid\" style=\"max-height: 300px;\">
                </div>
            </div>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class=\"filters-section py-4 bg-light\">
        <div class=\"container\">
            <form method=\"GET\" class=\"row g-3 align-items-end\">
                <!-- Search -->
                <div class=\"col-md-3\">
                    <label for=\"search\" class=\"form-label\">Search Courses</label>
                    <input type=\"text\" class=\"form-control\" id=\"search\" name=\"search\" 
                           value=\"";
        // line 45
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 45, $this->source); })()), "html", null, true);
        yield "\" placeholder=\"Course title, description...\">
                </div>

                <!-- Category Filter -->
                <div class=\"col-md-2\">
                    <label for=\"category\" class=\"form-label\">Category</label>
                    <select class=\"form-select\" id=\"category\" name=\"category\">
                        <option value=\"\">All Categories</option>
                        ";
        // line 53
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 53, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 54
            yield "                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["category"], "html", null, true);
            yield "\" ";
            yield ((((isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 54, $this->source); })()) == $context["category"])) ? ("selected") : (""));
            yield ">
                                ";
            // line 55
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["category"], "html", null, true);
            yield "
                            </option>
                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 58
        yield "                    </select>
                </div>

                <!-- Level Filter -->
                <div class=\"col-md-2\">
                    <label for=\"level\" class=\"form-label\">Level</label>
                    <select class=\"form-select\" id=\"level\" name=\"level\">
                        <option value=\"\">All Levels</option>
                        ";
        // line 66
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["levels"]) || array_key_exists("levels", $context) ? $context["levels"] : (function () { throw new RuntimeError('Variable "levels" does not exist.', 66, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["level"]) {
            // line 67
            yield "                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["level"], "html", null, true);
            yield "\" ";
            yield ((((isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 67, $this->source); })()) == $context["level"])) ? ("selected") : (""));
            yield ">
                                ";
            // line 68
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["level"], "html", null, true);
            yield "
                            </option>
                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['level'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 71
        yield "                    </select>
                </div>

                <!-- Instructor Filter -->
                <div class=\"col-md-2\">
                    <label for=\"instructor\" class=\"form-label\">Instructor</label>
                    <select class=\"form-select\" id=\"instructor\" name=\"instructor\">
                        <option value=\"\">All Instructors</option>
                        ";
        // line 79
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["instructors"]) || array_key_exists("instructors", $context) ? $context["instructors"] : (function () { throw new RuntimeError('Variable "instructors" does not exist.', 79, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["instructor"]) {
            // line 80
            yield "                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["instructor"], "html", null, true);
            yield "\" ";
            yield ((((isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 80, $this->source); })()) == $context["instructor"])) ? ("selected") : (""));
            yield ">
                                ";
            // line 81
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["instructor"], "html", null, true);
            yield "
                            </option>
                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['instructor'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 84
        yield "                    </select>
                </div>

                <!-- Sort -->
                <div class=\"col-md-2\">
                    <label for=\"sort\" class=\"form-label\">Sort By</label>
                    <select class=\"form-select\" id=\"sort\" name=\"sort\">
                        <option value=\"newest\" ";
        // line 91
        yield ((((isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 91, $this->source); })()) == "newest")) ? ("selected") : (""));
        yield ">Newest First</option>
                        <option value=\"popular\" ";
        // line 92
        yield ((((isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 92, $this->source); })()) == "popular")) ? ("selected") : (""));
        yield ">Most Popular</option>
                        <option value=\"price_low\" ";
        // line 93
        yield ((((isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 93, $this->source); })()) == "price_low")) ? ("selected") : (""));
        yield ">Price: Low to High</option>
                        <option value=\"price_high\" ";
        // line 94
        yield ((((isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 94, $this->source); })()) == "price_high")) ? ("selected") : (""));
        yield ">Price: High to Low</option>
                    </select>
                </div>

                <!-- Submit -->
                <div class=\"col-md-1\">
                    <button type=\"submit\" class=\"btn btn-primary w-100\">
                        <i class=\"fas fa-search\"></i>
                    </button>
                </div>
            </form>

            <!-- Active Filters -->
            ";
        // line 107
        if (((((isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 107, $this->source); })()) || (isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 107, $this->source); })())) || (isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 107, $this->source); })())) || (isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 107, $this->source); })()))) {
            // line 108
            yield "                <div class=\"active-filters mt-3\">
                    <span class=\"text-muted me-2\">Active filters:</span>
                    ";
            // line 110
            if ((($tmp = (isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 110, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 111
                yield "                        <span class=\"badge bg-primary me-2\">
                            Search: ";
                // line 112
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 112, $this->source); })()), "html", null, true);
                yield "
                            <a href=\"";
                // line 113
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_index", ["category" => (isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 113, $this->source); })()), "level" => (isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 113, $this->source); })()), "instructor" => (isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 113, $this->source); })()), "sort" => (isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 113, $this->source); })())]), "html", null, true);
                yield "\" class=\"text-white ms-1\">×</a>
                        </span>
                    ";
            }
            // line 116
            yield "                    ";
            if ((($tmp = (isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 116, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 117
                yield "                        <span class=\"badge bg-info me-2\">
                            Category: ";
                // line 118
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 118, $this->source); })()), "html", null, true);
                yield "
                            <a href=\"";
                // line 119
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_index", ["search" => (isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 119, $this->source); })()), "level" => (isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 119, $this->source); })()), "instructor" => (isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 119, $this->source); })()), "sort" => (isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 119, $this->source); })())]), "html", null, true);
                yield "\" class=\"text-white ms-1\">×</a>
                        </span>
                    ";
            }
            // line 122
            yield "                    ";
            if ((($tmp = (isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 122, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 123
                yield "                        <span class=\"badge bg-success me-2\">
                            Level: ";
                // line 124
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 124, $this->source); })()), "html", null, true);
                yield "
                            <a href=\"";
                // line 125
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_index", ["search" => (isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 125, $this->source); })()), "category" => (isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 125, $this->source); })()), "instructor" => (isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 125, $this->source); })()), "sort" => (isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 125, $this->source); })())]), "html", null, true);
                yield "\" class=\"text-white ms-1\">×</a>
                        </span>
                    ";
            }
            // line 128
            yield "                    ";
            if ((($tmp = (isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 128, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 129
                yield "                        <span class=\"badge bg-warning me-2\">
                            Instructor: ";
                // line 130
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 130, $this->source); })()), "html", null, true);
                yield "
                            <a href=\"";
                // line 131
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_index", ["search" => (isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 131, $this->source); })()), "category" => (isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 131, $this->source); })()), "level" => (isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 131, $this->source); })()), "sort" => (isset($context["current_sort"]) || array_key_exists("current_sort", $context) ? $context["current_sort"] : (function () { throw new RuntimeError('Variable "current_sort" does not exist.', 131, $this->source); })())]), "html", null, true);
                yield "\" class=\"text-white ms-1\">×</a>
                        </span>
                    ";
            }
            // line 134
            yield "                    <a href=\"";
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_index");
            yield "\" class=\"btn btn-sm btn-outline-secondary\">Clear All</a>
                </div>
            ";
        }
        // line 137
        yield "        </div>
    </section>

    <!-- Courses Grid -->
    <section class=\"courses-section py-5\">
        <div class=\"container\">
            ";
        // line 143
        if (Twig\Extension\CoreExtension::testEmpty((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 143, $this->source); })()))) {
            // line 144
            yield "                <div class=\"text-center py-5\">
                    <i class=\"fas fa-video fa-4x text-muted mb-3\"></i>
                    <h3 class=\"text-muted mb-3\">No courses found</h3>
                    ";
            // line 147
            if (((((isset($context["current_search"]) || array_key_exists("current_search", $context) ? $context["current_search"] : (function () { throw new RuntimeError('Variable "current_search" does not exist.', 147, $this->source); })()) || (isset($context["current_category"]) || array_key_exists("current_category", $context) ? $context["current_category"] : (function () { throw new RuntimeError('Variable "current_category" does not exist.', 147, $this->source); })())) || (isset($context["current_level"]) || array_key_exists("current_level", $context) ? $context["current_level"] : (function () { throw new RuntimeError('Variable "current_level" does not exist.', 147, $this->source); })())) || (isset($context["current_instructor"]) || array_key_exists("current_instructor", $context) ? $context["current_instructor"] : (function () { throw new RuntimeError('Variable "current_instructor" does not exist.', 147, $this->source); })()))) {
                // line 148
                yield "                        <p class=\"text-muted mb-4\">Try adjusting your filters or search terms.</p>
                        <a href=\"";
                // line 149
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_index");
                yield "\" class=\"btn btn-primary\">View All Courses</a>
                    ";
            } else {
                // line 151
                yield "                        <p class=\"text-muted mb-4\">Check back soon for new remote courses.</p>
                    ";
            }
            // line 153
            yield "                </div>
            ";
        } else {
            // line 155
            yield "                <div class=\"row\">
                    ";
            // line 156
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 156, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
                // line 157
                yield "                        <div class=\"col-lg-4 col-md-6 mb-4\">
                            <div class=\"card course-card h-100 shadow-sm\">
                                <!-- Course Thumbnail -->
                                <div class=\"position-relative\">
                                    <img src=\"";
                // line 161
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailUrl", [], "any", false, false, false, 161), "html", null, true);
                yield "\" class=\"card-img-top\" alt=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 161), "html", null, true);
                yield "\" style=\"height: 200px; object-fit: cover;\">
                                    <div class=\"position-absolute top-0 end-0 m-2\">
                                        <span class=\"badge bg-primary\">";
                // line 163
                yield ((CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 163)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 163), "html", null, true)) : ("All Levels"));
                yield "</span>
                                    </div>
                                    <div class=\"position-absolute bottom-0 start-0 m-2\">
                                        <span class=\"badge bg-dark\">";
                // line 166
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "totalVideos", [], "any", false, false, false, 166), "html", null, true);
                yield " videos</span>
                                    </div>
                                </div>

                                <div class=\"card-body d-flex flex-column\">
                                    <!-- Course Category -->
                                    <div class=\"mb-2\">
                                        <span class=\"badge bg-info\">";
                // line 173
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 173), "html", null, true);
                yield "</span>
                                    </div>

                                    <!-- Course Title -->
                                    <h5 class=\"card-title\">
                                        <a href=\"";
                // line 178
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_show", ["code" => CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 178)]), "html", null, true);
                yield "\" class=\"text-decoration-none\">
                                            ";
                // line 179
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 179), "html", null, true);
                yield "
                                        </a>
                                    </h5>

                                    <!-- Course Description -->
                                    <p class=\"card-text text-muted flex-grow-1\">
                                        ";
                // line 185
                yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 185)) > 120)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 185), 0, 120) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 185), "html", null, true)));
                yield "
                                    </p>

                                    <!-- Course Meta -->
                                    <div class=\"course-meta mb-3\">
                                        ";
                // line 190
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "instructor", [], "any", false, false, false, 190)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 191
                    yield "                                            <small class=\"text-muted d-block\">
                                                <i class=\"fas fa-user-tie me-1\"></i>";
                    // line 192
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "instructor", [], "any", false, false, false, 192), "html", null, true);
                    yield "
                                            </small>
                                        ";
                }
                // line 195
                yield "                                        <small class=\"text-muted d-block\">
                                            <i class=\"fas fa-clock me-1\"></i>";
                // line 196
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "formattedDuration", [], "any", false, false, false, 196), "html", null, true);
                yield "
                                        </small>
                                        <small class=\"text-muted d-block\">
                                            <i class=\"fas fa-list me-1\"></i>";
                // line 199
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["course"], "chapters", [], "any", false, false, false, 199), "count", [], "any", false, false, false, 199), "html", null, true);
                yield " chapters
                                        </small>
                                    </div>

                                    <!-- Course Price and Action -->
                                    <div class=\"d-flex justify-content-between align-items-center\">
                                        <div class=\"price\">
                                            <span class=\"h5 text-primary fw-bold\">";
                // line 206
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "formattedPrice", [], "any", false, false, false, 206), "html", null, true);
                yield "</span>
                                        </div>
                                        <div class=\"actions\">
                                            <a href=\"";
                // line 209
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_remote_course_show", ["code" => CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 209)]), "html", null, true);
                yield "\" class=\"btn btn-primary btn-sm\">
                                                View Course
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 218
            yield "                </div>
            ";
        }
        // line 220
        yield "        </div>
    </section>

    <!-- Call to Action -->
    <section class=\"cta-section bg-primary text-white py-5\">
        <div class=\"container text-center\">
            <h2 class=\"mb-3\">Ready to Start Your Trading Journey?</h2>
            <p class=\"lead mb-4\">Join thousands of successful traders who have learned with Capitol Academy's remote courses.</p>
            <div class=\"row justify-content-center\">
                <div class=\"col-md-8\">
                    <div class=\"d-flex justify-content-center gap-3 flex-wrap\">
                        <a href=\"";
        // line 231
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_register");
        yield "\" class=\"btn btn-light btn-lg\">
                            <i class=\"fas fa-user-plus me-2\"></i>Create Free Account
                        </a>
                        <a href=\"";
        // line 234
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
        yield "\" class=\"btn btn-outline-light btn-lg\">
                            <i class=\"fas fa-envelope me-2\"></i>Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 244
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 245
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        .hero-section {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
        }
        
        .stat-item {
            text-align: center;
            padding: 0 1rem;
        }
        
        .course-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }
        
        .course-card .card-title a {
            color: #011a2d;
        }
        
        .course-card .card-title a:hover {
            color: #a90418;
        }
        
        .active-filters .badge a {
            text-decoration: none;
        }
        
        .filters-section {
            border-bottom: 1px solid #dee2e6;
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 283
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 284
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        // Auto-submit form when filters change
        document.addEventListener('DOMContentLoaded', function() {
            const filterSelects = document.querySelectorAll('#category, #level, #instructor, #sort');
            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    this.form.submit();
                });
            });
        });
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "remote_course/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  645 => 284,  632 => 283,  583 => 245,  570 => 244,  550 => 234,  544 => 231,  531 => 220,  527 => 218,  512 => 209,  506 => 206,  496 => 199,  490 => 196,  487 => 195,  481 => 192,  478 => 191,  476 => 190,  468 => 185,  459 => 179,  455 => 178,  447 => 173,  437 => 166,  431 => 163,  424 => 161,  418 => 157,  414 => 156,  411 => 155,  407 => 153,  403 => 151,  398 => 149,  395 => 148,  393 => 147,  388 => 144,  386 => 143,  378 => 137,  371 => 134,  365 => 131,  361 => 130,  358 => 129,  355 => 128,  349 => 125,  345 => 124,  342 => 123,  339 => 122,  333 => 119,  329 => 118,  326 => 117,  323 => 116,  317 => 113,  313 => 112,  310 => 111,  308 => 110,  304 => 108,  302 => 107,  286 => 94,  282 => 93,  278 => 92,  274 => 91,  265 => 84,  256 => 81,  249 => 80,  245 => 79,  235 => 71,  226 => 68,  219 => 67,  215 => 66,  205 => 58,  196 => 55,  189 => 54,  185 => 53,  174 => 45,  151 => 25,  144 => 21,  137 => 17,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Remote Courses - Capitol Academy{% endblock %}

{% block meta_description %}Explore our comprehensive collection of remote trading courses. Learn at your own pace with expert-led video content covering technical analysis, risk management, and advanced trading strategies.{% endblock %}

{% block body %}
    <!-- Hero Section -->
    <section class=\"hero-section bg-gradient-primary text-white py-5\">
        <div class=\"container\">
            <div class=\"row align-items-center\">
                <div class=\"col-lg-8\">
                    <h1 class=\"display-4 fw-bold mb-3\">Remote Trading Courses</h1>
                    <p class=\"lead mb-4\">Master trading from anywhere with our comprehensive video-based courses. Learn at your own pace with expert instructors and structured content.</p>
                    <div class=\"d-flex flex-wrap gap-3\">
                        <div class=\"stat-item\">
                            <span class=\"h4 fw-bold\">{{ courses|length }}</span>
                            <small class=\"d-block\">Available Courses</small>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"h4 fw-bold\">{{ instructors|length }}</span>
                            <small class=\"d-block\">Expert Instructors</small>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"h4 fw-bold\">{{ categories|length }}</span>
                            <small class=\"d-block\">Categories</small>
                        </div>
                    </div>
                </div>
                <div class=\"col-lg-4 text-center\">
                    <img src=\"/images/remote-learning-hero.svg\" alt=\"Remote Learning\" class=\"img-fluid\" style=\"max-height: 300px;\">
                </div>
            </div>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class=\"filters-section py-4 bg-light\">
        <div class=\"container\">
            <form method=\"GET\" class=\"row g-3 align-items-end\">
                <!-- Search -->
                <div class=\"col-md-3\">
                    <label for=\"search\" class=\"form-label\">Search Courses</label>
                    <input type=\"text\" class=\"form-control\" id=\"search\" name=\"search\" 
                           value=\"{{ current_search }}\" placeholder=\"Course title, description...\">
                </div>

                <!-- Category Filter -->
                <div class=\"col-md-2\">
                    <label for=\"category\" class=\"form-label\">Category</label>
                    <select class=\"form-select\" id=\"category\" name=\"category\">
                        <option value=\"\">All Categories</option>
                        {% for category in categories %}
                            <option value=\"{{ category }}\" {{ current_category == category ? 'selected' : '' }}>
                                {{ category }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Level Filter -->
                <div class=\"col-md-2\">
                    <label for=\"level\" class=\"form-label\">Level</label>
                    <select class=\"form-select\" id=\"level\" name=\"level\">
                        <option value=\"\">All Levels</option>
                        {% for level in levels %}
                            <option value=\"{{ level }}\" {{ current_level == level ? 'selected' : '' }}>
                                {{ level }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Instructor Filter -->
                <div class=\"col-md-2\">
                    <label for=\"instructor\" class=\"form-label\">Instructor</label>
                    <select class=\"form-select\" id=\"instructor\" name=\"instructor\">
                        <option value=\"\">All Instructors</option>
                        {% for instructor in instructors %}
                            <option value=\"{{ instructor }}\" {{ current_instructor == instructor ? 'selected' : '' }}>
                                {{ instructor }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Sort -->
                <div class=\"col-md-2\">
                    <label for=\"sort\" class=\"form-label\">Sort By</label>
                    <select class=\"form-select\" id=\"sort\" name=\"sort\">
                        <option value=\"newest\" {{ current_sort == 'newest' ? 'selected' : '' }}>Newest First</option>
                        <option value=\"popular\" {{ current_sort == 'popular' ? 'selected' : '' }}>Most Popular</option>
                        <option value=\"price_low\" {{ current_sort == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value=\"price_high\" {{ current_sort == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                    </select>
                </div>

                <!-- Submit -->
                <div class=\"col-md-1\">
                    <button type=\"submit\" class=\"btn btn-primary w-100\">
                        <i class=\"fas fa-search\"></i>
                    </button>
                </div>
            </form>

            <!-- Active Filters -->
            {% if current_search or current_category or current_level or current_instructor %}
                <div class=\"active-filters mt-3\">
                    <span class=\"text-muted me-2\">Active filters:</span>
                    {% if current_search %}
                        <span class=\"badge bg-primary me-2\">
                            Search: {{ current_search }}
                            <a href=\"{{ path('app_remote_course_index', {category: current_category, level: current_level, instructor: current_instructor, sort: current_sort}) }}\" class=\"text-white ms-1\">×</a>
                        </span>
                    {% endif %}
                    {% if current_category %}
                        <span class=\"badge bg-info me-2\">
                            Category: {{ current_category }}
                            <a href=\"{{ path('app_remote_course_index', {search: current_search, level: current_level, instructor: current_instructor, sort: current_sort}) }}\" class=\"text-white ms-1\">×</a>
                        </span>
                    {% endif %}
                    {% if current_level %}
                        <span class=\"badge bg-success me-2\">
                            Level: {{ current_level }}
                            <a href=\"{{ path('app_remote_course_index', {search: current_search, category: current_category, instructor: current_instructor, sort: current_sort}) }}\" class=\"text-white ms-1\">×</a>
                        </span>
                    {% endif %}
                    {% if current_instructor %}
                        <span class=\"badge bg-warning me-2\">
                            Instructor: {{ current_instructor }}
                            <a href=\"{{ path('app_remote_course_index', {search: current_search, category: current_category, level: current_level, sort: current_sort}) }}\" class=\"text-white ms-1\">×</a>
                        </span>
                    {% endif %}
                    <a href=\"{{ path('app_remote_course_index') }}\" class=\"btn btn-sm btn-outline-secondary\">Clear All</a>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Courses Grid -->
    <section class=\"courses-section py-5\">
        <div class=\"container\">
            {% if courses is empty %}
                <div class=\"text-center py-5\">
                    <i class=\"fas fa-video fa-4x text-muted mb-3\"></i>
                    <h3 class=\"text-muted mb-3\">No courses found</h3>
                    {% if current_search or current_category or current_level or current_instructor %}
                        <p class=\"text-muted mb-4\">Try adjusting your filters or search terms.</p>
                        <a href=\"{{ path('app_remote_course_index') }}\" class=\"btn btn-primary\">View All Courses</a>
                    {% else %}
                        <p class=\"text-muted mb-4\">Check back soon for new remote courses.</p>
                    {% endif %}
                </div>
            {% else %}
                <div class=\"row\">
                    {% for course in courses %}
                        <div class=\"col-lg-4 col-md-6 mb-4\">
                            <div class=\"card course-card h-100 shadow-sm\">
                                <!-- Course Thumbnail -->
                                <div class=\"position-relative\">
                                    <img src=\"{{ course.thumbnailUrl }}\" class=\"card-img-top\" alt=\"{{ course.title }}\" style=\"height: 200px; object-fit: cover;\">
                                    <div class=\"position-absolute top-0 end-0 m-2\">
                                        <span class=\"badge bg-primary\">{{ course.level ?: 'All Levels' }}</span>
                                    </div>
                                    <div class=\"position-absolute bottom-0 start-0 m-2\">
                                        <span class=\"badge bg-dark\">{{ course.totalVideos }} videos</span>
                                    </div>
                                </div>

                                <div class=\"card-body d-flex flex-column\">
                                    <!-- Course Category -->
                                    <div class=\"mb-2\">
                                        <span class=\"badge bg-info\">{{ course.category }}</span>
                                    </div>

                                    <!-- Course Title -->
                                    <h5 class=\"card-title\">
                                        <a href=\"{{ path('app_remote_course_show', {code: course.code}) }}\" class=\"text-decoration-none\">
                                            {{ course.title }}
                                        </a>
                                    </h5>

                                    <!-- Course Description -->
                                    <p class=\"card-text text-muted flex-grow-1\">
                                        {{ course.description|length > 120 ? course.description|slice(0, 120) ~ '...' : course.description }}
                                    </p>

                                    <!-- Course Meta -->
                                    <div class=\"course-meta mb-3\">
                                        {% if course.instructor %}
                                            <small class=\"text-muted d-block\">
                                                <i class=\"fas fa-user-tie me-1\"></i>{{ course.instructor }}
                                            </small>
                                        {% endif %}
                                        <small class=\"text-muted d-block\">
                                            <i class=\"fas fa-clock me-1\"></i>{{ course.formattedDuration }}
                                        </small>
                                        <small class=\"text-muted d-block\">
                                            <i class=\"fas fa-list me-1\"></i>{{ course.chapters.count }} chapters
                                        </small>
                                    </div>

                                    <!-- Course Price and Action -->
                                    <div class=\"d-flex justify-content-between align-items-center\">
                                        <div class=\"price\">
                                            <span class=\"h5 text-primary fw-bold\">{{ course.formattedPrice }}</span>
                                        </div>
                                        <div class=\"actions\">
                                            <a href=\"{{ path('app_remote_course_show', {code: course.code}) }}\" class=\"btn btn-primary btn-sm\">
                                                View Course
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Call to Action -->
    <section class=\"cta-section bg-primary text-white py-5\">
        <div class=\"container text-center\">
            <h2 class=\"mb-3\">Ready to Start Your Trading Journey?</h2>
            <p class=\"lead mb-4\">Join thousands of successful traders who have learned with Capitol Academy's remote courses.</p>
            <div class=\"row justify-content-center\">
                <div class=\"col-md-8\">
                    <div class=\"d-flex justify-content-center gap-3 flex-wrap\">
                        <a href=\"{{ path('app_register') }}\" class=\"btn btn-light btn-lg\">
                            <i class=\"fas fa-user-plus me-2\"></i>Create Free Account
                        </a>
                        <a href=\"{{ path('app_contact') }}\" class=\"btn btn-outline-light btn-lg\">
                            <i class=\"fas fa-envelope me-2\"></i>Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .hero-section {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
        }
        
        .stat-item {
            text-align: center;
            padding: 0 1rem;
        }
        
        .course-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }
        
        .course-card .card-title a {
            color: #011a2d;
        }
        
        .course-card .card-title a:hover {
            color: #a90418;
        }
        
        .active-filters .badge a {
            text-decoration: none;
        }
        
        .filters-section {
            border-bottom: 1px solid #dee2e6;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Auto-submit form when filters change
        document.addEventListener('DOMContentLoaded', function() {
            const filterSelects = document.querySelectorAll('#category, #level, #instructor, #sort');
            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    this.form.submit();
                });
            });
        });
    </script>
{% endblock %}
", "remote_course/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\remote_course\\index.html.twig");
    }
}
