<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/plans/preview.html.twig */
class __TwigTemplate_2ecb11480891c3d073a5eb240cd17b47 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'stylesheets' => [$this, 'block_stylesheets'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/preview.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/preview.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Plan Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Plan Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\">Plans</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-layer-group mr-3\" style=\"font-size: 2rem;\"></i>
                        Plan Details: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 37, $this->source); })()), "code", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Plan Button (Icon Only) -->
                        <a href=\"";
        // line 43
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plan_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 43, $this->source); })()), "code", [], "any", false, false, false, 43)]), "html", null, true);
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Plan\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Plan Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Plan Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Plans Button -->
                        <a href=\"";
        // line 63
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plans");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Plan Code and Title Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Plan Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                        Plan Code
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 91
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 91, $this->source); })()), "code", [], "any", false, false, false, 91), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                        Plan Title
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 104
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 104, $this->source); })()), "title", [], "any", false, false, false, 104), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Details Row -->
                        <div class=\"row print-four-column clearfix\">
                            <!-- Duration -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-clock text-primary mr-1\"></i>
                                        Duration (Days)
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 120
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["plan"] ?? null), "duration", [], "any", true, true, false, 120) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 120, $this->source); })()), "duration", [], "any", false, false, false, 120)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 120, $this->source); })()), "duration", [], "any", false, false, false, 120), "html", null, true)) : ("Not specified"));
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Price -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                        Price (USD)
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        \$";
        // line 133
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["plan"] ?? null), "price", [], "any", true, true, false, 133) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 133, $this->source); })()), "price", [], "any", false, false, false, 133)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 133, $this->source); })()), "price", [], "any", false, false, false, 133), "html", null, true)) : ("0.00"));
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 146
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 146, $this->source); })()), "isActive", [], "any", false, false, false, 146)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 147
            yield "                                            <span class=\"badge bg-success\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Active</span>
                                        ";
        } else {
            // line 149
            yield "                                            <span class=\"badge bg-secondary\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Inactive</span>
                                        ";
        }
        // line 151
        yield "                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        ";
        // line 163
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 163, $this->source); })()), "createdAt", [], "any", false, false, false, 163), "M d, Y h:i A"), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Description -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-file-alt text-primary mr-1\"></i>
                                Description
                            </label>
                            <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                ";
        // line 176
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["plan"] ?? null), "description", [], "any", true, true, false, 176) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 176, $this->source); })()), "description", [], "any", false, false, false, 176)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 176, $this->source); })()), "description", [], "any", false, false, false, 176), "html", null, true)) : ("No description provided"));
        yield "
                            </div>
                        </div>

                        <!-- Included Videos -->
                        ";
        // line 181
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 181, $this->source); })()), "videos", [], "any", false, false, false, 181)) > 0)) {
            // line 182
            yield "                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-video text-primary mr-1\"></i>
                                Included Videos (";
            // line 185
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 185, $this->source); })()), "videos", [], "any", false, false, false, 185)), "html", null, true);
            yield ")
                            </label>
                            <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                <div class=\"row\">
                                    ";
            // line 189
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 189, $this->source); })()), "videos", [], "any", false, false, false, 189));
            foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
                // line 190
                yield "                                    <div class=\"col-md-6 mb-3\">
                                        <a href=\"";
                // line 191
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_show", ["id" => CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 191)]), "html", null, true);
                yield "\"
                                           class=\"video-item-link text-decoration-none\">
                                            <div class=\"d-flex align-items-center p-3 video-item-card\"
                                                 style=\"background: white; border-radius: 8px; border: 1px solid #e9ecef; transition: all 0.3s ease;\">
                                                <div class=\"me-3\">
                                                    ";
                // line 196
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "thumbnail", [], "any", false, false, false, 196)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 197
                    yield "                                                        <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, $context["video"], "thumbnail", [], "any", false, false, false, 197))), "html", null, true);
                    yield "\"
                                                             alt=\"";
                    // line 198
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 198), "html", null, true);
                    yield "\"
                                                             class=\"video-thumbnail\"
                                                             style=\"width: 60px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #dee2e6;\">
                                                    ";
                } else {
                    // line 202
                    yield "                                                        <div class=\"video-placeholder d-flex align-items-center justify-content-center\"
                                                             style=\"width: 60px; height: 40px; background: #f8f9fa; border-radius: 4px; border: 1px solid #dee2e6;\">
                                                            <i class=\"fas fa-play-circle\" style=\"font-size: 1.2rem; color: #6c757d;\"></i>
                                                        </div>
                                                    ";
                }
                // line 207
                yield "                                                </div>
                                                <div class=\"flex-grow-1\">
                                                    <h6 class=\"mb-1 video-title\" style=\"color: #011a2d; font-weight: 600;\">";
                // line 209
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 209), "html", null, true);
                yield "</h6>
                                                    <div class=\"d-flex align-items-center justify-content-between\">
                                                        <small class=\"text-muted\">
                                                            ";
                // line 212
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 212)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 213
                    yield "                                                                <i class=\"fas fa-tag me-1\"></i>";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 213), "html", null, true);
                    yield "
                                                            ";
                }
                // line 215
                yield "                                                        </small>
                                                        <div>
                                                            ";
                // line 217
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isFree", [], "any", false, false, false, 217)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 218
                    yield "                                                                <span class=\"badge bg-success\">Free</span>
                                                            ";
                } else {
                    // line 220
                    yield "                                                                <span class=\"badge bg-primary\">\$";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "price", [], "any", false, false, false, 220), "html", null, true);
                    yield "</span>
                                                            ";
                }
                // line 222
                yield "                                                        </div>
                                                    </div>
                                                </div>
                                                <div class=\"ms-2\">
                                                    <i class=\"fas fa-external-link-alt\" style=\"color: #6c757d; font-size: 0.8rem;\"></i>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 232
            yield "                                </div>
                            </div>
                        </div>
                        ";
        }
        // line 236
        yield "
                    </div>
                </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 244
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 245
        yield "<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Enhanced Video Item Styling */
.video-item-link {
    display: block;
    color: inherit;
}

.video-item-card {
    cursor: pointer;
    border: 1px solid #e9ecef !important;
    background: white !important;
}

.video-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
    border-color: #011a2d !important;
}

.video-item-card:hover .video-title {
    color: #011a2d !important;
}

.video-item-card:hover .fas.fa-external-link-alt {
    color: #011a2d !important;
}

.video-thumbnail {
    transition: all 0.3s ease;
}

.video-item-card:hover .video-thumbnail {
    transform: scale(1.05);
}

.video-placeholder {
    transition: all 0.3s ease;
}

.video-item-card:hover .video-placeholder {
    background: #e9ecef !important;
}

.video-item-card:hover .video-placeholder i {
    color: #011a2d !important;
}

/* Print Styles */
@media print {
    .btn, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .enhanced-display-field {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/plans/preview.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  525 => 245,  512 => 244,  495 => 236,  489 => 232,  474 => 222,  468 => 220,  464 => 218,  462 => 217,  458 => 215,  452 => 213,  450 => 212,  444 => 209,  440 => 207,  433 => 202,  426 => 198,  421 => 197,  419 => 196,  411 => 191,  408 => 190,  404 => 189,  397 => 185,  392 => 182,  390 => 181,  382 => 176,  366 => 163,  352 => 151,  348 => 149,  344 => 147,  342 => 146,  326 => 133,  310 => 120,  291 => 104,  275 => 91,  244 => 63,  221 => 43,  212 => 37,  202 => 29,  192 => 25,  189 => 24,  185 => 23,  182 => 22,  172 => 18,  169 => 17,  165 => 16,  161 => 14,  148 => 13,  135 => 10,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Plan Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Plan Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_plans') }}\">Plans</a></li>
<li class=\"breadcrumb-item active\">{{ plan.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-layer-group mr-3\" style=\"font-size: 2rem;\"></i>
                        Plan Details: {{ plan.code }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Plan Button (Icon Only) -->
                        <a href=\"{{ path('admin_plan_edit', {'code': plan.code}) }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Plan\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Plan Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Plan Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Plans Button -->
                        <a href=\"{{ path('admin_plans') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Plan Code and Title Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Plan Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                        Plan Code
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ plan.code }}
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                        Plan Title
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ plan.title }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Details Row -->
                        <div class=\"row print-four-column clearfix\">
                            <!-- Duration -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-clock text-primary mr-1\"></i>
                                        Duration (Days)
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ plan.duration ?? 'Not specified' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Price -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                        Price (USD)
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        \${{ plan.price ?? '0.00' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {% if plan.isActive %}
                                            <span class=\"badge bg-success\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Active</span>
                                        {% else %}
                                            <span class=\"badge bg-secondary\" style=\"font-size: 0.75rem; padding: 0.4rem 0.6rem;\">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class=\"col-md-3\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;\">
                                        {{ plan.createdAt|date('M d, Y h:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Description -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-file-alt text-primary mr-1\"></i>
                                Description
                            </label>
                            <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                {{ plan.description ?? 'No description provided' }}
                            </div>
                        </div>

                        <!-- Included Videos -->
                        {% if plan.videos|length > 0 %}
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-video text-primary mr-1\"></i>
                                Included Videos ({{ plan.videos|length }})
                            </label>
                            <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                <div class=\"row\">
                                    {% for video in plan.videos %}
                                    <div class=\"col-md-6 mb-3\">
                                        <a href=\"{{ path('admin_video_show', {'id': video.id}) }}\"
                                           class=\"video-item-link text-decoration-none\">
                                            <div class=\"d-flex align-items-center p-3 video-item-card\"
                                                 style=\"background: white; border-radius: 8px; border: 1px solid #e9ecef; transition: all 0.3s ease;\">
                                                <div class=\"me-3\">
                                                    {% if video.thumbnail %}
                                                        <img src=\"{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}\"
                                                             alt=\"{{ video.title }}\"
                                                             class=\"video-thumbnail\"
                                                             style=\"width: 60px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #dee2e6;\">
                                                    {% else %}
                                                        <div class=\"video-placeholder d-flex align-items-center justify-content-center\"
                                                             style=\"width: 60px; height: 40px; background: #f8f9fa; border-radius: 4px; border: 1px solid #dee2e6;\">
                                                            <i class=\"fas fa-play-circle\" style=\"font-size: 1.2rem; color: #6c757d;\"></i>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                <div class=\"flex-grow-1\">
                                                    <h6 class=\"mb-1 video-title\" style=\"color: #011a2d; font-weight: 600;\">{{ video.title }}</h6>
                                                    <div class=\"d-flex align-items-center justify-content-between\">
                                                        <small class=\"text-muted\">
                                                            {% if video.category %}
                                                                <i class=\"fas fa-tag me-1\"></i>{{ video.category }}
                                                            {% endif %}
                                                        </small>
                                                        <div>
                                                            {% if video.isFree %}
                                                                <span class=\"badge bg-success\">Free</span>
                                                            {% else %}
                                                                <span class=\"badge bg-primary\">\${{ video.price }}</span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class=\"ms-2\">
                                                    <i class=\"fas fa-external-link-alt\" style=\"color: #6c757d; font-size: 0.8rem;\"></i>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                    </div>
                </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Enhanced Video Item Styling */
.video-item-link {
    display: block;
    color: inherit;
}

.video-item-card {
    cursor: pointer;
    border: 1px solid #e9ecef !important;
    background: white !important;
}

.video-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
    border-color: #011a2d !important;
}

.video-item-card:hover .video-title {
    color: #011a2d !important;
}

.video-item-card:hover .fas.fa-external-link-alt {
    color: #011a2d !important;
}

.video-thumbnail {
    transition: all 0.3s ease;
}

.video-item-card:hover .video-thumbnail {
    transform: scale(1.05);
}

.video-placeholder {
    transition: all 0.3s ease;
}

.video-item-card:hover .video-placeholder {
    background: #e9ecef !important;
}

.video-item-card:hover .video-placeholder i {
    color: #011a2d !important;
}

/* Print Styles */
@media print {
    .btn, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .enhanced-display-field {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}
</style>
{% endblock %}
", "admin/plans/preview.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\plans\\preview.html.twig");
    }
}
