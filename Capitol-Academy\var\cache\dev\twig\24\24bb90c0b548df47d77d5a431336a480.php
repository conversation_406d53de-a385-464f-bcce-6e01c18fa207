<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/security/create_admin.html.twig */
class __TwigTemplate_b1d05c931145539d6de9e748b0ca0bc7 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/security/create_admin.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/security/create_admin.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"utf-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">
    <title>Create Admin - Capitol Academy</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
    <!-- AdminLTE -->
    <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css\">
    
    <style>
        .register-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .register-box {
            width: 450px;
        }
        .card {
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
            border-radius: 15px;
        }
        .card-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 2rem 1rem;
        }
        .card-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 300;
        }
        .card-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 25px;
            padding: 0.75rem 1.25rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .input-group-text {
            border-radius: 25px 0 0 25px;
            border: 2px solid #e9ecef;
            border-right: none;
            background-color: #f8f9fa;
        }
        .input-group .form-control {
            border-radius: 0 25px 25px 0;
            border-left: none;
        }
        .alert {
            border-radius: 10px;
        }
        .logo {
            max-width: 60px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class=\"hold-transition register-page\">
<div class=\"register-box\">
    <div class=\"card card-outline card-primary\">
        <div class=\"card-header\">
            <img src=\"";
        // line 90
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"logo\" onerror=\"this.style.display='none'\">
            <h1>Capitol Academy</h1>
            <p class=\"mb-0\">Create Admin Account</p>
        </div>
        <div class=\"card-body\">
            ";
        // line 95
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 95, $this->source); })()), "flashes", [], "any", false, false, false, 95));
        foreach ($context['_seq'] as $context["type"] => $context["messages"]) {
            // line 96
            yield "                ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable($context["messages"]);
            foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
                // line 97
                yield "                    <div class=\"alert alert-";
                yield ((($context["type"] == "error")) ? ("danger") : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["type"], "html", null, true)));
                yield " alert-dismissible fade show\" role=\"alert\">
                        ";
                // line 98
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
                yield "
                        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
                    </div>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 102
            yield "            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['type'], $context['messages'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 103
        yield "
            <p class=\"login-box-msg\">Create the first admin account</p>

            <form method=\"post\">
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-user-circle\"></i></span>
                    <input type=\"text\"
                           class=\"form-control\"
                           name=\"username\"
                           placeholder=\"Username\"
                           required
                           pattern=\"[a-zA-Z0-9_]+\"
                           title=\"Username can only contain letters, numbers, and underscores\">
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-user\"></i></span>
                    <input type=\"text\"
                           class=\"form-control\"
                           name=\"firstName\"
                           placeholder=\"First Name\"
                           required>
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-user\"></i></span>
                    <input type=\"text\"
                           class=\"form-control\"
                           name=\"lastName\"
                           placeholder=\"Last Name\"
                           required>
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-envelope\"></i></span>
                    <input type=\"email\"
                           class=\"form-control\"
                           name=\"email\"
                           placeholder=\"Email\"
                           required>
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-lock\"></i></span>
                    <input type=\"password\" 
                           class=\"form-control\" 
                           name=\"password\" 
                           placeholder=\"Password\" 
                           required 
                           minlength=\"6\">
                </div>
                
                <div class=\"row\">
                    <div class=\"col-12\">
                        <button type=\"submit\" class=\"btn btn-primary btn-block w-100\">
                            <i class=\"fas fa-user-plus me-2\"></i>
                            Create Admin Account
                        </button>
                    </div>
                </div>
            </form>

            <div class=\"text-center mt-4\">
                <a href=\"";
        // line 162
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_login");
        yield "\" class=\"text-muted\">
                    <i class=\"fas fa-arrow-left me-1\"></i>
                    Back to Login
                </a>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>
<!-- Bootstrap 5 -->
<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
<!-- AdminLTE App -->
<script src=\"https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js\"></script>

<script>
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        \$('.alert').fadeOut('slow');
    }, 5000);
</script>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/security/create_admin.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  238 => 162,  177 => 103,  171 => 102,  161 => 98,  156 => 97,  151 => 96,  147 => 95,  139 => 90,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"utf-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">
    <title>Create Admin - Capitol Academy</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
    <!-- AdminLTE -->
    <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css\">
    
    <style>
        .register-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .register-box {
            width: 450px;
        }
        .card {
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
            border-radius: 15px;
        }
        .card-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 2rem 1rem;
        }
        .card-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 300;
        }
        .card-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 25px;
            padding: 0.75rem 1.25rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .input-group-text {
            border-radius: 25px 0 0 25px;
            border: 2px solid #e9ecef;
            border-right: none;
            background-color: #f8f9fa;
        }
        .input-group .form-control {
            border-radius: 0 25px 25px 0;
            border-left: none;
        }
        .alert {
            border-radius: 10px;
        }
        .logo {
            max-width: 60px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class=\"hold-transition register-page\">
<div class=\"register-box\">
    <div class=\"card card-outline card-primary\">
        <div class=\"card-header\">
            <img src=\"{{ asset('images/logos/logo.png') }}\" alt=\"Capitol Academy\" class=\"logo\" onerror=\"this.style.display='none'\">
            <h1>Capitol Academy</h1>
            <p class=\"mb-0\">Create Admin Account</p>
        </div>
        <div class=\"card-body\">
            {% for type, messages in app.flashes %}
                {% for message in messages %}
                    <div class=\"alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show\" role=\"alert\">
                        {{ message }}
                        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
                    </div>
                {% endfor %}
            {% endfor %}

            <p class=\"login-box-msg\">Create the first admin account</p>

            <form method=\"post\">
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-user-circle\"></i></span>
                    <input type=\"text\"
                           class=\"form-control\"
                           name=\"username\"
                           placeholder=\"Username\"
                           required
                           pattern=\"[a-zA-Z0-9_]+\"
                           title=\"Username can only contain letters, numbers, and underscores\">
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-user\"></i></span>
                    <input type=\"text\"
                           class=\"form-control\"
                           name=\"firstName\"
                           placeholder=\"First Name\"
                           required>
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-user\"></i></span>
                    <input type=\"text\"
                           class=\"form-control\"
                           name=\"lastName\"
                           placeholder=\"Last Name\"
                           required>
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-envelope\"></i></span>
                    <input type=\"email\"
                           class=\"form-control\"
                           name=\"email\"
                           placeholder=\"Email\"
                           required>
                </div>
                <div class=\"input-group mb-3\">
                    <span class=\"input-group-text\"><i class=\"fas fa-lock\"></i></span>
                    <input type=\"password\" 
                           class=\"form-control\" 
                           name=\"password\" 
                           placeholder=\"Password\" 
                           required 
                           minlength=\"6\">
                </div>
                
                <div class=\"row\">
                    <div class=\"col-12\">
                        <button type=\"submit\" class=\"btn btn-primary btn-block w-100\">
                            <i class=\"fas fa-user-plus me-2\"></i>
                            Create Admin Account
                        </button>
                    </div>
                </div>
            </form>

            <div class=\"text-center mt-4\">
                <a href=\"{{ path('admin_login') }}\" class=\"text-muted\">
                    <i class=\"fas fa-arrow-left me-1\"></i>
                    Back to Login
                </a>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>
<!-- Bootstrap 5 -->
<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
<!-- AdminLTE App -->
<script src=\"https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js\"></script>

<script>
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        \$('.alert').fadeOut('slow');
    }, 5000);
</script>
</body>
</html>
", "admin/security/create_admin.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\security\\create_admin.html.twig");
    }
}
