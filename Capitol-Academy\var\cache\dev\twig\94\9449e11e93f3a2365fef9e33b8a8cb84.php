<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/market_analysis/edit.html.twig */
class __TwigTemplate_24d77db5a74e3012b82c4278f584f01d extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Market Analysis - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Market Analysis";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_index");
        yield "\">Market Analysis</a></li>
<li class=\"breadcrumb-item active\">Edit Analysis</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Market Analysis
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Market Analysis Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Market Analysis
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("market_analysis_edit"), "html", null, true);
        yield "\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Title and Asset Type Row -->
                            <div class=\"row\">
                                <!-- Analysis Title -->
                                <div class=\"col-md-8\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-heading text-primary mr-1\"></i>
                                            Analysis Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"";
        // line 75
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 75, $this->source); })()), "title", [], "any", false, false, false, 75), "html", null, true);
        yield "\"
                                               placeholder=\"Enter analysis title...\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide an analysis title.
                                        </div>
                                    </div>
                                </div>

                                <!-- Asset Type -->
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label for=\"asset_type\" class=\"form-label\">
                                            <i class=\"fas fa-chart-bar text-primary mr-1\"></i>
                                            Asset Type <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"asset_type\"
                                                name=\"asset_type\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; padding: 0.75rem 2.25rem 0.75rem 0.75rem;\">
                                            <option value=\"\">Select Asset Type</option>
                                            <option value=\"stocks\" ";
        // line 99
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 99, $this->source); })()), "assetType", [], "any", false, false, false, 99) == "stocks")) ? ("selected") : (""));
        yield ">Stocks</option>
                                            <option value=\"forex\" ";
        // line 100
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 100, $this->source); })()), "assetType", [], "any", false, false, false, 100) == "forex")) ? ("selected") : (""));
        yield ">Forex</option>
                                            <option value=\"crypto\" ";
        // line 101
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 101, $this->source); })()), "assetType", [], "any", false, false, false, 101) == "crypto")) ? ("selected") : (""));
        yield ">Crypto</option>
                                            <option value=\"crude_oil\" ";
        // line 102
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 102, $this->source); })()), "assetType", [], "any", false, false, false, 102) == "crude_oil")) ? ("selected") : (""));
        yield ">Crude Oil</option>
                                            <option value=\"gold\" ";
        // line 103
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 103, $this->source); })()), "assetType", [], "any", false, false, false, 103) == "gold")) ? ("selected") : (""));
        yield ">Gold</option>
                                            <option value=\"commodities\" ";
        // line 104
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 104, $this->source); })()), "assetType", [], "any", false, false, false, 104) == "commodities")) ? ("selected") : (""));
        yield ">Commodities</option>
                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select an asset type.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Author and Date Row -->
                            <div class=\"row\">
                                <!-- Author -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"author\" class=\"form-label\">
                                            <i class=\"fas fa-user-edit text-primary mr-1\"></i>
                                            Author
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"author\"
                                               name=\"author\"
                                               value=\"";
        // line 126
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 126, $this->source); })()), "author", [], "any", false, false, false, 126), "html", null, true);
        yield "\"
                                               placeholder=\"Enter author name (leave empty for 'Capitol Academy Analyst')...\"
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Leave empty to automatically set as \"Capitol Academy Analyst\"
                                        </small>
                                    </div>
                                </div>

                                <!-- Published Date -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"publish_date\" class=\"form-label\">
                                            <i class=\"fas fa-calendar text-primary mr-1\"></i>
                                            Published Date <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"date\"
                                               class=\"form-control enhanced-field\"
                                               id=\"publish_date\"
                                               name=\"publish_date\"
                                               value=\"";
        // line 147
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 147, $this->source); })()), "publishDate", [], "any", false, false, false, 147), "Y-m-d"), "html", null, true);
        yield "\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a published date.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Excerpt -->
                            <div class=\"form-group\">
                                <label for=\"excerpt\" class=\"form-label\">
                                    <i class=\"fas fa-quote-left text-primary mr-1\"></i>
                                    Excerpt <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"excerpt\"
                                          name=\"excerpt\"
                                          rows=\"3\"
                                          placeholder=\"Enter a brief summary of the analysis...\"
                                          required
                                          maxlength=\"500\"
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">";
        // line 170
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 170, $this->source); })()), "excerpt", [], "any", false, false, false, 170), "html", null, true);
        yield "</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide an excerpt.
                                </div>
                            </div>

                            <!-- Content -->
                            <div class=\"form-group\">
                                <label for=\"content\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Content <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field rich-text-editor\"
                                          id=\"content\"
                                          name=\"content\"
                                          rows=\"12\"
                                          placeholder=\"Enter detailed analysis content...\"
                                          required
                                          style=\"min-height: 300px; font-size: 1rem; border: 2px solid #ced4da;\">";
        // line 188
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 188, $this->source); })()), "content", [], "any", false, false, false, 188), "html", null, true);
        yield "</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide content for the analysis.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Use the rich text editor to format your content with colors, fonts, and styling.
                                </small>
                            </div>

                            <!-- Image Upload Row -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnailImage\" class=\"form-label\">
                                            <i class=\"fas fa-image text-primary mr-1\"></i>
                                            Thumbnail Image
                                        </label>

                                        ";
        // line 207
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 207, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 207)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 208
            yield "                                            <div class=\"text-center mb-3\">
                                                <img src=\"";
            // line 209
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/market_analysis/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 209, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 209))), "html", null, true);
            yield "\"
                                                     alt=\"Current Thumbnail\"
                                                     class=\"img-fluid rounded\"
                                                     style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                                                <div class=\"text-muted mt-1\">Current thumbnail</div>
                                            </div>
                                        ";
        }
        // line 216
        yield "
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"thumbnailImage\"
                                               name=\"thumbnailImage\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Upload a new image to replace the current one. Recommended size: 400x300px.
                                        </small>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"featured_image\" class=\"form-label\">
                                            <i class=\"fas fa-star text-primary mr-1\"></i>
                                            Featured Image
                                        </label>

                                        ";
        // line 237
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 237, $this->source); })()), "featuredImage", [], "any", false, false, false, 237)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 238
            yield "                                            <div class=\"text-center mb-3\">
                                                <img src=\"";
            // line 239
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/market_analysis/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 239, $this->source); })()), "featuredImage", [], "any", false, false, false, 239))), "html", null, true);
            yield "\"
                                                     alt=\"Current Featured\"
                                                     class=\"img-fluid rounded\"
                                                     style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                                                <div class=\"text-muted mt-1\">Current featured image</div>
                                            </div>
                                        ";
        }
        // line 246
        yield "
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"featured_image\"
                                               name=\"featured_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Upload a new image to replace the current one. Recommended size: 1200x400px.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

            <!-- Form Footer -->
            <div class=\"card-footer bg-light border-top-0\" style=\"border-radius: 0 0 15px 15px; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"";
        // line 265
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_index");
        yield "\" class=\"btn btn-outline-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <div>
                        <a href=\"";
        // line 269
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_show_readable", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["article"]) || array_key_exists("article", $context) ? $context["article"] : (function () { throw new RuntimeError('Variable "article" does not exist.', 269, $this->source); })()), "slug", [], "any", false, false, false, 269)]), "html", null, true);
        yield "\" class=\"btn btn-outline-info me-2\">
                            <i class=\"fas fa-eye me-2\"></i>View Details
                        </a>
                        <button type=\"submit\" class=\"btn\" id=\"submitBtn\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 0.75rem 2rem; border-radius: 8px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\" onmouseover=\"this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.4)';\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(40, 167, 69, 0.3)';\">
                            <i class=\"fas fa-save me-2\"></i>Update Analysis
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>


<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    font-weight: 500;
    color: #011a2d;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

.enhanced-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Form Label Styling */
.form-label {
    color: #1e3c72;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #007bff;
    margin-right: 0.5rem;
}

/* Form Group Spacing */
.form-group {
    margin-bottom: 1.5rem;
}

/* Button Styling */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Card Styling */
.card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Image Preview Styling */
.img-fluid {
    transition: all 0.3s ease;
}

.img-fluid:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Form Group Focus Effect */
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

/* Professional Button Styling */
.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    const submitBtn = document.getElementById('submitBtn');

    // Form validation and submission
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating...';
        }
        form.classList.add('was-validated');
    });

    // Enhanced field focus effects
    const enhancedFields = document.querySelectorAll('.enhanced-field');
    enhancedFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group').classList.add('focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group').classList.remove('focused');
        });
    });

    // Image preview functionality
    const imageInputs = document.querySelectorAll('input[type=\"file\"]');
    imageInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Find existing preview or create new one
                    let preview = input.parentNode.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'image-preview text-center mt-2';
                        input.parentNode.insertBefore(preview, input.nextSibling);
                    }

                    preview.innerHTML = `
                        <img src=\"\${e.target.result}\"
                             alt=\"Preview\"
                             class=\"img-fluid rounded\"
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #28a745;\">
                        <div class=\"text-success mt-1\">New image selected</div>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    });

    function resetSubmitButton() {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Analysis';
    }

    // Reset button state on page load
    resetSubmitButton();
});
</script>

<!-- Include CKEditor for rich text editing -->
<script src=\"https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js\"></script>

<script>
// Initialize CKEditor for content field
let contentEditor;
ClassicEditor
    .create(document.querySelector('#content'), {
        toolbar: {
            items: [
                'undo', 'redo',
                '|', 'heading',
                '|', 'fontSize', 'fontFamily',
                '|', 'bold', 'italic', 'underline',
                '|', 'fontColor', 'fontBackgroundColor',
                '|', 'link', 'insertTable', 'blockQuote',
                '|', 'bulletedList', 'numberedList', 'outdent', 'indent',
                '|', 'alignment',
                '|', 'removeFormat'
            ]
        },
        fontSize: {
            options: [
                8, 9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 44, 48, 54, 60, 72
            ],
            supportAllValues: true
        },
        fontColor: {
            colors: [
                {
                    color: 'hsl(0, 0%, 0%)',
                    label: 'Black'
                },
                {
                    color: 'hsl(0, 0%, 30%)',
                    label: 'Dim grey'
                },
                {
                    color: 'hsl(0, 0%, 60%)',
                    label: 'Grey'
                },
                {
                    color: 'hsl(0, 0%, 90%)',
                    label: 'Light grey'
                },
                {
                    color: 'hsl(0, 0%, 100%)',
                    label: 'White',
                    hasBorder: true
                },
                {
                    color: 'hsl(0, 75%, 60%)',
                    label: 'Red'
                },
                {
                    color: 'hsl(30, 75%, 60%)',
                    label: 'Orange'
                },
                {
                    color: 'hsl(60, 75%, 60%)',
                    label: 'Yellow'
                },
                {
                    color: 'hsl(90, 75%, 60%)',
                    label: 'Light green'
                },
                {
                    color: 'hsl(120, 75%, 60%)',
                    label: 'Green'
                },
                {
                    color: 'hsl(150, 75%, 60%)',
                    label: 'Aquamarine'
                },
                {
                    color: 'hsl(180, 75%, 60%)',
                    label: 'Turquoise'
                },
                {
                    color: 'hsl(210, 75%, 60%)',
                    label: 'Light blue'
                },
                {
                    color: 'hsl(240, 75%, 60%)',
                    label: 'Blue'
                },
                {
                    color: 'hsl(270, 75%, 60%)',
                    label: 'Purple'
                },
                // Capitol Academy brand colors
                {
                    color: '#011a2d',
                    label: 'Capitol Navy'
                },
                {
                    color: '#a90418',
                    label: 'Capitol Red'
                }
            ]
        },
        fontBackgroundColor: {
            colors: [
                {
                    color: 'hsl(0, 0%, 0%)',
                    label: 'Black'
                },
                {
                    color: 'hsl(0, 0%, 30%)',
                    label: 'Dim grey'
                },
                {
                    color: 'hsl(0, 0%, 60%)',
                    label: 'Grey'
                },
                {
                    color: 'hsl(0, 0%, 90%)',
                    label: 'Light grey'
                },
                {
                    color: 'hsl(0, 0%, 100%)',
                    label: 'White',
                    hasBorder: true
                },
                {
                    color: 'hsl(0, 75%, 60%)',
                    label: 'Red'
                },
                {
                    color: 'hsl(30, 75%, 60%)',
                    label: 'Orange'
                },
                {
                    color: 'hsl(60, 75%, 60%)',
                    label: 'Yellow'
                },
                {
                    color: 'hsl(90, 75%, 60%)',
                    label: 'Light green'
                },
                {
                    color: 'hsl(120, 75%, 60%)',
                    label: 'Green'
                },
                {
                    color: 'hsl(150, 75%, 60%)',
                    label: 'Aquamarine'
                },
                {
                    color: 'hsl(180, 75%, 60%)',
                    label: 'Turquoise'
                },
                {
                    color: 'hsl(210, 75%, 60%)',
                    label: 'Light blue'
                },
                {
                    color: 'hsl(240, 75%, 60%)',
                    label: 'Blue'
                },
                {
                    color: 'hsl(270, 75%, 60%)',
                    label: 'Purple'
                }
            ]
        },
        fontFamily: {
            options: [
                'default',
                'Arial, Helvetica, sans-serif',
                'Courier New, Courier, monospace',
                'Georgia, serif',
                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                'Tahoma, Geneva, sans-serif',
                'Times New Roman, Times, serif',
                'Trebuchet MS, Helvetica, sans-serif',
                'Verdana, Geneva, sans-serif'
            ],
            supportAllValues: true
        },
        heading: {
            options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
            ]
        },
        table: {
            contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
        }
    })
    .then(editor => {
        contentEditor = editor;
        // Set minimum height
        editor.editing.view.change(writer => {
            writer.setStyle('min-height', '300px', editor.editing.view.document.getRoot());
        });
    })
    .catch(error => {
        console.error('CKEditor initialization error:', error);
    });

// Update form submission to include CKEditor content
document.querySelector('form').addEventListener('submit', function(event) {
    if (contentEditor) {
        document.querySelector('#content').value = contentEditor.getData();
    }
});
</script>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/market_analysis/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  498 => 269,  491 => 265,  470 => 246,  460 => 239,  457 => 238,  455 => 237,  432 => 216,  422 => 209,  419 => 208,  417 => 207,  395 => 188,  374 => 170,  348 => 147,  324 => 126,  299 => 104,  295 => 103,  291 => 102,  287 => 101,  283 => 100,  279 => 99,  252 => 75,  231 => 57,  214 => 43,  198 => 29,  188 => 25,  185 => 24,  181 => 23,  178 => 22,  168 => 18,  165 => 17,  161 => 16,  157 => 14,  144 => 13,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Market Analysis - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Market Analysis{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_market_analysis_index') }}\">Market Analysis</a></li>
<li class=\"breadcrumb-item active\">Edit Analysis</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Market Analysis
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Market Analysis Button -->
                        <a href=\"{{ path('admin_market_analysis_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Market Analysis
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('market_analysis_edit') }}\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Title and Asset Type Row -->
                            <div class=\"row\">
                                <!-- Analysis Title -->
                                <div class=\"col-md-8\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-heading text-primary mr-1\"></i>
                                            Analysis Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"{{ article.title }}\"
                                               placeholder=\"Enter analysis title...\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide an analysis title.
                                        </div>
                                    </div>
                                </div>

                                <!-- Asset Type -->
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label for=\"asset_type\" class=\"form-label\">
                                            <i class=\"fas fa-chart-bar text-primary mr-1\"></i>
                                            Asset Type <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"asset_type\"
                                                name=\"asset_type\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; padding: 0.75rem 2.25rem 0.75rem 0.75rem;\">
                                            <option value=\"\">Select Asset Type</option>
                                            <option value=\"stocks\" {{ article.assetType == 'stocks' ? 'selected' : '' }}>Stocks</option>
                                            <option value=\"forex\" {{ article.assetType == 'forex' ? 'selected' : '' }}>Forex</option>
                                            <option value=\"crypto\" {{ article.assetType == 'crypto' ? 'selected' : '' }}>Crypto</option>
                                            <option value=\"crude_oil\" {{ article.assetType == 'crude_oil' ? 'selected' : '' }}>Crude Oil</option>
                                            <option value=\"gold\" {{ article.assetType == 'gold' ? 'selected' : '' }}>Gold</option>
                                            <option value=\"commodities\" {{ article.assetType == 'commodities' ? 'selected' : '' }}>Commodities</option>
                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select an asset type.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Author and Date Row -->
                            <div class=\"row\">
                                <!-- Author -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"author\" class=\"form-label\">
                                            <i class=\"fas fa-user-edit text-primary mr-1\"></i>
                                            Author
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"author\"
                                               name=\"author\"
                                               value=\"{{ article.author }}\"
                                               placeholder=\"Enter author name (leave empty for 'Capitol Academy Analyst')...\"
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Leave empty to automatically set as \"Capitol Academy Analyst\"
                                        </small>
                                    </div>
                                </div>

                                <!-- Published Date -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"publish_date\" class=\"form-label\">
                                            <i class=\"fas fa-calendar text-primary mr-1\"></i>
                                            Published Date <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"date\"
                                               class=\"form-control enhanced-field\"
                                               id=\"publish_date\"
                                               name=\"publish_date\"
                                               value=\"{{ article.publishDate|date('Y-m-d') }}\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a published date.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Excerpt -->
                            <div class=\"form-group\">
                                <label for=\"excerpt\" class=\"form-label\">
                                    <i class=\"fas fa-quote-left text-primary mr-1\"></i>
                                    Excerpt <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"excerpt\"
                                          name=\"excerpt\"
                                          rows=\"3\"
                                          placeholder=\"Enter a brief summary of the analysis...\"
                                          required
                                          maxlength=\"500\"
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">{{ article.excerpt }}</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide an excerpt.
                                </div>
                            </div>

                            <!-- Content -->
                            <div class=\"form-group\">
                                <label for=\"content\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Content <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field rich-text-editor\"
                                          id=\"content\"
                                          name=\"content\"
                                          rows=\"12\"
                                          placeholder=\"Enter detailed analysis content...\"
                                          required
                                          style=\"min-height: 300px; font-size: 1rem; border: 2px solid #ced4da;\">{{ article.content }}</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide content for the analysis.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Use the rich text editor to format your content with colors, fonts, and styling.
                                </small>
                            </div>

                            <!-- Image Upload Row -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnailImage\" class=\"form-label\">
                                            <i class=\"fas fa-image text-primary mr-1\"></i>
                                            Thumbnail Image
                                        </label>

                                        {% if article.thumbnailImage %}
                                            <div class=\"text-center mb-3\">
                                                <img src=\"{{ asset('uploads/market_analysis/' ~ article.thumbnailImage) }}\"
                                                     alt=\"Current Thumbnail\"
                                                     class=\"img-fluid rounded\"
                                                     style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                                                <div class=\"text-muted mt-1\">Current thumbnail</div>
                                            </div>
                                        {% endif %}

                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"thumbnailImage\"
                                               name=\"thumbnailImage\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Upload a new image to replace the current one. Recommended size: 400x300px.
                                        </small>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"featured_image\" class=\"form-label\">
                                            <i class=\"fas fa-star text-primary mr-1\"></i>
                                            Featured Image
                                        </label>

                                        {% if article.featuredImage %}
                                            <div class=\"text-center mb-3\">
                                                <img src=\"{{ asset('uploads/market_analysis/' ~ article.featuredImage) }}\"
                                                     alt=\"Current Featured\"
                                                     class=\"img-fluid rounded\"
                                                     style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;\">
                                                <div class=\"text-muted mt-1\">Current featured image</div>
                                            </div>
                                        {% endif %}

                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"featured_image\"
                                               name=\"featured_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Upload a new image to replace the current one. Recommended size: 1200x400px.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

            <!-- Form Footer -->
            <div class=\"card-footer bg-light border-top-0\" style=\"border-radius: 0 0 15px 15px; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"{{ path('admin_market_analysis_index') }}\" class=\"btn btn-outline-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <div>
                        <a href=\"{{ path('admin_market_analysis_show_readable', {slug: article.slug}) }}\" class=\"btn btn-outline-info me-2\">
                            <i class=\"fas fa-eye me-2\"></i>View Details
                        </a>
                        <button type=\"submit\" class=\"btn\" id=\"submitBtn\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 0.75rem 2rem; border-radius: 8px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\" onmouseover=\"this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.4)';\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(40, 167, 69, 0.3)';\">
                            <i class=\"fas fa-save me-2\"></i>Update Analysis
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>


<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    font-weight: 500;
    color: #011a2d;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

.enhanced-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Form Label Styling */
.form-label {
    color: #1e3c72;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #007bff;
    margin-right: 0.5rem;
}

/* Form Group Spacing */
.form-group {
    margin-bottom: 1.5rem;
}

/* Button Styling */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Card Styling */
.card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Image Preview Styling */
.img-fluid {
    transition: all 0.3s ease;
}

.img-fluid:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Form Group Focus Effect */
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

/* Professional Button Styling */
.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    const submitBtn = document.getElementById('submitBtn');

    // Form validation and submission
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating...';
        }
        form.classList.add('was-validated');
    });

    // Enhanced field focus effects
    const enhancedFields = document.querySelectorAll('.enhanced-field');
    enhancedFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.closest('.form-group').classList.add('focused');
        });

        field.addEventListener('blur', function() {
            this.closest('.form-group').classList.remove('focused');
        });
    });

    // Image preview functionality
    const imageInputs = document.querySelectorAll('input[type=\"file\"]');
    imageInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Find existing preview or create new one
                    let preview = input.parentNode.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'image-preview text-center mt-2';
                        input.parentNode.insertBefore(preview, input.nextSibling);
                    }

                    preview.innerHTML = `
                        <img src=\"\${e.target.result}\"
                             alt=\"Preview\"
                             class=\"img-fluid rounded\"
                             style=\"max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #28a745;\">
                        <div class=\"text-success mt-1\">New image selected</div>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    });

    function resetSubmitButton() {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Analysis';
    }

    // Reset button state on page load
    resetSubmitButton();
});
</script>

<!-- Include CKEditor for rich text editing -->
<script src=\"https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js\"></script>

<script>
// Initialize CKEditor for content field
let contentEditor;
ClassicEditor
    .create(document.querySelector('#content'), {
        toolbar: {
            items: [
                'undo', 'redo',
                '|', 'heading',
                '|', 'fontSize', 'fontFamily',
                '|', 'bold', 'italic', 'underline',
                '|', 'fontColor', 'fontBackgroundColor',
                '|', 'link', 'insertTable', 'blockQuote',
                '|', 'bulletedList', 'numberedList', 'outdent', 'indent',
                '|', 'alignment',
                '|', 'removeFormat'
            ]
        },
        fontSize: {
            options: [
                8, 9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 44, 48, 54, 60, 72
            ],
            supportAllValues: true
        },
        fontColor: {
            colors: [
                {
                    color: 'hsl(0, 0%, 0%)',
                    label: 'Black'
                },
                {
                    color: 'hsl(0, 0%, 30%)',
                    label: 'Dim grey'
                },
                {
                    color: 'hsl(0, 0%, 60%)',
                    label: 'Grey'
                },
                {
                    color: 'hsl(0, 0%, 90%)',
                    label: 'Light grey'
                },
                {
                    color: 'hsl(0, 0%, 100%)',
                    label: 'White',
                    hasBorder: true
                },
                {
                    color: 'hsl(0, 75%, 60%)',
                    label: 'Red'
                },
                {
                    color: 'hsl(30, 75%, 60%)',
                    label: 'Orange'
                },
                {
                    color: 'hsl(60, 75%, 60%)',
                    label: 'Yellow'
                },
                {
                    color: 'hsl(90, 75%, 60%)',
                    label: 'Light green'
                },
                {
                    color: 'hsl(120, 75%, 60%)',
                    label: 'Green'
                },
                {
                    color: 'hsl(150, 75%, 60%)',
                    label: 'Aquamarine'
                },
                {
                    color: 'hsl(180, 75%, 60%)',
                    label: 'Turquoise'
                },
                {
                    color: 'hsl(210, 75%, 60%)',
                    label: 'Light blue'
                },
                {
                    color: 'hsl(240, 75%, 60%)',
                    label: 'Blue'
                },
                {
                    color: 'hsl(270, 75%, 60%)',
                    label: 'Purple'
                },
                // Capitol Academy brand colors
                {
                    color: '#011a2d',
                    label: 'Capitol Navy'
                },
                {
                    color: '#a90418',
                    label: 'Capitol Red'
                }
            ]
        },
        fontBackgroundColor: {
            colors: [
                {
                    color: 'hsl(0, 0%, 0%)',
                    label: 'Black'
                },
                {
                    color: 'hsl(0, 0%, 30%)',
                    label: 'Dim grey'
                },
                {
                    color: 'hsl(0, 0%, 60%)',
                    label: 'Grey'
                },
                {
                    color: 'hsl(0, 0%, 90%)',
                    label: 'Light grey'
                },
                {
                    color: 'hsl(0, 0%, 100%)',
                    label: 'White',
                    hasBorder: true
                },
                {
                    color: 'hsl(0, 75%, 60%)',
                    label: 'Red'
                },
                {
                    color: 'hsl(30, 75%, 60%)',
                    label: 'Orange'
                },
                {
                    color: 'hsl(60, 75%, 60%)',
                    label: 'Yellow'
                },
                {
                    color: 'hsl(90, 75%, 60%)',
                    label: 'Light green'
                },
                {
                    color: 'hsl(120, 75%, 60%)',
                    label: 'Green'
                },
                {
                    color: 'hsl(150, 75%, 60%)',
                    label: 'Aquamarine'
                },
                {
                    color: 'hsl(180, 75%, 60%)',
                    label: 'Turquoise'
                },
                {
                    color: 'hsl(210, 75%, 60%)',
                    label: 'Light blue'
                },
                {
                    color: 'hsl(240, 75%, 60%)',
                    label: 'Blue'
                },
                {
                    color: 'hsl(270, 75%, 60%)',
                    label: 'Purple'
                }
            ]
        },
        fontFamily: {
            options: [
                'default',
                'Arial, Helvetica, sans-serif',
                'Courier New, Courier, monospace',
                'Georgia, serif',
                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                'Tahoma, Geneva, sans-serif',
                'Times New Roman, Times, serif',
                'Trebuchet MS, Helvetica, sans-serif',
                'Verdana, Geneva, sans-serif'
            ],
            supportAllValues: true
        },
        heading: {
            options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
            ]
        },
        table: {
            contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
        }
    })
    .then(editor => {
        contentEditor = editor;
        // Set minimum height
        editor.editing.view.change(writer => {
            writer.setStyle('min-height', '300px', editor.editing.view.document.getRoot());
        });
    })
    .catch(error => {
        console.error('CKEditor initialization error:', error);
    });

// Update form submission to include CKEditor content
document.querySelector('form').addEventListener('submit', function(event) {
    if (contentEditor) {
        document.querySelector('#content').value = contentEditor.getData();
    }
});
</script>

{% endblock %}
", "admin/market_analysis/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\market_analysis\\edit.html.twig");
    }
}
