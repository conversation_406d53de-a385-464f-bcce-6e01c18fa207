<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* checkout/success.html.twig */
class __TwigTemplate_99c7ad6353b0adb9c57fbb5ed61abc4c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "checkout/success.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "checkout/success.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Order Confirmation - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 6
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        .success-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);
        }

        .success-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }

        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
        }

        .success-content {
            padding: 40px;
        }

        .order-details {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
        }

        .detail-value {
            color: #011a2d;
            font-weight: 500;
        }

        .order-items {
            margin-bottom: 30px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            background: white;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #28a745;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #28a745;
        }

        .access-info {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .access-info i {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: #011a2d;
            border-color: #011a2d;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-primary:hover {
            background: #0d2a42;
            border-color: #0d2a42;
        }

        .btn-outline-primary {
            color: #011a2d;
            border-color: #011a2d;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background: #011a2d;
            border-color: #011a2d;
        }

        .next-steps {
            background: #e8f4fd;
            border: 1px solid #b8daff;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .next-steps h6 {
            color: #004085;
            margin-bottom: 15px;
        }

        .next-steps ul {
            margin-bottom: 0;
            color: #004085;
        }

        .payment-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .payment-info h6 {
            color: #856404;
            margin-bottom: 10px;
        }

        .payment-info p {
            color: #856404;
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            .success-section {
                padding: 40px 0;
            }
            
            .success-header {
                padding: 30px 20px;
            }
            
            .success-content {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 217
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 218
        yield "    <section class=\"success-section\">
        <div class=\"container\">
            <div class=\"success-container\">
                <div class=\"success-header\">
                    <div class=\"success-icon\">
                        <i class=\"fas fa-check\"></i>
                    </div>
                    <h1 class=\"mb-3\">Payment Successful!</h1>
                    <p class=\"lead mb-0\">Thank you for your purchase. Your order has been confirmed.</p>
                </div>

                <div class=\"success-content\">
                    <!-- Order Details -->
                    <div class=\"order-details\">
                        <h5 class=\"mb-3\">
                            <i class=\"fas fa-receipt me-2\"></i>Order Details
                        </h5>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Order Number:</span>
                            <span class=\"detail-value\">#";
        // line 238
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 238, $this->source); })()), "orderNumber", [], "any", false, false, false, 238), "html", null, true);
        yield "</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Order Date:</span>
                            <span class=\"detail-value\">";
        // line 243
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 243, $this->source); })()), "completedAt", [], "any", false, false, false, 243), "M d, Y \\a\\t g:i A"), "html", null, true);
        yield "</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Payment Method:</span>
                            <span class=\"detail-value\">";
        // line 248
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 248, $this->source); })()), "paymentGateway", [], "any", false, false, false, 248)), "html", null, true);
        yield "</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Transaction ID:</span>
                            <span class=\"detail-value\">";
        // line 253
        yield ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 253, $this->source); })()), "paypalTransactionId", [], "any", false, false, false, 253)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 253, $this->source); })()), "paypalTransactionId", [], "any", false, false, false, 253), "html", null, true)) : ("Processing..."));
        yield "</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Total Amount:</span>
                            <span class=\"detail-value\">\$";
        // line 258
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 258, $this->source); })()), "totalPrice", [], "any", false, false, false, 258), 2), "html", null, true);
        yield "</span>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class=\"order-items\">
                        <h5 class=\"mb-3\">
                            <i class=\"fas fa-shopping-bag me-2\"></i>Items Purchased
                        </h5>
                        
                        ";
        // line 268
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 268, $this->source); })()), "items", [], "any", false, false, false, 268));
        foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
            // line 269
            yield "                            <div class=\"order-item\">
                                <div class=\"item-info\">
                                    <h6>";
            // line 271
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 271), "html", null, true);
            yield "</h6>
                                    <span class=\"item-type\">";
            // line 272
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), Twig\Extension\CoreExtension::replace(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 272), ["_" => " "])), "html", null, true);
            yield "</span>
                                    ";
            // line 273
            if ((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 273) > 1)) {
                // line 274
                yield "                                        <small class=\"text-muted d-block\">Quantity: ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 274), "html", null, true);
                yield "</small>
                                    ";
            }
            // line 276
            yield "                                </div>
                                <div class=\"item-price\">
                                    \$";
            // line 278
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "subtotal", [], "any", false, false, false, 278), 2), "html", null, true);
            yield "
                                </div>
                            </div>
                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['item'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 282
        yield "                    </div>

                    <!-- Access Information -->
                    <div class=\"access-info\">
                        <i class=\"fas fa-unlock-alt\"></i>
                        <h5 class=\"mb-3\">Your Content is Now Available!</h5>
                        <p class=\"mb-0\">
                            You now have access to all purchased videos and courses. 
                            Start trading immediately by visiting your dashboard or browsing your content.
                        </p>
                    </div>

                    <!-- Next Steps -->
                    <div class=\"next-steps\">
                        <h6>
                            <i class=\"fas fa-list-check me-2\"></i>What's Next?
                        </h6>
                        <ul>
                            <li>Access your purchased videos from the <strong>Premium Videos</strong> section</li>
                            <li>Check your email for order confirmation and access details</li>
                            <li>Visit your user dashboard to track your trading progress</li>
                            <li>Contact our support team if you need assistance with your purchase</li>
                        </ul>
                    </div>

                    <!-- Payment Information -->
                    <div class=\"payment-info\">
                        <h6>
                            <i class=\"fas fa-info-circle me-2\"></i>Payment Information
                        </h6>
                        <p>
                            A confirmation email has been sent to <strong>";
        // line 313
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 313, $this->source); })()), "user", [], "any", false, false, false, 313), "email", [], "any", false, false, false, 313), "html", null, true);
        yield "</strong> 
                            with your receipt and access instructions.
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class=\"action-buttons\">
                        <a href=\"";
        // line 320
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_premium_videos");
        yield "\" class=\"btn btn-primary\">
                            <i class=\"fas fa-play me-2\"></i>Start Trading
                        </a>
                        
                        <a href=\"";
        // line 324
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_user_orders");
        yield "\" class=\"btn btn-outline-primary\">
                            <i class=\"fas fa-history me-2\"></i>View Order History
                        </a>
                        
                        <a href=\"";
        // line 328
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\" class=\"btn btn-outline-primary\">
                            <i class=\"fas fa-home me-2\"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 338
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 339
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        // Auto-refresh page after 30 seconds to update transaction ID if still processing
        ";
        // line 342
        if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["order"]) || array_key_exists("order", $context) ? $context["order"] : (function () { throw new RuntimeError('Variable "order" does not exist.', 342, $this->source); })()), "paypalTransactionId", [], "any", false, false, false, 342)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 343
            yield "            setTimeout(function() {
                location.reload();
            }, 30000);
        ";
        }
        // line 347
        yield "    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "checkout/success.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  547 => 347,  541 => 343,  539 => 342,  532 => 339,  519 => 338,  499 => 328,  492 => 324,  485 => 320,  475 => 313,  442 => 282,  432 => 278,  428 => 276,  422 => 274,  420 => 273,  416 => 272,  412 => 271,  408 => 269,  404 => 268,  391 => 258,  383 => 253,  375 => 248,  367 => 243,  359 => 238,  337 => 218,  324 => 217,  102 => 6,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Order Confirmation - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .success-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);
        }

        .success-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }

        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
        }

        .success-content {
            padding: 40px;
        }

        .order-details {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
        }

        .detail-value {
            color: #011a2d;
            font-weight: 500;
        }

        .order-items {
            margin-bottom: 30px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            background: white;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #28a745;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #28a745;
        }

        .access-info {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .access-info i {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: #011a2d;
            border-color: #011a2d;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-primary:hover {
            background: #0d2a42;
            border-color: #0d2a42;
        }

        .btn-outline-primary {
            color: #011a2d;
            border-color: #011a2d;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background: #011a2d;
            border-color: #011a2d;
        }

        .next-steps {
            background: #e8f4fd;
            border: 1px solid #b8daff;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .next-steps h6 {
            color: #004085;
            margin-bottom: 15px;
        }

        .next-steps ul {
            margin-bottom: 0;
            color: #004085;
        }

        .payment-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .payment-info h6 {
            color: #856404;
            margin-bottom: 10px;
        }

        .payment-info p {
            color: #856404;
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            .success-section {
                padding: 40px 0;
            }
            
            .success-header {
                padding: 30px 20px;
            }
            
            .success-content {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class=\"success-section\">
        <div class=\"container\">
            <div class=\"success-container\">
                <div class=\"success-header\">
                    <div class=\"success-icon\">
                        <i class=\"fas fa-check\"></i>
                    </div>
                    <h1 class=\"mb-3\">Payment Successful!</h1>
                    <p class=\"lead mb-0\">Thank you for your purchase. Your order has been confirmed.</p>
                </div>

                <div class=\"success-content\">
                    <!-- Order Details -->
                    <div class=\"order-details\">
                        <h5 class=\"mb-3\">
                            <i class=\"fas fa-receipt me-2\"></i>Order Details
                        </h5>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Order Number:</span>
                            <span class=\"detail-value\">#{{ order.orderNumber }}</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Order Date:</span>
                            <span class=\"detail-value\">{{ order.completedAt|date('M d, Y \\\\a\\\\t g:i A') }}</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Payment Method:</span>
                            <span class=\"detail-value\">{{ order.paymentGateway|title }}</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Transaction ID:</span>
                            <span class=\"detail-value\">{{ order.paypalTransactionId ?: 'Processing...' }}</span>
                        </div>
                        
                        <div class=\"detail-row\">
                            <span class=\"detail-label\">Total Amount:</span>
                            <span class=\"detail-value\">\${{ order.totalPrice|number_format(2) }}</span>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class=\"order-items\">
                        <h5 class=\"mb-3\">
                            <i class=\"fas fa-shopping-bag me-2\"></i>Items Purchased
                        </h5>
                        
                        {% for item in order.items %}
                            <div class=\"order-item\">
                                <div class=\"item-info\">
                                    <h6>{{ item.title }}</h6>
                                    <span class=\"item-type\">{{ item.type|replace({'_': ' '})|title }}</span>
                                    {% if item.quantity > 1 %}
                                        <small class=\"text-muted d-block\">Quantity: {{ item.quantity }}</small>
                                    {% endif %}
                                </div>
                                <div class=\"item-price\">
                                    \${{ item.subtotal|number_format(2) }}
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    <!-- Access Information -->
                    <div class=\"access-info\">
                        <i class=\"fas fa-unlock-alt\"></i>
                        <h5 class=\"mb-3\">Your Content is Now Available!</h5>
                        <p class=\"mb-0\">
                            You now have access to all purchased videos and courses. 
                            Start trading immediately by visiting your dashboard or browsing your content.
                        </p>
                    </div>

                    <!-- Next Steps -->
                    <div class=\"next-steps\">
                        <h6>
                            <i class=\"fas fa-list-check me-2\"></i>What's Next?
                        </h6>
                        <ul>
                            <li>Access your purchased videos from the <strong>Premium Videos</strong> section</li>
                            <li>Check your email for order confirmation and access details</li>
                            <li>Visit your user dashboard to track your trading progress</li>
                            <li>Contact our support team if you need assistance with your purchase</li>
                        </ul>
                    </div>

                    <!-- Payment Information -->
                    <div class=\"payment-info\">
                        <h6>
                            <i class=\"fas fa-info-circle me-2\"></i>Payment Information
                        </h6>
                        <p>
                            A confirmation email has been sent to <strong>{{ order.user.email }}</strong> 
                            with your receipt and access instructions.
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class=\"action-buttons\">
                        <a href=\"{{ path('app_premium_videos') }}\" class=\"btn btn-primary\">
                            <i class=\"fas fa-play me-2\"></i>Start Trading
                        </a>
                        
                        <a href=\"{{ path('app_user_orders') }}\" class=\"btn btn-outline-primary\">
                            <i class=\"fas fa-history me-2\"></i>View Order History
                        </a>
                        
                        <a href=\"{{ path('app_home') }}\" class=\"btn btn-outline-primary\">
                            <i class=\"fas fa-home me-2\"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Auto-refresh page after 30 seconds to update transaction ID if still processing
        {% if not order.paypalTransactionId %}
            setTimeout(function() {
                location.reload();
            }, 30000);
        {% endif %}
    </script>
{% endblock %}
", "checkout/success.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\checkout\\success.html.twig");
    }
}
