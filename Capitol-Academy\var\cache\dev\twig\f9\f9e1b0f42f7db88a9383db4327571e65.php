<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/market_analysis/_modals.html.twig */
class __TwigTemplate_37cbf6a353cc98862907075e3afcddb6 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/_modals.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/_modals.html.twig"));

        // line 1
        yield "<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">
                    <i class=\"fas fa-toggle-on me-2\"></i>Change Article Status
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-question-circle text-warning\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"fw-bold mb-3\">Change Status</h6>
                <p class=\"text-muted mb-0\">Are you sure you want to change the status of \"<span id=\"statusArticleTitle\" class=\"fw-bold\"></span>\"?</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-secondary px-4\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-warning px-4\" id=\"confirmStatusToggle\">Change Status</button>
            </div>
        </div>
    </div>
</div>

<!-- Featured Toggle Modal -->
<div class=\"modal fade\" id=\"featuredToggleModal\" tabindex=\"-1\" aria-labelledby=\"featuredToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"featuredToggleModalLabel\">
                    <i class=\"fas fa-star me-2\"></i>Toggle Featured Status
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-star text-warning\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"fw-bold mb-3\">Toggle Featured</h6>
                <p class=\"text-muted mb-0\">Are you sure you want to toggle the featured status of \"<span id=\"featuredArticleTitle\" class=\"fw-bold\"></span>\"?</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-secondary px-4\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn px-4\" id=\"confirmFeaturedToggle\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white;\">Toggle Featured</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteConfirmModal\" tabindex=\"-1\" aria-labelledby=\"deleteConfirmModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"deleteConfirmModalLabel\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Confirm Deletion
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-trash-alt text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"fw-bold mb-3\">Delete Article</h6>
                <p class=\"text-muted mb-0\">Are you sure you want to permanently delete \"<span id=\"deleteItemTitle\" class=\"fw-bold\"></span>\"? This action cannot be undone.</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-secondary px-4\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger px-4\" id=\"confirmDelete\">Delete Article</button>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/market_analysis/_modals.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">
                    <i class=\"fas fa-toggle-on me-2\"></i>Change Article Status
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-question-circle text-warning\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"fw-bold mb-3\">Change Status</h6>
                <p class=\"text-muted mb-0\">Are you sure you want to change the status of \"<span id=\"statusArticleTitle\" class=\"fw-bold\"></span>\"?</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-secondary px-4\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-warning px-4\" id=\"confirmStatusToggle\">Change Status</button>
            </div>
        </div>
    </div>
</div>

<!-- Featured Toggle Modal -->
<div class=\"modal fade\" id=\"featuredToggleModal\" tabindex=\"-1\" aria-labelledby=\"featuredToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"featuredToggleModalLabel\">
                    <i class=\"fas fa-star me-2\"></i>Toggle Featured Status
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-star text-warning\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"fw-bold mb-3\">Toggle Featured</h6>
                <p class=\"text-muted mb-0\">Are you sure you want to toggle the featured status of \"<span id=\"featuredArticleTitle\" class=\"fw-bold\"></span>\"?</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-secondary px-4\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn px-4\" id=\"confirmFeaturedToggle\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white;\">Toggle Featured</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteConfirmModal\" tabindex=\"-1\" aria-labelledby=\"deleteConfirmModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"deleteConfirmModalLabel\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Confirm Deletion
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-trash-alt text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"fw-bold mb-3\">Delete Article</h6>
                <p class=\"text-muted mb-0\">Are you sure you want to permanently delete \"<span id=\"deleteItemTitle\" class=\"fw-bold\"></span>\"? This action cannot be undone.</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-secondary px-4\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger px-4\" id=\"confirmDelete\">Delete Article</button>
            </div>
        </div>
    </div>
</div>
", "admin/market_analysis/_modals.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\market_analysis\\_modals.html.twig");
    }
}
