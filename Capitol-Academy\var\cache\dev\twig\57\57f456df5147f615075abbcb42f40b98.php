<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/contacts/preview.html.twig */
class __TwigTemplate_d2e3803bfb4c68687ccc5b6d4ff6ede0 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/preview.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/preview.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Contact Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Contact Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_contacts");
        yield "\">Contacts</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 10, $this->source); })()), "fullName", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-envelope mr-3\" style=\"font-size: 2rem;\"></i>
                        Contact Details: ";
        // line 22
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 22, $this->source); })()), "fullName", [], "any", false, false, false, 22), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back Button (Icon Only) -->
                        <a href=\"";
        // line 28
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_contacts");
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\"
                           title=\"Back to Contacts\"
                           onmouseover=\"this.style.background='rgba(255,255,255,0.3)'; this.style.transform='scale(1.05)';\"
                           onmouseout=\"this.style.background='rgba(255,255,255,0.2)'; this.style.transform='scale(1)';\">
                            <i class=\"fas fa-arrow-left\" style=\"font-size: 1.1rem;\"></i>
                        </a>

                        <!-- Print Button (Icon Only) -->
                        <button type=\"button\"
                                onclick=\"printContactDetails()\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\"
                                title=\"Print Contact Details\"
                                onmouseover=\"this.style.background='rgba(255,255,255,0.3)'; this.style.transform='scale(1.05)';\"
                                onmouseout=\"this.style.background='rgba(255,255,255,0.2)'; this.style.transform='scale(1)';\">
                            <i class=\"fas fa-print\" style=\"font-size: 1.1rem;\"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Full Name and Email (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-user text-primary mr-1\"></i>
                                        Full Name
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 66
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 66, $this->source); })()), "fullName", [], "any", false, false, false, 66), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                        Email
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 77
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 77, $this->source); })()), "email", [], "any", false, false, false, 77), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Subject and Source Page (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-8\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tag text-primary mr-1\"></i>
                                        Subject
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 92
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "subject", [], "any", true, true, false, 92) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 92, $this->source); })()), "subject", [], "any", false, false, false, 92)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 92, $this->source); })()), "subject", [], "any", false, false, false, 92), "html", null, true)) : ("No subject provided"));
        yield "
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-link text-primary mr-1\"></i>
                                        Source Page
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 103
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "sourcePage", [], "any", true, true, false, 103) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 103, $this->source); })()), "sourcePage", [], "any", false, false, false, 103)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 103, $this->source); })()), "sourcePage", [], "any", false, false, false, 103), "html", null, true)) : ("Unknown"));
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Message (Full Width) -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-comment text-primary mr-1\"></i>
                                Message
                            </label>
                            <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500; line-height: 1.6; min-height: 120px;\">
                                ";
        // line 116
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 116, $this->source); })()), "message", [], "any", false, false, false, 116)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? (Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 116, $this->source); })()), "message", [], "any", false, false, false, 116), "html", null, true))) : ("No message provided"));
        yield "
                            </div>
                        </div>

                        <!-- IP Address and Country (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-map-marker-alt text-primary mr-1\"></i>
                                        IP Address
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 129
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "ipAddress", [], "any", true, true, false, 129) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 129, $this->source); })()), "ipAddress", [], "any", false, false, false, 129)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 129, $this->source); })()), "ipAddress", [], "any", false, false, false, 129), "html", null, true)) : ("Not recorded"));
        yield "
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-globe text-primary mr-1\"></i>
                                        Country
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 140
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "country", [], "any", true, true, false, 140) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 140, $this->source); })()), "country", [], "any", false, false, false, 140)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 140, $this->source); })()), "country", [], "any", false, false, false, 140), "html", null, true)) : ("Not specified"));
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status and Created Date (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 155
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 155, $this->source); })()), "processed", [], "any", false, false, false, 155)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 156
            yield "                                            <span class=\"badge bg-success\">Processed</span>
                                        ";
        } else {
            // line 158
            yield "                                            <span class=\"badge bg-warning\">Pending</span>
                                        ";
        }
        // line 160
        yield "                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        ";
        // line 170
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 170, $this->source); })()), "createdAt", [], "any", false, false, false, 170), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 183
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 184
        yield "<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Print Styles */
@media print {
    .btn, .breadcrumb, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #011a2d !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }

    .enhanced-display-field {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 222
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 223
        yield "<script>
// Print function
function printContactDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/contacts/preview.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  450 => 223,  437 => 222,  390 => 184,  377 => 183,  354 => 170,  342 => 160,  338 => 158,  334 => 156,  332 => 155,  314 => 140,  300 => 129,  284 => 116,  268 => 103,  254 => 92,  236 => 77,  222 => 66,  181 => 28,  172 => 22,  162 => 14,  149 => 13,  136 => 10,  132 => 9,  127 => 8,  114 => 7,  91 => 5,  68 => 3,  45 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Contact Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Contact Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_contacts') }}\">Contacts</a></li>
<li class=\"breadcrumb-item active\">{{ contact.fullName }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-envelope mr-3\" style=\"font-size: 2rem;\"></i>
                        Contact Details: {{ contact.fullName }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back Button (Icon Only) -->
                        <a href=\"{{ path('admin_contacts') }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\"
                           title=\"Back to Contacts\"
                           onmouseover=\"this.style.background='rgba(255,255,255,0.3)'; this.style.transform='scale(1.05)';\"
                           onmouseout=\"this.style.background='rgba(255,255,255,0.2)'; this.style.transform='scale(1)';\">
                            <i class=\"fas fa-arrow-left\" style=\"font-size: 1.1rem;\"></i>
                        </a>

                        <!-- Print Button (Icon Only) -->
                        <button type=\"button\"
                                onclick=\"printContactDetails()\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\"
                                title=\"Print Contact Details\"
                                onmouseover=\"this.style.background='rgba(255,255,255,0.3)'; this.style.transform='scale(1.05)';\"
                                onmouseout=\"this.style.background='rgba(255,255,255,0.2)'; this.style.transform='scale(1)';\">
                            <i class=\"fas fa-print\" style=\"font-size: 1.1rem;\"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Full Name and Email (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-user text-primary mr-1\"></i>
                                        Full Name
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {{ contact.fullName }}
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-envelope text-primary mr-1\"></i>
                                        Email
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {{ contact.email }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Subject and Source Page (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-8\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tag text-primary mr-1\"></i>
                                        Subject
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {{ contact.subject ?? 'No subject provided' }}
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-link text-primary mr-1\"></i>
                                        Source Page
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {{ contact.sourcePage ?? 'Unknown' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Message (Full Width) -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-comment text-primary mr-1\"></i>
                                Message
                            </label>
                            <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500; line-height: 1.6; min-height: 120px;\">
                                {{ contact.message ? contact.message|nl2br : 'No message provided' }}
                            </div>
                        </div>

                        <!-- IP Address and Country (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-map-marker-alt text-primary mr-1\"></i>
                                        IP Address
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {{ contact.ipAddress ?? 'Not recorded' }}
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-globe text-primary mr-1\"></i>
                                        Country
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {{ contact.country ?? 'Not specified' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status and Created Date (Same Line) -->
                        <div class=\"row\">
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {% if contact.processed %}
                                            <span class=\"badge bg-success\">Processed</span>
                                        {% else %}
                                            <span class=\"badge bg-warning\">Pending</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;\">
                                        {{ contact.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Print Styles */
@media print {
    .btn, .breadcrumb, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #011a2d !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }

    .enhanced-display-field {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function
function printContactDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/contacts/preview.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\contacts\\preview.html.twig");
    }
}
