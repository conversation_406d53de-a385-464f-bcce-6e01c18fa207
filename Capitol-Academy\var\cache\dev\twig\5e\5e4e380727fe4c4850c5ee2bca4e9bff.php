<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* payment/success.html.twig */
class __TwigTemplate_5e27c1b69e0689d6918f97a24ff6e86d extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "payment/success.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "payment/success.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Payment Successful - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Your payment has been processed successfully. Welcome to ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 5, $this->source); })()), "title", [], "any", false, false, false, 5), "html", null, true);
        yield " at Capitol Academy.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-success: #28a745;
    --ca-white: #ffffff;
}

.success-hero {
    background: linear-gradient(135deg, var(--ca-success) 0%, #20c997 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.success-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.success-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.payment-details {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--ca-primary);
}

.detail-value {
    color: #495057;
}

.course-card {
    background: var(--ca-light-gray);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    border: 2px solid #e9ecef;
}

.course-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ca-primary);
    margin-bottom: 1rem;
}

.course-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn-primary-custom {
    background: var(--ca-primary);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-primary-custom:hover {
    background: var(--ca-accent);
    border-color: var(--ca-accent);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-secondary-custom {
    background: white;
    color: var(--ca-primary);
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-secondary-custom:hover {
    background: var(--ca-primary);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.next-steps {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.next-steps h4 {
    color: #155724;
    margin-bottom: 1rem;
}

.next-steps ul {
    color: #155724;
    margin: 0;
}

.next-steps li {
    margin-bottom: 0.5rem;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 168
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 169
        yield "<!-- Success Hero Section -->
<section class=\"success-hero\">
    <div class=\"container\">
        <div class=\"success-icon\">
            <i class=\"fas fa-check-circle\"></i>
        </div>
        <h1 class=\"success-title\">Payment Successful!</h1>
        <p class=\"success-subtitle\">Welcome to your new course at Capitol Academy</p>
    </div>
</section>

<!-- Payment Details Section -->
<section class=\"py-5\">
    <div class=\"container\">
        <div class=\"row justify-content-center\">
            <div class=\"col-lg-8\">
                <!-- Payment Details -->
                <div class=\"payment-details\">
                    <h3 class=\"text-center mb-4\" style=\"color: var(--ca-primary);\">
                        <i class=\"fas fa-receipt mr-2\"></i>
                        Payment Details
                    </h3>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Course:</span>
                        <span class=\"detail-value\">";
        // line 194
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 194, $this->source); })()), "title", [], "any", false, false, false, 194), "html", null, true);
        yield "</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Course Code:</span>
                        <span class=\"detail-value\">";
        // line 199
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 199, $this->source); })()), "code", [], "any", false, false, false, 199), "html", null, true);
        yield "</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Amount Paid:</span>
                        <span class=\"detail-value\">\$";
        // line 204
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["payment"]) || array_key_exists("payment", $context) ? $context["payment"] : (function () { throw new RuntimeError('Variable "payment" does not exist.', 204, $this->source); })()), "amount", [], "any", false, false, false, 204), 2), "html", null, true);
        yield " ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::upper($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["payment"]) || array_key_exists("payment", $context) ? $context["payment"] : (function () { throw new RuntimeError('Variable "payment" does not exist.', 204, $this->source); })()), "currency", [], "any", false, false, false, 204)), "html", null, true);
        yield "</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Payment ID:</span>
                        <span class=\"detail-value\">";
        // line 209
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["payment"]) || array_key_exists("payment", $context) ? $context["payment"] : (function () { throw new RuntimeError('Variable "payment" does not exist.', 209, $this->source); })()), "stripePaymentId", [], "any", false, false, false, 209), "html", null, true);
        yield "</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Date:</span>
                        <span class=\"detail-value\">";
        // line 214
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["payment"]) || array_key_exists("payment", $context) ? $context["payment"] : (function () { throw new RuntimeError('Variable "payment" does not exist.', 214, $this->source); })()), "createdAt", [], "any", false, false, false, 214), "F j, Y g:i A"), "html", null, true);
        yield "</span>
                    </div>
                </div>

                <!-- Course Information -->
                <div class=\"course-card\">
                    <h3 class=\"course-title\">";
        // line 220
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 220, $this->source); })()), "title", [], "any", false, false, false, 220), "html", null, true);
        yield "</h3>
                    ";
        // line 221
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 221, $this->source); })()), "description", [], "any", false, false, false, 221)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 222
            yield "                        <p class=\"course-description\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 222, $this->source); })()), "description", [], "any", false, false, false, 222), 0, 200), "html", null, true);
            yield "...</p>
                    ";
        }
        // line 224
        yield "                    
                    <div class=\"next-steps\">
                        <h4><i class=\"fas fa-list-check mr-2\"></i>What's Next?</h4>
                        <ul>
                            <li>You now have full access to all course materials</li>
                            <li>Start with the first module and progress at your own pace</li>
                            <li>Access your course anytime from your dashboard</li>
                            <li>Contact support if you need any assistance</li>
                        </ul>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class=\"action-buttons\">
                    <a href=\"";
        // line 238
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("course_show", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 238, $this->source); })()), "code", [], "any", false, false, false, 238)]), "html", null, true);
        yield "\" class=\"btn-primary-custom\">
                        <i class=\"fas fa-play mr-2\"></i>
                        Start Learning
                    </a>
                    
                    <a href=\"";
        // line 243
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\" class=\"btn-secondary-custom\">
                        <i class=\"fas fa-home mr-2\"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "payment/success.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  416 => 243,  408 => 238,  392 => 224,  386 => 222,  384 => 221,  380 => 220,  371 => 214,  363 => 209,  353 => 204,  345 => 199,  337 => 194,  310 => 169,  297 => 168,  127 => 8,  114 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Payment Successful - Capitol Academy{% endblock %}

{% block meta_description %}Your payment has been processed successfully. Welcome to {{ course.title }} at Capitol Academy.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-success: #28a745;
    --ca-white: #ffffff;
}

.success-hero {
    background: linear-gradient(135deg, var(--ca-success) 0%, #20c997 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.success-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.success-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.payment-details {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--ca-primary);
}

.detail-value {
    color: #495057;
}

.course-card {
    background: var(--ca-light-gray);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    border: 2px solid #e9ecef;
}

.course-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ca-primary);
    margin-bottom: 1rem;
}

.course-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn-primary-custom {
    background: var(--ca-primary);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-primary-custom:hover {
    background: var(--ca-accent);
    border-color: var(--ca-accent);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-secondary-custom {
    background: white;
    color: var(--ca-primary);
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-secondary-custom:hover {
    background: var(--ca-primary);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.next-steps {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.next-steps h4 {
    color: #155724;
    margin-bottom: 1rem;
}

.next-steps ul {
    color: #155724;
    margin: 0;
}

.next-steps li {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block body %}
<!-- Success Hero Section -->
<section class=\"success-hero\">
    <div class=\"container\">
        <div class=\"success-icon\">
            <i class=\"fas fa-check-circle\"></i>
        </div>
        <h1 class=\"success-title\">Payment Successful!</h1>
        <p class=\"success-subtitle\">Welcome to your new course at Capitol Academy</p>
    </div>
</section>

<!-- Payment Details Section -->
<section class=\"py-5\">
    <div class=\"container\">
        <div class=\"row justify-content-center\">
            <div class=\"col-lg-8\">
                <!-- Payment Details -->
                <div class=\"payment-details\">
                    <h3 class=\"text-center mb-4\" style=\"color: var(--ca-primary);\">
                        <i class=\"fas fa-receipt mr-2\"></i>
                        Payment Details
                    </h3>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Course:</span>
                        <span class=\"detail-value\">{{ course.title }}</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Course Code:</span>
                        <span class=\"detail-value\">{{ course.code }}</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Amount Paid:</span>
                        <span class=\"detail-value\">\${{ payment.amount|number_format(2) }} {{ payment.currency|upper }}</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Payment ID:</span>
                        <span class=\"detail-value\">{{ payment.stripePaymentId }}</span>
                    </div>
                    
                    <div class=\"detail-row\">
                        <span class=\"detail-label\">Date:</span>
                        <span class=\"detail-value\">{{ payment.createdAt|date('F j, Y g:i A') }}</span>
                    </div>
                </div>

                <!-- Course Information -->
                <div class=\"course-card\">
                    <h3 class=\"course-title\">{{ course.title }}</h3>
                    {% if course.description %}
                        <p class=\"course-description\">{{ course.description|slice(0, 200) }}...</p>
                    {% endif %}
                    
                    <div class=\"next-steps\">
                        <h4><i class=\"fas fa-list-check mr-2\"></i>What's Next?</h4>
                        <ul>
                            <li>You now have full access to all course materials</li>
                            <li>Start with the first module and progress at your own pace</li>
                            <li>Access your course anytime from your dashboard</li>
                            <li>Contact support if you need any assistance</li>
                        </ul>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class=\"action-buttons\">
                    <a href=\"{{ path('course_show', {'code': course.code}) }}\" class=\"btn-primary-custom\">
                        <i class=\"fas fa-play mr-2\"></i>
                        Start Learning
                    </a>
                    
                    <a href=\"{{ path('app_home') }}\" class=\"btn-secondary-custom\">
                        <i class=\"fas fa-home mr-2\"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
", "payment/success.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\payment\\success.html.twig");
    }
}
