<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/promotional_banners/edit.html.twig */
class __TwigTemplate_18beafd7b32d6831d84594c50a377e95 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/promotional_banners/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/promotional_banners/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Promotional Banner - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Promotional Banner";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_promotional_banners");
        yield "\">Promotional Banners</a></li>
<li class=\"breadcrumb-item active\">Edit: ";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-bullhorn mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Promotional Banner: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 37, $this->source); })()), "title", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Banners Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_promotional_banners");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Banners
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken(("promotional_banner_edit_" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 57, $this->source); })()), "id", [], "any", false, false, false, 57))), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Banner Title -->
                            <div class=\"form-group\">
                                <label for=\"title\" class=\"form-label\">
                                    <i class=\"fas fa-bullhorn text-primary mr-1\"></i>
                                    Banner Title <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"text\"
                                       class=\"form-control enhanced-field\"
                                       id=\"title\"
                                       name=\"title\"
                                       value=\"";
        // line 73
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 73, $this->source); })()), "title", [], "any", false, false, false, 73), "html", null, true);
        yield "\"
                                       placeholder=\"e.g., Cyber Friday Sale - Save up to 25%\"
                                       required
                                       maxlength=\"255\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <div class=\"invalid-feedback\">
                                    Please provide a banner title.
                                </div>
                            </div>

                            <!-- Banner Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Banner Description
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"3\"
                                          placeholder=\"e.g., Join our Analysis Group Today & Save Over \$150\"
                                          maxlength=\"500\"
                                          style=\"border: 2px solid #ced4da;\">";
        // line 95
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 95, $this->source); })()), "description", [], "any", false, false, false, 95), "html", null, true);
        yield "</textarea>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Brief description that appears alongside the title.
                                </small>
                            </div>

                            <!-- End Date and Background Color Row -->
                            <div class=\"row\">
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"endDate\" class=\"form-label\">
                                            <i class=\"fas fa-calendar-times text-primary mr-1\"></i>
                                            End Date/Time
                                        </label>
                                        <input type=\"datetime-local\"
                                               class=\"form-control enhanced-field datetime-input\"
                                               id=\"endDate\"
                                               name=\"endDate\"
                                               value=\"";
        // line 113
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 113, $this->source); })()), "endDate", [], "any", false, false, false, 113)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 113, $this->source); })()), "endDate", [], "any", false, false, false, 113), "Y-m-d\\TH:i"), "html", null, true)) : (""));
        yield "\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background: #ffffff;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            When the banner should stop being displayed (optional).
                                        </small>
                                    </div>
                                </div>
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"backgroundColor\" class=\"form-label\">
                                            <i class=\"fas fa-palette text-primary mr-1\"></i>
                                            Background Color
                                        </label>
                                        <input type=\"color\"
                                               class=\"form-control enhanced-field\"
                                               id=\"backgroundColor\"
                                               name=\"backgroundColor\"
                                               value=\"";
        // line 130
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["banner"] ?? null), "backgroundColor", [], "any", true, true, false, 130)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 130, $this->source); })()), "backgroundColor", [], "any", false, false, false, 130), "#001427")) : ("#001427")), "html", null, true);
        yield "\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Choose the banner background color.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Preview Section -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-eye text-primary mr-1\"></i>
                                    Live Preview
                                </label>
                                <div id=\"banner-preview\" class=\"promotional-banner-preview border rounded\" style=\"background-color: ";
        // line 145
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["banner"] ?? null), "backgroundColor", [], "any", true, true, false, 145)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 145, $this->source); })()), "backgroundColor", [], "any", false, false, false, 145), "#001427")) : ("#001427")), "html", null, true);
        yield "; color: white; padding: 15px; margin-bottom: 1rem;\">
                                    <div class=\"container-fluid\">
                                        <div class=\"row align-items-center\">
                                            <!-- Banner Content -->
                                            <div class=\"col-md-8\">
                                                <div class=\"promo-text\">
                                                    <h6 class=\"mb-1 text-white fw-bold\" id=\"preview-title\">";
        // line 151
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 151, $this->source); })()), "title", [], "any", false, false, false, 151), "html", null, true);
        yield "</h6>
                                                    <p class=\"mb-0 text-light small\" id=\"preview-description\">";
        // line 152
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 152, $this->source); })()), "description", [], "any", false, false, false, 152), "html", null, true);
        yield "</p>
                                                </div>
                                            </div>

                                            <!-- Call to Action -->
                                            <div class=\"col-md-4 text-end\">
                                                <div class=\"d-flex align-items-center justify-content-end gap-2\">
                                                    <a href=\"#\" class=\"btn btn-sm fw-bold px-3 py-2\"
                                                       style=\"background-color: #28a745; border-color: #28a745; color: white; border-radius: 25px; text-decoration: none; transition: all 0.3s ease;\">
                                                        Join Now
                                                    </a>
                                                    <button class=\"btn btn-sm btn-outline-light\" type=\"button\"
                                                            style=\"border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\">
                                                        <i class=\"fas fa-times\" style=\"font-size: 0.75rem;\"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>





                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Banner
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 190
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_promotional_banners");
        yield "\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 203
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 204
        yield "<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form field focus effects
    \$('.form-control, .enhanced-field').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Live preview updates
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const backgroundColorInput = document.getElementById('backgroundColor');

    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const bannerPreview = document.getElementById('banner-preview');

    // Update title preview
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            previewTitle.textContent = this.value || '";
        // line 242
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 242, $this->source); })()), "title", [], "any", false, false, false, 242), "html", null, true);
        yield "';
        });
    }

    // Update description preview
    if (descriptionInput) {
        descriptionInput.addEventListener('input', function() {
            previewDescription.textContent = this.value || '";
        // line 249
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["banner"]) || array_key_exists("banner", $context) ? $context["banner"] : (function () { throw new RuntimeError('Variable "banner" does not exist.', 249, $this->source); })()), "description", [], "any", false, false, false, 249), "html", null, true);
        yield "';
            previewDescription.style.display = this.value ? 'block' : 'block';
        });
    }

    // Update background color preview
    if (backgroundColorInput) {
        backgroundColorInput.addEventListener('input', function() {
            bannerPreview.style.backgroundColor = this.value;
        });
    }


});
</script>

<style>
.form-group.focused .form-label {
    color: #011a2d;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #011a2d !important;
    transition: all 0.3s ease;
}

.enhanced-field:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Capitol Academy Datetime Picker Styling */
input[type=\"datetime-local\"] {
    position: relative;
    background: white;
    border: 2px solid #ced4da;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    color: #343a40;
    transition: all 0.3s ease;
    height: calc(1.6em + 1.25rem + 4px);
}

input[type=\"datetime-local\"]:hover {
    border-color: #011a2d !important;
    box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
    background-color: #f8f9fa;
}

input[type=\"datetime-local\"]:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    outline: none;
    background-color: white;
    transform: translateY(-1px);
}

/* Webkit datetime picker calendar icon styling */
input[type=\"datetime-local\"]::-webkit-calendar-picker-indicator {
    background-color: #011a2d;
    border-radius: 4px;
    cursor: pointer;
    padding: 4px;
    transition: all 0.3s ease;
}

input[type=\"datetime-local\"]::-webkit-calendar-picker-indicator:hover {
    background-color: #011a2d;
    transform: scale(1.1);
}

/* Firefox datetime picker styling */
input[type=\"datetime-local\"]::-moz-datetime-picker {
    background-color: white;
    border: 1px solid #011a2d;
    border-radius: 8px;
}

/* Custom calendar popup styling for webkit browsers */
input[type=\"datetime-local\"]::-webkit-datetime-edit {
    color: #343a40;
    font-weight: 500;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-fields-wrapper {
    background: transparent;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-text {
    color: #6c757d;
    padding: 0 0.25rem;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-month-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-day-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-year-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-hour-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-minute-field {
    background: transparent;
    color: #343a40;
    font-weight: 500;
    padding: 0 0.125rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-month-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-day-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-year-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-hour-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-minute-field:focus {
    background-color: rgba(1, 26, 45, 0.1);
    color: #011a2d;
    outline: none;
}

/* Banner Preview Styling */
.promotional-banner-preview {
    transition: all 0.3s ease;
}

.promotional-banner-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.countdown-timer {
    font-family: 'Courier New', monospace;
}

.countdown-item {
    min-width: 45px;
}

.countdown-number {
    font-size: 1.2rem;
    line-height: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
    margin-bottom: 2px;
}

.countdown-label {
    font-size: 0.7rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.countdown-separator {
    font-size: 1.2rem;
    font-weight: bold;
    opacity: 0.7;
    margin: 0 5px;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}

/* Fix datetime input calendar icon styling */
.datetime-input::-webkit-calendar-picker-indicator {
    background: transparent !important;
    color: #6c757d !important;
    cursor: pointer !important;
    border: none !important;
    padding: 4px !important;
    border-radius: 4px !important;
}

.datetime-input::-webkit-calendar-picker-indicator:hover {
    background: rgba(108, 117, 125, 0.1) !important;
}

/* Firefox datetime input styling */
.datetime-input::-moz-calendar-picker-indicator {
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/promotional_banners/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  481 => 249,  471 => 242,  431 => 204,  418 => 203,  395 => 190,  354 => 152,  350 => 151,  341 => 145,  323 => 130,  303 => 113,  282 => 95,  257 => 73,  238 => 57,  221 => 43,  212 => 37,  202 => 29,  192 => 25,  189 => 24,  185 => 23,  182 => 22,  172 => 18,  169 => 17,  165 => 16,  161 => 14,  148 => 13,  135 => 10,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Promotional Banner - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Promotional Banner{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_promotional_banners') }}\">Promotional Banners</a></li>
<li class=\"breadcrumb-item active\">Edit: {{ banner.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-bullhorn mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Promotional Banner: {{ banner.title }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Banners Button -->
                        <a href=\"{{ path('admin_promotional_banners') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Banners
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('promotional_banner_edit_' ~ banner.id) }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Banner Title -->
                            <div class=\"form-group\">
                                <label for=\"title\" class=\"form-label\">
                                    <i class=\"fas fa-bullhorn text-primary mr-1\"></i>
                                    Banner Title <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"text\"
                                       class=\"form-control enhanced-field\"
                                       id=\"title\"
                                       name=\"title\"
                                       value=\"{{ banner.title }}\"
                                       placeholder=\"e.g., Cyber Friday Sale - Save up to 25%\"
                                       required
                                       maxlength=\"255\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <div class=\"invalid-feedback\">
                                    Please provide a banner title.
                                </div>
                            </div>

                            <!-- Banner Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Banner Description
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"3\"
                                          placeholder=\"e.g., Join our Analysis Group Today & Save Over \$150\"
                                          maxlength=\"500\"
                                          style=\"border: 2px solid #ced4da;\">{{ banner.description }}</textarea>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Brief description that appears alongside the title.
                                </small>
                            </div>

                            <!-- End Date and Background Color Row -->
                            <div class=\"row\">
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"endDate\" class=\"form-label\">
                                            <i class=\"fas fa-calendar-times text-primary mr-1\"></i>
                                            End Date/Time
                                        </label>
                                        <input type=\"datetime-local\"
                                               class=\"form-control enhanced-field datetime-input\"
                                               id=\"endDate\"
                                               name=\"endDate\"
                                               value=\"{{ banner.endDate ? banner.endDate|date('Y-m-d\\\\TH:i') : '' }}\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background: #ffffff;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            When the banner should stop being displayed (optional).
                                        </small>
                                    </div>
                                </div>
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"backgroundColor\" class=\"form-label\">
                                            <i class=\"fas fa-palette text-primary mr-1\"></i>
                                            Background Color
                                        </label>
                                        <input type=\"color\"
                                               class=\"form-control enhanced-field\"
                                               id=\"backgroundColor\"
                                               name=\"backgroundColor\"
                                               value=\"{{ banner.backgroundColor|default('#001427') }}\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Choose the banner background color.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Preview Section -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-eye text-primary mr-1\"></i>
                                    Live Preview
                                </label>
                                <div id=\"banner-preview\" class=\"promotional-banner-preview border rounded\" style=\"background-color: {{ banner.backgroundColor|default('#001427') }}; color: white; padding: 15px; margin-bottom: 1rem;\">
                                    <div class=\"container-fluid\">
                                        <div class=\"row align-items-center\">
                                            <!-- Banner Content -->
                                            <div class=\"col-md-8\">
                                                <div class=\"promo-text\">
                                                    <h6 class=\"mb-1 text-white fw-bold\" id=\"preview-title\">{{ banner.title }}</h6>
                                                    <p class=\"mb-0 text-light small\" id=\"preview-description\">{{ banner.description }}</p>
                                                </div>
                                            </div>

                                            <!-- Call to Action -->
                                            <div class=\"col-md-4 text-end\">
                                                <div class=\"d-flex align-items-center justify-content-end gap-2\">
                                                    <a href=\"#\" class=\"btn btn-sm fw-bold px-3 py-2\"
                                                       style=\"background-color: #28a745; border-color: #28a745; color: white; border-radius: 25px; text-decoration: none; transition: all 0.3s ease;\">
                                                        Join Now
                                                    </a>
                                                    <button class=\"btn btn-sm btn-outline-light\" type=\"button\"
                                                            style=\"border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;\">
                                                        <i class=\"fas fa-times\" style=\"font-size: 0.75rem;\"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>





                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Update Banner
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_promotional_banners') }}\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>

{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form field focus effects
    \$('.form-control, .enhanced-field').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Live preview updates
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const backgroundColorInput = document.getElementById('backgroundColor');

    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const bannerPreview = document.getElementById('banner-preview');

    // Update title preview
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            previewTitle.textContent = this.value || '{{ banner.title }}';
        });
    }

    // Update description preview
    if (descriptionInput) {
        descriptionInput.addEventListener('input', function() {
            previewDescription.textContent = this.value || '{{ banner.description }}';
            previewDescription.style.display = this.value ? 'block' : 'block';
        });
    }

    // Update background color preview
    if (backgroundColorInput) {
        backgroundColorInput.addEventListener('input', function() {
            bannerPreview.style.backgroundColor = this.value;
        });
    }


});
</script>

<style>
.form-group.focused .form-label {
    color: #011a2d;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #011a2d !important;
    transition: all 0.3s ease;
}

.enhanced-field:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Capitol Academy Datetime Picker Styling */
input[type=\"datetime-local\"] {
    position: relative;
    background: white;
    border: 2px solid #ced4da;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    color: #343a40;
    transition: all 0.3s ease;
    height: calc(1.6em + 1.25rem + 4px);
}

input[type=\"datetime-local\"]:hover {
    border-color: #011a2d !important;
    box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
    background-color: #f8f9fa;
}

input[type=\"datetime-local\"]:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    outline: none;
    background-color: white;
    transform: translateY(-1px);
}

/* Webkit datetime picker calendar icon styling */
input[type=\"datetime-local\"]::-webkit-calendar-picker-indicator {
    background-color: #011a2d;
    border-radius: 4px;
    cursor: pointer;
    padding: 4px;
    transition: all 0.3s ease;
}

input[type=\"datetime-local\"]::-webkit-calendar-picker-indicator:hover {
    background-color: #011a2d;
    transform: scale(1.1);
}

/* Firefox datetime picker styling */
input[type=\"datetime-local\"]::-moz-datetime-picker {
    background-color: white;
    border: 1px solid #011a2d;
    border-radius: 8px;
}

/* Custom calendar popup styling for webkit browsers */
input[type=\"datetime-local\"]::-webkit-datetime-edit {
    color: #343a40;
    font-weight: 500;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-fields-wrapper {
    background: transparent;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-text {
    color: #6c757d;
    padding: 0 0.25rem;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-month-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-day-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-year-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-hour-field,
input[type=\"datetime-local\"]::-webkit-datetime-edit-minute-field {
    background: transparent;
    color: #343a40;
    font-weight: 500;
    padding: 0 0.125rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

input[type=\"datetime-local\"]::-webkit-datetime-edit-month-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-day-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-year-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-hour-field:focus,
input[type=\"datetime-local\"]::-webkit-datetime-edit-minute-field:focus {
    background-color: rgba(1, 26, 45, 0.1);
    color: #011a2d;
    outline: none;
}

/* Banner Preview Styling */
.promotional-banner-preview {
    transition: all 0.3s ease;
}

.promotional-banner-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.countdown-timer {
    font-family: 'Courier New', monospace;
}

.countdown-item {
    min-width: 45px;
}

.countdown-number {
    font-size: 1.2rem;
    line-height: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
    margin-bottom: 2px;
}

.countdown-label {
    font-size: 0.7rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.countdown-separator {
    font-size: 1.2rem;
    font-weight: bold;
    opacity: 0.7;
    margin: 0 5px;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}

/* Fix datetime input calendar icon styling */
.datetime-input::-webkit-calendar-picker-indicator {
    background: transparent !important;
    color: #6c757d !important;
    cursor: pointer !important;
    border: none !important;
    padding: 4px !important;
    border-radius: 4px !important;
}

.datetime-input::-webkit-calendar-picker-indicator:hover {
    background: rgba(108, 117, 125, 0.1) !important;
}

/* Firefox datetime input styling */
.datetime-input::-moz-calendar-picker-indicator {
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
}
</style>
{% endblock %}
", "admin/promotional_banners/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\promotional_banners\\edit.html.twig");
    }
}
