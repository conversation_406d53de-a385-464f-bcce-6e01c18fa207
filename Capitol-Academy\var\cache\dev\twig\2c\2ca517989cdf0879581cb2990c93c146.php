<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/plans/index.html.twig */
class __TwigTemplate_289c84613f679813358ffeac76b8b779 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Plans Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Plans Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Plans</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Plans Management", "page_icon" => "fas fa-layer-group", "search_placeholder" => "Search...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plan_create"), "text" => "Add New Plan", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Plans", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 25
(isset($context["plans"]) || array_key_exists("plans", $context) ? $context["plans"] : (function () { throw new RuntimeError('Variable "plans" does not exist.', 25, $this->source); })())), "icon" => "fas fa-layer-group", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 32
(isset($context["plans"]) || array_key_exists("plans", $context) ? $context["plans"] : (function () { throw new RuntimeError('Variable "plans" does not exist.', 32, $this->source); })()), function ($__plan__) use ($context, $macros) { $context["plan"] = $__plan__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 32, $this->source); })()), "isActive", [], "any", false, false, false, 32); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Inactive", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 39
(isset($context["plans"]) || array_key_exists("plans", $context) ? $context["plans"] : (function () { throw new RuntimeError('Variable "plans" does not exist.', 39, $this->source); })()), function ($__plan__) use ($context, $macros) { $context["plan"] = $__plan__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 39, $this->source); })()), "isActive", [], "any", false, false, false, 39); })), "icon" => "fas fa-pause-circle", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["plans"]) || array_key_exists("plans", $context) ? $context["plans"] : (function () { throw new RuntimeError('Variable "plans" does not exist.', 46, $this->source); })()), function ($__plan__) use ($context, $macros) { $context["plan"] = $__plan__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["plan"]) || array_key_exists("plan", $context) ? $context["plan"] : (function () { throw new RuntimeError('Variable "plan" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-clock", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/plans/index.html.twig", 54, "505002636")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        // line 116
        yield "
";
        // line 117
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        // line 192
        yield "
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 117
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 118
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.plan-row',
        ['.plan-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Plan management functions using standardized modals
function showPlanStatusModal(planCode, planTitle, isActive) {
    AdminPageUtils.showStatusModal(planCode, planTitle, isActive, function(code, newStatus) {
        togglePlanStatus(code, newStatus);
    });
}

function showPlanDeleteModal(planCode, planTitle) {
    AdminPageUtils.showDeleteModal(planCode, planTitle, deletePlan);
}

// Actual execution functions
function togglePlanStatus(planCode, newStatus) {
    fetch(`/admin/plans/\${planCode}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while updating the plan status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the plan status.');
    });
}

function deletePlan(planCode) {
    fetch(`/admin/plans/\${planCode}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the plan');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the plan.');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/plans/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  195 => 118,  182 => 117,  170 => 192,  168 => 117,  165 => 116,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Plans Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Plans Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Plans</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Plans Management',
    'page_icon': 'fas fa-layer-group',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_plan_create'),
        'text': 'Add New Plan',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Plans',
            'value': plans|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': plans|filter(plan => plan.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': plans|filter(plan => not plan.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': plans|filter(plan => plan.createdAt and plan.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Videos'},
            {'text': 'Duration'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for plan in plans %}
            {% set row_cells = [
                {
                    'content': '<code class=\"plan-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">' ~ plan.code ~ '</code>'
                },
                {
                    'content': '<h6 class=\"plan-title mb-0 font-weight-bold text-dark\">' ~ plan.title ~ '</h6>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ plan.videos|length ~ ' videos</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ plan.formattedDuration ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">\$' ~ (plan.price|default('0')) ~ '</span>'
                },
                {
                    'content': plan.isActive ?
                        '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_plan_preview', {'code': plan.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Plan\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_plan_edit', {'code': plan.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Plan\"><i class=\"fas fa-edit\"></i></a>
                        <button onclick=\"showPlanStatusModal(\\'' ~ plan.code ~ '\\', \\'' ~ plan.title ~ '\\', ' ~ (plan.isActive ? 'true' : 'false') ~ ')\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (plan.isActive ? '#ffc107' : '#28a745') ~ '; color: ' ~ (plan.isActive ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (plan.isActive ? 'Pause' : 'Activate') ~ ' Plan\"><i class=\"fas fa-' ~ (plan.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button onclick=\"showPlanDeleteModal(\\'' ~ plan.code ~ '\\', \\'' ~ plan.title ~ '\\')\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Plan\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'plan-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'plan-row',
            'empty_message': 'No plans found',
            'empty_icon': 'fas fa-layer-group',
            'empty_description': 'Get started by creating your first plan.',
            'search_config': {
                'fields': ['.plan-title']
            }
        } %}
    {% endblock %}
{% endembed %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.plan-row',
        ['.plan-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Plan management functions using standardized modals
function showPlanStatusModal(planCode, planTitle, isActive) {
    AdminPageUtils.showStatusModal(planCode, planTitle, isActive, function(code, newStatus) {
        togglePlanStatus(code, newStatus);
    });
}

function showPlanDeleteModal(planCode, planTitle) {
    AdminPageUtils.showDeleteModal(planCode, planTitle, deletePlan);
}

// Actual execution functions
function togglePlanStatus(planCode, newStatus) {
    fetch(`/admin/plans/\${planCode}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while updating the plan status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the plan status.');
    });
}

function deletePlan(planCode) {
    fetch(`/admin/plans/\${planCode}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the plan');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the plan.');
    });
}
</script>
{% endblock %}

{% endblock %}
", "admin/plans/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\plans\\index.html.twig");
    }
}


/* admin/plans/index.html.twig */
class __TwigTemplate_289c84613f679813358ffeac76b8b779___505002636 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/plans/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "
        <!-- Standardized Table -->
        ";
        // line 58
        $context["table_headers"] = [["text" => "Code"], ["text" => "Title"], ["text" => "Videos"], ["text" => "Duration"], ["text" => "Price"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 67
        yield "
        ";
        // line 68
        $context["table_rows"] = [];
        // line 69
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["plans"]) || array_key_exists("plans", $context) ? $context["plans"] : (function () { throw new RuntimeError('Variable "plans" does not exist.', 69, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["plan"]) {
            // line 70
            yield "            ";
            $context["row_cells"] = [["content" => (("<code class=\"plan-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 72
$context["plan"], "code", [], "any", false, false, false, 72)) . "</code>")], ["content" => (("<h6 class=\"plan-title mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 75
$context["plan"], "title", [], "any", false, false, false, 75)) . "</h6>")], ["content" => (("<span class=\"text-dark font-weight-medium\">" . Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["plan"], "videos", [], "any", false, false, false, 78))) . " videos</span>")], ["content" => (("<span class=\"text-dark font-weight-medium\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 81
$context["plan"], "formattedDuration", [], "any", false, false, false, 81)) . "</span>")], ["content" => (("<span class=\"text-dark font-weight-medium\">\$" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 84
$context["plan"], "price", [], "any", true, true, false, 84)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "price", [], "any", false, false, false, 84), "0")) : ("0"))) . "</span>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 87
$context["plan"], "isActive", [], "any", false, false, false, 87)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>") : ("<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>"))], ["content" => (((((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plan_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 93
$context["plan"], "code", [], "any", false, false, false, 93)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Plan\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_plan_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 94
$context["plan"], "code", [], "any", false, false, false, 94)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Plan\"><i class=\"fas fa-edit\"></i></a>
                        <button onclick=\"showPlanStatusModal('") . CoreExtension::getAttribute($this->env, $this->source,             // line 95
$context["plan"], "code", [], "any", false, false, false, 95)) . "', '") . CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "title", [], "any", false, false, false, 95)) . "', ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "isActive", [], "any", false, false, false, 95)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("true") : ("false"))) . ")\" class=\"btn btn-sm shadow-sm\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "isActive", [], "any", false, false, false, 95)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#ffc107") : ("#28a745"))) . "; color: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "isActive", [], "any", false, false, false, 95)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#212529") : ("white"))) . "; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "isActive", [], "any", false, false, false, 95)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Pause") : ("Activate"))) . " Plan\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "isActive", [], "any", false, false, false, 95)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"))) . "\"></i></button>
                        <button onclick=\"showPlanDeleteModal('") . CoreExtension::getAttribute($this->env, $this->source,             // line 96
$context["plan"], "code", [], "any", false, false, false, 96)) . "', '") . CoreExtension::getAttribute($this->env, $this->source, $context["plan"], "title", [], "any", false, false, false, 96)) . "')\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Plan\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 100
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 100, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 100, $this->source); })()), "class" => "plan-row"]]);
            // line 101
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['plan'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 102
        yield "
        ";
        // line 103
        yield from $this->load("components/admin_table.html.twig", 103)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 104
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 104, $this->source); })()), "rows" =>         // line 105
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 105, $this->source); })()), "row_class" => "plan-row", "empty_message" => "No plans found", "empty_icon" => "fas fa-layer-group", "empty_description" => "Get started by creating your first plan.", "search_config" => ["fields" => [".plan-title"]]]));
        // line 114
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/plans/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  611 => 114,  609 => 105,  608 => 104,  607 => 103,  604 => 102,  598 => 101,  595 => 100,  592 => 96,  590 => 95,  588 => 94,  586 => 93,  584 => 87,  583 => 84,  582 => 81,  581 => 78,  580 => 75,  579 => 72,  577 => 70,  572 => 69,  570 => 68,  567 => 67,  565 => 58,  561 => 56,  548 => 55,  525 => 54,  195 => 118,  182 => 117,  170 => 192,  168 => 117,  165 => 116,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Plans Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Plans Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Plans</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Plans Management',
    'page_icon': 'fas fa-layer-group',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_plan_create'),
        'text': 'Add New Plan',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Plans',
            'value': plans|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': plans|filter(plan => plan.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': plans|filter(plan => not plan.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': plans|filter(plan => plan.createdAt and plan.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Videos'},
            {'text': 'Duration'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for plan in plans %}
            {% set row_cells = [
                {
                    'content': '<code class=\"plan-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">' ~ plan.code ~ '</code>'
                },
                {
                    'content': '<h6 class=\"plan-title mb-0 font-weight-bold text-dark\">' ~ plan.title ~ '</h6>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ plan.videos|length ~ ' videos</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ plan.formattedDuration ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">\$' ~ (plan.price|default('0')) ~ '</span>'
                },
                {
                    'content': plan.isActive ?
                        '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_plan_preview', {'code': plan.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Plan\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_plan_edit', {'code': plan.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Plan\"><i class=\"fas fa-edit\"></i></a>
                        <button onclick=\"showPlanStatusModal(\\'' ~ plan.code ~ '\\', \\'' ~ plan.title ~ '\\', ' ~ (plan.isActive ? 'true' : 'false') ~ ')\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (plan.isActive ? '#ffc107' : '#28a745') ~ '; color: ' ~ (plan.isActive ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (plan.isActive ? 'Pause' : 'Activate') ~ ' Plan\"><i class=\"fas fa-' ~ (plan.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button onclick=\"showPlanDeleteModal(\\'' ~ plan.code ~ '\\', \\'' ~ plan.title ~ '\\')\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Plan\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'plan-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'plan-row',
            'empty_message': 'No plans found',
            'empty_icon': 'fas fa-layer-group',
            'empty_description': 'Get started by creating your first plan.',
            'search_config': {
                'fields': ['.plan-title']
            }
        } %}
    {% endblock %}
{% endembed %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.plan-row',
        ['.plan-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Plan management functions using standardized modals
function showPlanStatusModal(planCode, planTitle, isActive) {
    AdminPageUtils.showStatusModal(planCode, planTitle, isActive, function(code, newStatus) {
        togglePlanStatus(code, newStatus);
    });
}

function showPlanDeleteModal(planCode, planTitle) {
    AdminPageUtils.showDeleteModal(planCode, planTitle, deletePlan);
}

// Actual execution functions
function togglePlanStatus(planCode, newStatus) {
    fetch(`/admin/plans/\${planCode}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while updating the plan status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the plan status.');
    });
}

function deletePlan(planCode) {
    fetch(`/admin/plans/\${planCode}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the plan');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the plan.');
    });
}
</script>
{% endblock %}

{% endblock %}
", "admin/plans/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\plans\\index.html.twig");
    }
}
