<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/profile/settings.html.twig */
class __TwigTemplate_345cced82ea820dc66df18fafdc23139 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/profile/settings.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/profile/settings.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Profile Settings - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Profile Settings";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Dashboard</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_profile");
        yield "\">Profile</a></li>
<li class=\"breadcrumb-item active\">Settings</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid p-0\">
    <!-- Professional Settings Header -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border-radius: 8px;\">
                <div class=\"card-body p-4\">
                    <div class=\"d-flex align-items-center justify-content-between\">
                        <div>
                            <h2 class=\"mb-1 font-weight-bold\">Profile Settings</h2>
                            <p class=\"mb-0\" style=\"opacity: 0.9;\">Manage your admin account settings and preferences</p>
                        </div>
                        <div>
                            <a href=\"";
        // line 26
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_profile");
        yield "\" class=\"btn btn-light\">
                                <i class=\"fas fa-arrow-left me-2\"></i>Back to Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Profile Information -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-user-circle me-2 text-primary\"></i>
                        Current Profile Information
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"d-flex align-items-center\">
                        <div class=\"flex-shrink-0 me-4\">
                            <img src=\"";
        // line 49
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 49, $this->source); })()), "profileImageUrl", [], "any", false, false, false, 49), "html", null, true);
        yield "\"
                                 alt=\"Admin Profile\"
                                 id=\"current-profile-image\"
                                 class=\"rounded-circle shadow\"
                                 style=\"width: 80px; height: 80px; object-fit: cover; border: 3px solid #1e3c72;\">
                        </div>
                        <div class=\"flex-grow-1\">
                            <h4 class=\"mb-1 text-primary\">";
        // line 56
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 56, $this->source); })()), "fullName", [], "any", false, false, false, 56), "html", null, true);
        yield "</h4>
                            <p class=\"mb-1 text-muted\">";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 57, $this->source); })()), "email", [], "any", false, false, false, 57), "html", null, true);
        yield "</p>
                            <p class=\"mb-0 text-muted\">
                                <i class=\"fas fa-user-tag me-1\"></i>";
        // line 59
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 59, $this->source); })()), "username", [], "any", false, false, false, 59), "html", null, true);
        yield "
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Image Upload Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-camera me-2 text-success\"></i>
                        Profile Image
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <form method=\"post\" enctype=\"multipart/form-data\">
                        <input type=\"hidden\" name=\"action\" value=\"upload_profile_image\">
                        
                        <div class=\"row\">
                            <div class=\"col-md-8\">
                                <div class=\"mb-3\">
                                    <label for=\"profile_image\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-upload me-1\"></i>
                                        Upload New Profile Image
                                    </label>
                                    <input type=\"file\" 
                                           class=\"form-control\" 
                                           id=\"profile_image\" 
                                           name=\"profile_image\" 
                                           accept=\"image/jpeg,image/png,image/jpg\"
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"form-text text-muted\">
                                        <i class=\"fas fa-info-circle me-1\"></i>
                                        JPEG/PNG only, max 2MB. Recommended: 400x400px square image.
                                    </div>
                                </div>
                                
                                <div class=\"image-preview mt-3\" id=\"image-preview\" style=\"display: none;\">
                                    <label class=\"form-label font-weight-medium\">Preview:</label>
                                    <div>
                                        <img src=\"\" alt=\"Preview\" class=\"rounded-circle shadow\" style=\"width: 100px; height: 100px; object-fit: cover; border: 3px solid #1e3c72;\">
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"d-flex justify-content-center\">
                                    <button type=\"submit\" class=\"btn btn-success btn-lg\">
                                        <i class=\"fas fa-save me-2\"></i>
                                        Update Image
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-lock me-2 text-warning\"></i>
                        Change Password
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <form method=\"post\" class=\"needs-validation\" novalidate>
                        <input type=\"hidden\" name=\"action\" value=\"change_password\">
                        
                        <div class=\"row g-4\">
                            <div class=\"col-md-4\">
                                <div class=\"mb-3\">
                                    <label for=\"current_password\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-key me-1\"></i>
                                        Current Password <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"password\" 
                                           class=\"form-control\" 
                                           id=\"current_password\" 
                                           name=\"current_password\" 
                                           required
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"invalid-feedback\">
                                        Please enter your current password.
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"mb-3\">
                                    <label for=\"new_password\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-lock me-1\"></i>
                                        New Password <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"password\" 
                                           class=\"form-control\" 
                                           id=\"new_password\" 
                                           name=\"new_password\" 
                                           minlength=\"6\"
                                           required
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"invalid-feedback\">
                                        Password must be at least 6 characters long.
                                    </div>
                                    <div class=\"form-text text-muted\">
                                        <i class=\"fas fa-info-circle me-1\"></i>
                                        Password must be at least 6 characters long.
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"mb-3\">
                                    <label for=\"confirm_password\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-lock me-1\"></i>
                                        Confirm New Password <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"password\" 
                                           class=\"form-control\" 
                                           id=\"confirm_password\" 
                                           name=\"confirm_password\" 
                                           minlength=\"6\"
                                           required
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"invalid-feedback\">
                                        Please confirm your new password.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class=\"row\">
                            <div class=\"col-12\">
                                <div class=\"d-flex justify-content-center mt-3\">
                                    <button type=\"submit\" class=\"btn btn-warning btn-lg\">
                                        <i class=\"fas fa-save me-2\"></i>
                                        Change Password
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Security Section -->
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-shield-alt me-2 text-danger\"></i>
                        Account Security
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"row g-4\">
                        <div class=\"col-md-4\">
                            <div class=\"security-item\">
                                <h6 class=\"text-muted mb-2\">Account Status</h6>
                                <p class=\"mb-0\">
                                    ";
        // line 229
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 229, $this->source); })()), "isActive", [], "any", false, false, false, 229)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 230
            yield "                                        <span class=\"badge bg-success px-3 py-2\">
                                            <i class=\"fas fa-check-circle me-1\"></i>Active & Secure
                                        </span>
                                    ";
        } else {
            // line 234
            yield "                                        <span class=\"badge bg-danger px-3 py-2\">
                                            <i class=\"fas fa-exclamation-triangle me-1\"></i>Inactive
                                        </span>
                                    ";
        }
        // line 238
        yield "                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"security-item\">
                                <h6 class=\"text-muted mb-2\">Last Login</h6>
                                <p class=\"mb-0 font-weight-medium\">
                                    ";
        // line 245
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 245, $this->source); })()), "lastLoginAt", [], "any", false, false, false, 245)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 246
            yield "                                        ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 246, $this->source); })()), "lastLoginAt", [], "any", false, false, false, 246), "M j, Y g:i A"), "html", null, true);
            yield "
                                    ";
        } else {
            // line 248
            yield "                                        <span class=\"text-muted\">Never</span>
                                    ";
        }
        // line 250
        yield "                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"security-item\">
                                <h6 class=\"text-muted mb-2\">IP Address</h6>
                                <p class=\"mb-0\">
                                    ";
        // line 257
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 257, $this->source); })()), "ipAddress", [], "any", false, false, false, 257)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 258
            yield "                                        <code class=\"bg-light text-dark px-2 py-1 rounded\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["admin"]) || array_key_exists("admin", $context) ? $context["admin"] : (function () { throw new RuntimeError('Variable "admin" does not exist.', 258, $this->source); })()), "ipAddress", [], "any", false, false, false, 258), "html", null, true);
            yield "</code>
                                    ";
        } else {
            // line 260
            yield "                                        <span class=\"text-muted\">Not recorded</span>
                                    ";
        }
        // line 262
        yield "                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 273
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 274
        yield "<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Password confirmation validation
    \$('#confirm_password').on('input', function() {
        var newPassword = \$('#new_password').val();
        var confirmPassword = \$(this).val();
        
        if (newPassword !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Image preview functionality
    \$('#profile_image').on('change', function() {
        previewImage(this, '#image-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
                
                // Also update the current profile image preview
                \$('#current-profile-image').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/profile/settings.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  479 => 274,  466 => 273,  446 => 262,  442 => 260,  436 => 258,  434 => 257,  425 => 250,  421 => 248,  415 => 246,  413 => 245,  404 => 238,  398 => 234,  392 => 230,  390 => 229,  217 => 59,  212 => 57,  208 => 56,  198 => 49,  172 => 26,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Profile Settings - Capitol Academy Admin{% endblock %}

{% block page_title %}Profile Settings{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Dashboard</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_profile') }}\">Profile</a></li>
<li class=\"breadcrumb-item active\">Settings</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid p-0\">
    <!-- Professional Settings Header -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border-radius: 8px;\">
                <div class=\"card-body p-4\">
                    <div class=\"d-flex align-items-center justify-content-between\">
                        <div>
                            <h2 class=\"mb-1 font-weight-bold\">Profile Settings</h2>
                            <p class=\"mb-0\" style=\"opacity: 0.9;\">Manage your admin account settings and preferences</p>
                        </div>
                        <div>
                            <a href=\"{{ path('admin_profile') }}\" class=\"btn btn-light\">
                                <i class=\"fas fa-arrow-left me-2\"></i>Back to Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Profile Information -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-user-circle me-2 text-primary\"></i>
                        Current Profile Information
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"d-flex align-items-center\">
                        <div class=\"flex-shrink-0 me-4\">
                            <img src=\"{{ admin.profileImageUrl }}\"
                                 alt=\"Admin Profile\"
                                 id=\"current-profile-image\"
                                 class=\"rounded-circle shadow\"
                                 style=\"width: 80px; height: 80px; object-fit: cover; border: 3px solid #1e3c72;\">
                        </div>
                        <div class=\"flex-grow-1\">
                            <h4 class=\"mb-1 text-primary\">{{ admin.fullName }}</h4>
                            <p class=\"mb-1 text-muted\">{{ admin.email }}</p>
                            <p class=\"mb-0 text-muted\">
                                <i class=\"fas fa-user-tag me-1\"></i>{{ admin.username }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Image Upload Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-camera me-2 text-success\"></i>
                        Profile Image
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <form method=\"post\" enctype=\"multipart/form-data\">
                        <input type=\"hidden\" name=\"action\" value=\"upload_profile_image\">
                        
                        <div class=\"row\">
                            <div class=\"col-md-8\">
                                <div class=\"mb-3\">
                                    <label for=\"profile_image\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-upload me-1\"></i>
                                        Upload New Profile Image
                                    </label>
                                    <input type=\"file\" 
                                           class=\"form-control\" 
                                           id=\"profile_image\" 
                                           name=\"profile_image\" 
                                           accept=\"image/jpeg,image/png,image/jpg\"
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"form-text text-muted\">
                                        <i class=\"fas fa-info-circle me-1\"></i>
                                        JPEG/PNG only, max 2MB. Recommended: 400x400px square image.
                                    </div>
                                </div>
                                
                                <div class=\"image-preview mt-3\" id=\"image-preview\" style=\"display: none;\">
                                    <label class=\"form-label font-weight-medium\">Preview:</label>
                                    <div>
                                        <img src=\"\" alt=\"Preview\" class=\"rounded-circle shadow\" style=\"width: 100px; height: 100px; object-fit: cover; border: 3px solid #1e3c72;\">
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"d-flex justify-content-center\">
                                    <button type=\"submit\" class=\"btn btn-success btn-lg\">
                                        <i class=\"fas fa-save me-2\"></i>
                                        Update Image
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Section -->
    <div class=\"row mb-4\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-lock me-2 text-warning\"></i>
                        Change Password
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <form method=\"post\" class=\"needs-validation\" novalidate>
                        <input type=\"hidden\" name=\"action\" value=\"change_password\">
                        
                        <div class=\"row g-4\">
                            <div class=\"col-md-4\">
                                <div class=\"mb-3\">
                                    <label for=\"current_password\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-key me-1\"></i>
                                        Current Password <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"password\" 
                                           class=\"form-control\" 
                                           id=\"current_password\" 
                                           name=\"current_password\" 
                                           required
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"invalid-feedback\">
                                        Please enter your current password.
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"mb-3\">
                                    <label for=\"new_password\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-lock me-1\"></i>
                                        New Password <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"password\" 
                                           class=\"form-control\" 
                                           id=\"new_password\" 
                                           name=\"new_password\" 
                                           minlength=\"6\"
                                           required
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"invalid-feedback\">
                                        Password must be at least 6 characters long.
                                    </div>
                                    <div class=\"form-text text-muted\">
                                        <i class=\"fas fa-info-circle me-1\"></i>
                                        Password must be at least 6 characters long.
                                    </div>
                                </div>
                            </div>
                            <div class=\"col-md-4\">
                                <div class=\"mb-3\">
                                    <label for=\"confirm_password\" class=\"form-label font-weight-medium\">
                                        <i class=\"fas fa-lock me-1\"></i>
                                        Confirm New Password <span class=\"text-danger\">*</span>
                                    </label>
                                    <input type=\"password\" 
                                           class=\"form-control\" 
                                           id=\"confirm_password\" 
                                           name=\"confirm_password\" 
                                           minlength=\"6\"
                                           required
                                           style=\"border: 2px solid #1e3c72; border-radius: 8px;\">
                                    <div class=\"invalid-feedback\">
                                        Please confirm your new password.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class=\"row\">
                            <div class=\"col-12\">
                                <div class=\"d-flex justify-content-center mt-3\">
                                    <button type=\"submit\" class=\"btn btn-warning btn-lg\">
                                        <i class=\"fas fa-save me-2\"></i>
                                        Change Password
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Security Section -->
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">
                <div class=\"card-header\" style=\"background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;\">
                    <h5 class=\"mb-0 text-dark\">
                        <i class=\"fas fa-shield-alt me-2 text-danger\"></i>
                        Account Security
                    </h5>
                </div>
                <div class=\"card-body p-4\">
                    <div class=\"row g-4\">
                        <div class=\"col-md-4\">
                            <div class=\"security-item\">
                                <h6 class=\"text-muted mb-2\">Account Status</h6>
                                <p class=\"mb-0\">
                                    {% if admin.isActive %}
                                        <span class=\"badge bg-success px-3 py-2\">
                                            <i class=\"fas fa-check-circle me-1\"></i>Active & Secure
                                        </span>
                                    {% else %}
                                        <span class=\"badge bg-danger px-3 py-2\">
                                            <i class=\"fas fa-exclamation-triangle me-1\"></i>Inactive
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"security-item\">
                                <h6 class=\"text-muted mb-2\">Last Login</h6>
                                <p class=\"mb-0 font-weight-medium\">
                                    {% if admin.lastLoginAt %}
                                        {{ admin.lastLoginAt|date('M j, Y g:i A') }}
                                    {% else %}
                                        <span class=\"text-muted\">Never</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"security-item\">
                                <h6 class=\"text-muted mb-2\">IP Address</h6>
                                <p class=\"mb-0\">
                                    {% if admin.ipAddress %}
                                        <code class=\"bg-light text-dark px-2 py-1 rounded\">{{ admin.ipAddress }}</code>
                                    {% else %}
                                        <span class=\"text-muted\">Not recorded</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Password confirmation validation
    \$('#confirm_password').on('input', function() {
        var newPassword = \$('#new_password').val();
        var confirmPassword = \$(this).val();
        
        if (newPassword !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Image preview functionality
    \$('#profile_image').on('change', function() {
        previewImage(this, '#image-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
                
                // Also update the current profile image preview
                \$('#current-profile-image').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }
});
</script>
{% endblock %}
", "admin/profile/settings.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\profile\\settings.html.twig");
    }
}
